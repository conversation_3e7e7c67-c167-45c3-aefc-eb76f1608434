{"version": 3, "file": "forum.js", "mappings": ";2BAAA,IAAIA,EAAoBC,EAAQ,KAChC,SAASC,IAEP,IAAIC,EACFC,EACAC,EAAI,mBAAqBC,OAASA,OAAS,CAAC,EAC5CC,EAAIF,EAAEG,UAAY,aAClBC,EAAIJ,EAAEK,aAAe,gBACvB,SAASC,EAAEN,EAAGE,EAAGE,EAAGE,GAClB,IAAIC,EAAIL,GAAKA,EAAEM,qBAAqBC,EAAYP,EAAIO,EAClDC,EAAIC,OAAOC,OAAOL,EAAEC,WACtB,OAAOb,EAAkBe,EAAG,UAAW,SAAUV,EAAGE,EAAGE,GACrD,IAAIE,EACFC,EACAG,EACAG,EAAI,EACJC,EAAIV,GAAK,GACTW,GAAI,EACJC,EAAI,CACFF,EAAG,EACHZ,EAAG,EACHe,EAAGnB,EACHoB,EAAGC,EACHN,EAAGM,EAAEC,KAAKtB,EAAG,GACbqB,EAAG,SAAWpB,EAAGC,GACf,OAAOM,EAAIP,EAAGQ,EAAI,EAAGG,EAAIZ,EAAGkB,EAAEd,EAAIF,EAAGkB,CACvC,GAEJ,SAASC,EAAEnB,EAAGE,GACZ,IAAKK,EAAIP,EAAGU,EAAIR,EAAGH,EAAI,GAAIgB,GAAKF,IAAMT,GAAKL,EAAIe,EAAEO,OAAQtB,IAAK,CAC5D,IAAIK,EACFE,EAAIQ,EAAEf,GACNoB,EAAIH,EAAEF,EACNQ,EAAIhB,EAAE,GACRN,EAAI,GAAKI,EAAIkB,IAAMpB,KAAOQ,EAAIJ,GAAGC,EAAID,EAAE,IAAM,GAAKC,EAAI,EAAG,IAAKD,EAAE,GAAKA,EAAE,GAAKR,GAAKQ,EAAE,IAAMa,KAAOf,EAAIJ,EAAI,GAAKmB,EAAIb,EAAE,KAAOC,EAAI,EAAGS,EAAEC,EAAIf,EAAGc,EAAEd,EAAII,EAAE,IAAMa,EAAIG,IAAMlB,EAAIJ,EAAI,GAAKM,EAAE,GAAKJ,GAAKA,EAAIoB,KAAOhB,EAAE,GAAKN,EAAGM,EAAE,GAAKJ,EAAGc,EAAEd,EAAIoB,EAAGf,EAAI,GACzO,CACA,GAAIH,GAAKJ,EAAI,EAAG,OAAOkB,EACvB,MAAMH,GAAI,EAAIb,CAChB,CACA,OAAO,SAAUE,EAAGU,EAAGQ,GACrB,GAAIT,EAAI,EAAG,MAAMU,UAAU,gCAC3B,IAAKR,GAAK,IAAMD,GAAKK,EAAEL,EAAGQ,GAAIf,EAAIO,EAAGJ,EAAIY,GAAIvB,EAAIQ,EAAI,EAAIT,EAAIY,KAAOK,GAAI,CACtET,IAAMC,EAAIA,EAAI,GAAKA,EAAI,IAAMS,EAAEd,GAAK,GAAIiB,EAAEZ,EAAGG,IAAMM,EAAEd,EAAIQ,EAAIM,EAAEC,EAAIP,GACnE,IACE,GAAIG,EAAI,EAAGP,EAAG,CACZ,GAAIC,IAAMH,EAAI,QAASL,EAAIO,EAAEF,GAAI,CAC/B,KAAML,EAAIA,EAAEyB,KAAKlB,EAAGI,IAAK,MAAMa,UAAU,oCACzC,IAAKxB,EAAE0B,KAAM,OAAO1B,EACpBW,EAAIX,EAAE2B,MAAOnB,EAAI,IAAMA,EAAI,EAC7B,MAAO,IAAMA,IAAMR,EAAIO,EAAU,SAAMP,EAAEyB,KAAKlB,GAAIC,EAAI,IAAMG,EAAIa,UAAU,oCAAsCnB,EAAI,YAAaG,EAAI,GACrID,EAAIR,CACN,MAAO,IAAKC,GAAKgB,EAAIC,EAAEd,EAAI,GAAKQ,EAAIV,EAAEwB,KAAKtB,EAAGc,MAAQE,EAAG,KAC3D,CAAE,MAAOnB,GACPO,EAAIR,EAAGS,EAAI,EAAGG,EAAIX,CACpB,CAAE,QACAc,EAAI,CACN,CACF,CACA,MAAO,CACLa,MAAO3B,EACP0B,KAAMV,EAEV,CACF,CApDuC,CAoDrCf,EAAGI,EAAGE,IAAI,GAAKI,CACnB,CACA,IAAIQ,EAAI,CAAC,EACT,SAAST,IAAa,CACtB,SAASkB,IAAqB,CAC9B,SAASC,IAA8B,CACvC7B,EAAIY,OAAOkB,eACX,IAAItB,EAAI,GAAGL,GAAKH,EAAEA,EAAE,GAAGG,QAAUP,EAAkBI,EAAI,CAAC,EAAGG,EAAG,WAC1D,OAAO4B,IACT,GAAI/B,GACJW,EAAIkB,EAA2BpB,UAAYC,EAAUD,UAAYG,OAAOC,OAAOL,GACjF,SAASM,EAAEf,GACT,OAAOa,OAAOoB,eAAiBpB,OAAOoB,eAAejC,EAAG8B,IAA+B9B,EAAEkC,UAAYJ,EAA4BjC,EAAkBG,EAAGM,EAAG,sBAAuBN,EAAEU,UAAYG,OAAOC,OAAOF,GAAIZ,CAClN,CACA,OAAO6B,EAAkBnB,UAAYoB,EAA4BjC,EAAkBe,EAAG,cAAekB,GAA6BjC,EAAkBiC,EAA4B,cAAeD,GAAoBA,EAAkBM,YAAc,oBAAqBtC,EAAkBiC,EAA4BxB,EAAG,qBAAsBT,EAAkBe,GAAIf,EAAkBe,EAAGN,EAAG,aAAcT,EAAkBe,EAAGR,EAAG,WACja,OAAO4B,IACT,GAAInC,EAAkBe,EAAG,WAAY,WACnC,MAAO,oBACT,IAAKwB,EAAOC,QAAUtC,EAAe,WACnC,MAAO,CACLuC,EAAG9B,EACH+B,EAEJ,EAAGH,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,UAC1E,CACAD,EAAOC,QAAUtC,EAAcqC,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,gBCxFpG,IAAII,EAAc3C,EAAQ,KACtB4C,EAA2B5C,EAAQ,KAIvCsC,EAAOC,QAHP,SAA8BnC,EAAGF,EAAGC,EAAGK,EAAGF,GACxC,OAAO,IAAIsC,EAAyBD,IAAcH,EAAEpC,EAAGF,EAAGC,EAAGK,GAAIF,GAAKuC,QACxE,EACuCP,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,UCI5GD,EAAOC,QATP,SAA0BrC,GACxB,IAAII,EAAIS,OAAOb,GACbE,EAAI,GACN,IAAK,IAAID,KAAKG,EAAGF,EAAE0C,QAAQ3C,GAC3B,OAAO,SAASD,IACd,KAAOE,EAAEqB,QAAS,IAAKtB,EAAIC,EAAE2C,SAAUzC,EAAG,OAAOJ,EAAE4B,MAAQ3B,EAAGD,EAAE2B,MAAO,EAAI3B,EAC3E,OAAOA,EAAE2B,MAAO,EAAI3B,CACtB,CACF,EACmCoC,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,UCNxGD,EAAOC,QAHP,SAAwBrC,EAAGqB,GACzBW,KAAKb,EAAInB,EAAGgC,KAAKc,EAAIzB,CACvB,EACiCe,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,gBCDtG,IAAIU,EAAUjD,EAAQ,IAARA,GACdsC,EAAOC,QAAUU,EAGjB,IACEC,mBAAqBD,CACvB,CAAE,MAAOE,GACmB,iBAAfC,WACTA,WAAWF,mBAAqBD,EAEhCI,SAAS,IAAK,yBAAdA,CAAwCJ,EAE5C,C,gBCdA,IAAIK,EAAgBtD,EAAQ,KACxBD,EAAoBC,EAAQ,KA+BhCsC,EAAOC,QA9BP,SAASgB,EAAcpD,EAAGD,GACxB,SAASI,EAAEF,EAAGI,EAAGE,EAAGO,GAClB,IACE,IAAIN,EAAIR,EAAEC,GAAGI,GACXM,EAAIH,EAAEmB,MACR,OAAOhB,aAAawC,EAAgBpD,EAAEsD,QAAQ1C,EAAEO,GAAGoC,KAAK,SAAUtD,GAChEG,EAAE,OAAQH,EAAGO,EAAGO,EAClB,EAAG,SAAUd,GACXG,EAAE,QAASH,EAAGO,EAAGO,EACnB,GAAKf,EAAEsD,QAAQ1C,GAAG2C,KAAK,SAAUtD,GAC/BQ,EAAEmB,MAAQ3B,EAAGO,EAAEC,EACjB,EAAG,SAAUR,GACX,OAAOG,EAAE,QAASH,EAAGO,EAAGO,EAC1B,EACF,CAAE,MAAOd,GACPc,EAAEd,EACJ,CACF,CACA,IAAIC,EACJ8B,KAAKwB,OAAS3D,EAAkBwD,EAAc3C,WAAYb,EAAkBwD,EAAc3C,UAAW,mBAAqBP,QAAUA,OAAOsD,eAAiB,iBAAkB,WAC5K,OAAOzB,IACT,IAAKnC,EAAkBmC,KAAM,UAAW,SAAU/B,EAAGK,EAAGE,GACtD,SAASO,IACP,OAAO,IAAIf,EAAE,SAAUA,EAAGE,GACxBE,EAAEH,EAAGO,EAAGR,EAAGE,EACb,EACF,CACA,OAAOA,EAAIA,EAAIA,EAAEqD,KAAKxC,EAAGA,GAAKA,GAChC,GAAG,EACL,EACgCqB,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,gBChCrG,IAAIqB,EAAsB5D,EAAQ,KAOlCsC,EAAOC,QANP,SAA2BjC,EAAGJ,EAAGE,EAAGD,EAAGK,GACrC,IAAIc,EAAIsC,EAAoBtD,EAAGJ,EAAGE,EAAGD,EAAGK,GACxC,OAAOc,EAAEoC,OAAOD,KAAK,SAAUnD,GAC7B,OAAOA,EAAEuB,KAAOvB,EAAEwB,MAAQR,EAAEoC,MAC9B,EACF,EACoCpB,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,gBCPzG,IAAIe,EAAgBtD,EAAQ,KACxB2C,EAAc3C,EAAQ,KACtB6D,EAAmB7D,EAAQ,KAC3B4D,EAAsB5D,EAAQ,KAC9B4C,EAA2B5C,EAAQ,KACnC8D,EAAkB9D,EAAQ,KAC1B+D,EAAoB/D,EAAQ,KAChC,SAASgE,IACP,aAEA,IAAI5D,EAAIuC,IACNzC,EAAIE,EAAEqC,EAAEuB,GACR7D,GAAKY,OAAOkB,eAAiBlB,OAAOkB,eAAe/B,GAAKA,EAAEkC,WAAW6B,YACvE,SAAS3D,EAAEF,GACT,IAAIF,EAAI,mBAAqBE,GAAKA,EAAE6D,YACpC,QAAS/D,IAAMA,IAAMC,GAAK,uBAAyBD,EAAEmC,aAAenC,EAAEgE,MACxE,CACA,IAAI1D,EAAI,CACN,MAAS,EACT,OAAU,EACV,MAAS,EACT,SAAY,GAEd,SAASc,EAAElB,GACT,IAAIF,EAAGC,EACP,OAAO,SAAUG,GACfJ,IAAMA,EAAI,CACRiE,KAAM,WACJ,OAAOhE,EAAEG,EAAEgB,EAAG,EAChB,EACA,MAAS,WACP,OAAOhB,EAAEe,CACX,EACA+C,OAAQ,SAAgBhE,EAAGF,GACzB,OAAOC,EAAEG,EAAEgB,EAAGd,EAAEJ,GAAIF,EACtB,EACAmE,cAAe,SAAuBjE,EAAGI,EAAGc,GAC1C,OAAOpB,EAAEoE,WAAa9D,EAAGL,EAAEG,EAAEiB,EAAGwC,EAAkB3D,GAAIkB,EACxD,EACAiD,OAAQ,SAAgBnE,GACtB,OAAOD,EAAEG,EAAEW,EAAGb,EAChB,GACCD,EAAI,SAAWC,EAAGoE,EAAIhE,GACvBF,EAAEY,EAAIhB,EAAEuE,KAAMnE,EAAEA,EAAIJ,EAAEwD,KACtB,IACE,OAAOtD,EAAEoE,EAAIhE,EACf,CAAE,QACAN,EAAEwD,KAAOpD,EAAEA,CACb,CACF,GAAIJ,EAAEoE,aAAepE,EAAEA,EAAEoE,YAAchE,EAAEe,EAAGnB,EAAEoE,gBAAa,GAASpE,EAAEwE,KAAOpE,EAAEe,EAAGnB,EAAEwD,KAAOpD,EAAEA,EAC7F,IACE,OAAOF,EAAEwB,KAAKM,KAAMhC,EACtB,CAAE,QACAI,EAAEY,EAAIhB,EAAEuE,KAAMnE,EAAEA,EAAIJ,EAAEwD,IACxB,CACF,CACF,CACA,OAAQpB,EAAOC,QAAUyB,EAAsB,WAC7C,MAAO,CACLW,KAAM,SAAczE,EAAGC,EAAGG,EAAGE,GAC3B,OAAOJ,EAAEoC,EAAElB,EAAEpB,GAAIC,EAAGG,EAAGE,GAAKA,EAAEoE,UAChC,EACAC,oBAAqBvE,EACrBwE,KAAM1E,EAAEqC,EACRsC,MAAO,SAAe3E,EAAGF,GACvB,OAAO,IAAIoD,EAAclD,EAAGF,EAC9B,EACAqD,cAAeX,EACfoC,MAAO,SAAe5E,EAAGF,EAAGC,EAAGK,EAAGM,GAChC,OAAQR,EAAEJ,GAAK0D,EAAsBC,GAAkBvC,EAAElB,GAAIF,EAAGC,EAAGK,EAAGM,EACxE,EACAmE,KAAMnB,EACNoB,OAAQnB,EAEZ,EAAGzB,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,UAC1E,CACAD,EAAOC,QAAUyB,EAAqB1B,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,UC5E3G,SAAS4C,EAAQ3E,GAGf,OAAO8B,EAAOC,QAAU4C,EAAU,mBAAqB9E,QAAU,iBAAmBA,OAAOE,SAAW,SAAUC,GAC9G,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAK,mBAAqBH,QAAUG,EAAEyD,cAAgB5D,QAAUG,IAAMH,OAAOO,UAAY,gBAAkBJ,CACpH,EAAG8B,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,QAAS4C,EAAQ3E,EAC3F,CACA8B,EAAOC,QAAU4C,EAAS7C,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,UCT/F,SAAS6C,EAAmBlF,EAAGE,EAAGE,EAAGH,GACnC,IAAIO,EAAIK,OAAOsE,eACf,IACE3E,EAAE,CAAC,EAAG,GAAI,CAAC,EACb,CAAE,MAAOR,GACPQ,EAAI,CACN,CACA4B,EAAOC,QAAU6C,EAAqB,SAA2BlF,EAAGE,EAAGE,EAAGH,GACxE,SAASK,EAAEJ,EAAGE,GACZ8E,EAAmBlF,EAAGE,EAAG,SAAUF,GACjC,OAAOgC,KAAKoD,QAAQlF,EAAGE,EAAGJ,EAC5B,EACF,CACAE,EAAIM,EAAIA,EAAER,EAAGE,EAAG,CACd0B,MAAOxB,EACPiF,YAAapF,EACbqF,cAAerF,EACfsF,UAAWtF,IACRD,EAAEE,GAAKE,GAAKE,EAAE,OAAQ,GAAIA,EAAE,QAAS,GAAIA,EAAE,SAAU,GAC5D,EAAG8B,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,QAAS6C,EAAmBlF,EAAGE,EAAGE,EAAGH,EAC/G,CACAmC,EAAOC,QAAU6C,EAAoB9C,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,gBCrB1G,IAAI4C,EAAUnF,EAAAA,KAAAA,QAkBdsC,EAAOC,QAjBP,SAA4BrC,GAC1B,GAAI,MAAQA,EAAG,CACb,IAAIC,EAAID,EAAE,mBAAqBG,QAAUA,OAAOE,UAAY,cAC1DH,EAAI,EACN,GAAID,EAAG,OAAOA,EAAEyB,KAAK1B,GACrB,GAAI,mBAAqBA,EAAEwD,KAAM,OAAOxD,EACxC,IAAKwF,MAAMxF,EAAEuB,QAAS,MAAO,CAC3BiC,KAAM,WACJ,OAAOxD,GAAKE,GAAKF,EAAEuB,SAAWvB,OAAI,GAAS,CACzC4B,MAAO5B,GAAKA,EAAEE,KACdyB,MAAO3B,EAEX,EAEJ,CACA,MAAM,IAAIyB,UAAUwD,EAAQjF,GAAK,mBACnC,EACqCoC,EAAOC,QAAQG,YAAa,EAAMJ,EAAOC,QAAiB,QAAID,EAAOC,O,GCjBtGoD,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAavD,QAGrB,IAAID,EAASqD,EAAyBE,GAAY,CAGjDtD,QAAS,CAAC,GAOX,OAHAyD,EAAoBH,GAAUvD,EAAQA,EAAOC,QAASqD,GAG/CtD,EAAOC,OACf,CCrBAqD,EAAoBtF,EAAKgC,IACxB,IAAI2D,EAAS3D,GAAUA,EAAOI,WAC7B,IAAOJ,EAAiB,QACxB,IAAM,EAEP,OADAsD,EAAoBrE,EAAE0E,EAAQ,CAAE3E,EAAG2E,IAC5BA,GCLRL,EAAoBrE,EAAI,CAACgB,EAAS2D,KACjC,IAAI,IAAIC,KAAOD,EACXN,EAAoBpF,EAAE0F,EAAYC,KAASP,EAAoBpF,EAAE+B,EAAS4D,IAC5EpF,OAAOsE,eAAe9C,EAAS4D,EAAK,CAAEZ,YAAY,EAAMa,IAAKF,EAAWC,MCJ3EP,EAAoBpF,EAAI,CAAC6F,EAAKC,IAAUvF,OAAOH,UAAU2F,eAAe3E,KAAKyE,EAAKC,G,mBCAlF,SAASE,EAAmBlG,EAAGH,EAAGD,EAAGE,EAAGI,EAAGc,EAAGX,GAC5C,IACE,IAAID,EAAIJ,EAAEgB,GAAGX,GACXG,EAAIJ,EAAEoB,KACV,CAAE,MAAOxB,GACP,YAAYJ,EAAEI,EAChB,CACAI,EAAEmB,KAAO1B,EAAEW,GAAK+B,QAAQW,QAAQ1C,GAAG2C,KAAKrD,EAAGI,EAC7C,CACA,SAASiG,EAAkBnG,GACzB,OAAO,WACL,IAAIH,EAAI+B,KACNhC,EAAIwG,UACN,OAAO,IAAI7D,QAAQ,SAAUzC,EAAGI,GAC9B,IAAIc,EAAIhB,EAAEqG,MAAMxG,EAAGD,GACnB,SAAS0G,EAAMtG,GACbkG,EAAmBlF,EAAGlB,EAAGI,EAAGoG,EAAOC,EAAQ,OAAQvG,EACrD,CACA,SAASuG,EAAOvG,GACdkG,EAAmBlF,EAAGlB,EAAGI,EAAGoG,EAAOC,EAAQ,QAASvG,EACtD,CACAsG,OAAM,EACR,EACF,CACF,C,sBCxBA,MAAM,EAA+BE,OAAOC,KAAKC,OAAO,a,aCAxD,SAASC,IACP,OAAOA,EAAWlG,OAAOmG,OAASnG,OAAOmG,OAAO1F,OAAS,SAAUlB,GACjE,IAAK,IAAIJ,EAAI,EAAGA,EAAIwG,UAAUjF,OAAQvB,IAAK,CACzC,IAAIC,EAAIuG,UAAUxG,GAClB,IAAK,IAAIE,KAAKD,GAAG,CAAG,GAAEoG,eAAe3E,KAAKzB,EAAGC,KAAOE,EAAEF,GAAKD,EAAEC,GAC/D,CACA,OAAOE,CACT,EAAG2G,EAASN,MAAM,KAAMD,UAC1B,CCRA,MAAM,EAA+BI,OAAOC,KAAKC,OAAO,iBCAlD,EAA+BF,OAAOC,KAAKC,OAAO,kC,aCAxD,SAASG,EAAgBhH,EAAGD,GAC1B,OAAOiH,EAAkBpG,OAAOoB,eAAiBpB,OAAOoB,eAAeX,OAAS,SAAUrB,EAAGD,GAC3F,OAAOC,EAAEiC,UAAYlC,EAAGC,CAC1B,EAAGgH,EAAgBhH,EAAGD,EACxB,CCHA,SAASkH,EAAejH,EAAGK,GACzBL,EAAES,UAAYG,OAAOC,OAAOR,EAAEI,WAAYT,EAAES,UAAUqD,YAAc9D,EAAGgC,EAAehC,EAAGK,EAC3F,CCUA,IAGe6G,EAAe,oBAAAA,IAAA,KAChBC,SAAmB,EAAK,KACxBC,KAAmB,IAAI,KAAAC,EAAAH,EAAAzG,UAaR,OAbQ4G,EAE1BC,UAAP,WACI,OAAOvF,KAAKoF,OAChB,EAACE,EAEME,QAAP,WACI,OAAOxF,KAAKqF,IAChB,EAACC,EAKeG,UAAS,eAAAC,EAAAnB,EAAAzC,IAAAA,KAAzB,SAAA6D,EAA0BC,GAAgB,IAAAC,EAAAvD,EAAA,OAAAR,IAAAA,KAAA,SAAAgE,GAAA,cAAAA,EAAAvD,KAAAuD,EAAAtE,MAAA,WAClCxB,KAAKoF,QAAS,CAAFU,EAAAtE,KAAA,eAAAsE,EAAA5D,OAAA,SACLvB,QAAQW,QAAQ,KAAG,OAGV,OAApBtB,KAAKoF,SAAU,EAAKU,EAAAvD,KAAA,EAAAuD,EAAAtE,KAAA,EAGMuE,IAAAA,MAAUC,KAAKJ,GAAS,OACP,OADjCK,EAAOH,EAAAtD,KACbxC,KAAKqF,KAAOrF,KAAKkG,aAAaD,GAASH,EAAA5D,OAAA,SAChClC,KAAKqF,MAAI,OAGD,OAHCS,EAAAvD,KAAA,EAAAD,EAAAwD,EAAA,SAEhBK,QAAQC,MAAM,2BAA2BR,EAAQ,IAAAtD,GACjDtC,KAAKqF,KAAO,GAAGS,EAAA5D,OAAA,SACRlC,KAAKqF,MAAI,OAEK,OAFLS,EAAAvD,KAAA,EAEhBvC,KAAKoF,SAAU,EAAMU,EAAAzD,OAAA,2BAAAyD,EAAA7D,OAAA,EAAA0D,EAAA,qBAhBJ,OAkBxB,SAlBwBU,GAAA,OAAAX,EAAAjB,MAAC,KAADD,UAAA,KAAAW,CAAA,CAfC,GAuCjBmB,EAAe,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAA9B,MAAA,KAAAD,YAAA,KAAAU,EAAAoB,EAAAC,GAAA,IAAAC,EAAAF,EAAA5H,UAWvB,OAXuB8H,EACjBC,KAAP,WACI,OAAOzG,KAAKyF,UAAU,mBAC1B,EAACe,EAEMN,aAAP,SAAoBD,GAChB,IAAMS,EAAuB,GAI7B,OAHIT,GACA,GAAGU,KAAKlC,MAAMiC,EAAMT,GAEjBS,CACX,EAACJ,CAAA,CAXuB,CAASnB,GAiBxByB,EAA2B,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAApC,MAAA,KAAAD,YAAA,KAAAU,EAAA0B,EAAAC,GAAA,IAAAC,EAAAF,EAAAlI,UAWnC,OAXmCoI,EAC7BL,KAAP,WACI,OAAOzG,KAAKyF,UAAU,2BAC1B,EAACqB,EAEMZ,aAAP,SAAoBD,GAChB,IAAMS,EAAmC,GAIzC,OAHIT,GACA,GAAGU,KAAKlC,MAAMiC,EAAMT,GAEjBS,CACX,EAACE,CAAA,CAXmC,CAASzB,GAiBpC4B,EAAiB,SAAAC,GAAA,SAAAD,IAAA,QAAAE,EAAAC,EAAA1C,UAAAjF,OAAA4H,EAAA,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAAF,EAAAE,GAAA7C,UAAA6C,GACC,OADDJ,EAAAD,EAAAtH,KAAA+E,MAAAuC,EAAA,OAAAM,OAAAH,KAAA,MAClBI,QAAkB,EAACN,CAAA,CAAA/B,EAAA6B,EAAAC,GAAA,IAAAQ,EAAAT,EAAArI,UA2C1B,OA3C0B8I,EAEpBf,KAAP,WACI,OAAOzG,KAAKyF,UAAU,iBAC1B,EAAC+B,EAEMtB,aAAP,SAAoBD,GAChB,IAAMS,EAAyB,GAI/B,OAHIT,GACA,GAAGU,KAAKlC,MAAMiC,EAAMT,GAEjBS,CACX,EAACc,EAEMC,WAAP,WACI,OAAOzH,KAAKuH,OAChB,EAACC,EAEME,WAAP,SAAkB9H,GACdI,KAAKuH,QAAU3H,CACnB,EAAC4H,EAEMG,iBAAP,WACI3H,KAAKuH,SACT,EAACC,EAEMI,iBAAP,WACI5H,KAAKuH,SACT,EAACC,EAEMK,eAAP,WACI,OAAI7H,KAAKqF,MAAQrF,KAAKqF,KAAKrF,KAAKuH,SACrBvH,KAAKqF,KAAKrF,KAAKuH,SAEnB,IACX,EAACC,EAEMM,QAAP,WACI,OAAqB,OAAd9H,KAAKqF,WAAiDxB,IAAhC7D,KAAKqF,KAAKrF,KAAKuH,QAAU,EAC1D,EAACC,EAEMO,YAAP,WACI,OAAqB,OAAd/H,KAAKqF,WAAiDxB,IAAhC7D,KAAKqF,KAAKrF,KAAKuH,QAAU,EAC1D,EAACR,CAAA,CA5CyB,CAAS5B,GAkD1B6C,EAAW,WAOpB,SAAAA,IAAsB,KAJdC,qBAAe,OACfC,iCAA2B,OAC3BC,uBAAiB,EAGrBnI,KAAKiI,gBAAkB,IAAI3B,EAC3BtG,KAAKkI,4BAA8B,IAAItB,EACvC5G,KAAKmI,kBAAoB,IAAIpB,CACjC,CAACiB,EAEaI,YAAd,WAII,OAHKJ,EAAYK,WACbL,EAAYK,SAAW,IAAIL,GAExBA,EAAYK,QACvB,EAAC,IAAAC,EAAAN,EAAAtJ,UA2CA,OA3CA4J,EAEMC,mBAAP,WACI,OAAOvI,KAAKiI,eAChB,EAACK,EAEME,+BAAP,WACI,OAAOxI,KAAKkI,2BAChB,EAACI,EAEMG,qBAAP,WACI,OAAOzI,KAAKmI,iBAChB,EAEAG,EAGaI,YAAW,eAAAC,EAAApE,EAAAzC,IAAAA,KAAxB,SAAA8G,IAAA,IAAAC,EAAA,OAAA/G,IAAAA,KAAA,SAAAgH,GAAA,cAAAA,EAAAvG,KAAAuG,EAAAtH,MAAA,OAK8C,OAJpCqH,EAAW,CACb7I,KAAKiI,gBAAgBxB,OACrBzG,KAAKkI,4BAA4BzB,OACjCzG,KAAKmI,kBAAkB1B,QACzBsC,OAAO,SAAAC,GAAO,YAAgBnF,IAAZmF,CAAqB,GAACF,EAAAtH,KAAA,EAEpCb,QAAQsI,IAAIJ,GAAS,wBAAAC,EAAA7G,OAAA,EAAA2G,EAAA,SAPP,OAQvB,WARuB,OAAAD,EAAAlE,MAAC,KAADD,UAAA,KAUxB8D,EAGOY,gBAAP,WACI,OAA0C,OAAnClJ,KAAKiI,gBAAgBzC,WAC0B,OAA/CxF,KAAKkI,4BAA4B1C,WACI,OAArCxF,KAAKmI,kBAAkB3C,SAClC,EAEA8C,EAGOa,iBAAP,WACI,OAAOnJ,KAAKiI,gBAAgB1C,aACrBvF,KAAKkI,4BAA4B3C,aACjCvF,KAAKmI,kBAAkB5C,WAClC,EAACyC,CAAA,CA7DmB,GAAXA,EACMK,cAAQ,ECvIpB,IAAMe,EAAS,aAATA,EAEcC,WAAa,GAF3BD,EAGcE,wBAA0B,IAHxCF,EAIcG,oBAAsB,IAJpCH,EAKcI,oBAAsB,IALpCJ,EAQcK,qBAAuB,GARrCL,EAScM,sBAAwB,GATtCN,EAUcO,uBAAyB,EAVvCP,EAWcQ,wBAA0B,EAXxCR,EAYcS,uBAAyB,EAZvCT,EAacU,wBAA0B,EAbxCV,EAccW,sBAAwB,GAdtCX,EAiBcY,iBAAmB,gCAjBjCZ,EAkBca,mBAAqB,qBAlBnCb,EAqBcc,qBAAuB,EArBrCd,EAsBce,qBAAuB,GAtBrCf,EAyBcgB,UAAY,CAC/BC,IAAK,OACLC,eAAgB,kBAChBC,YAAa,eACbC,kBAAmB,sBACnBC,kBAAmB,oBACnBC,UAAW,YACXC,SAAU,WACVC,oBAAqB,sBACrBC,SAAU,YACVC,uBAAwB,yBACxBC,mBAAoB,sBACpBC,iBAAkB,oBArCb5B,EAyCc6B,cAAgB,CACnCC,qBAAsB,qBACtBC,oBAAqB,oBACrBC,wBAAyB,wBACzBC,uBAAwB,uBACxBC,YAAa,8BACbC,aAAc,cACdC,qBAAsB,qBACtBC,2BAA4B,2BAC5BC,oBAAqB,oBACrBC,iBAAkB,iBAClBC,iBAAkB,kBApDbxC,EAwDcyC,YAAc,CACjCX,qBAAsB,qBACtBC,oBAAqB,oBACrBW,WAAY,mBACZC,UAAW,kBACXC,gBAAiB,wBACjBC,gBAAiB,iBACjBC,gBAAiB,iBACjBC,qBAAsB,sBACtBC,+BAAgC,+BAChCf,uBAAwB,uBACxBgB,MAAO,QACPC,iBAAkB,gCAClBC,uBAAwB,yBACxBC,8BAA+B,iCAOhC,IAAMC,EAAc,oBAAAA,IAAA,CA2CtB,OA1CDA,EAGcC,kBAAd,WACI,IAAMC,EAAiB5G,IAAAA,MAAU6G,UAAU,kCAC3C,OAAOD,EAAiBE,OAAOF,GAAkBvD,EAAUE,uBAC/D,EAEAmD,EAGcK,YAAd,SAA0BC,GACtB,OAAOhH,IAAAA,MAAU6G,UAAU,wBAAwBG,IAAY,IACnE,EAEAN,EAGcO,aAAd,SAA2BD,GACvB,OAAOhH,IAAAA,MAAU6G,UAAU,uBAAuBG,IAAY,IAClE,EAEAN,EAGcQ,kBAAd,WAGI,IAFA,IAAMC,EAA6D,GAE1D1O,EAAI,EAAGA,GAAK4K,EAAUe,qBAAsB3L,IAAK,CACtD,IAAM2O,EAAMnN,KAAK8M,YAAYtO,GACvB4O,EAAOpN,KAAKgN,aAAaxO,GAE3B2O,GACAD,EAAMvG,KAAK,CACPwG,IAAAA,EACAC,KAAMA,GAAQ,IACdL,MAAOvO,GAGnB,CAEA,OAAO0O,CACX,EAACT,CAAA,CA3CsB,GAiDdY,EAAY,oBAAAA,IAAA,CA6CpB,OA1CDA,EAGcC,SAAd,WAII,OAHuB,OAAnBtN,KAAKuN,YACLvN,KAAKuN,UAAYvN,KAAKwN,eAEnBxN,KAAKuN,SAChB,EAEAF,EAGeG,YAAf,WACI,IAAIC,GAAQ,EACNC,EAAYC,UAAUD,WAAaC,UAAUC,QAAWC,OAAeC,MAS7E,OAPIJ,IAIAD,EAHoB,2TAGAM,KAAKL,IAFJ,0kDAE+BK,KAAKL,EAAUM,OAAO,EAAG,KAG1EP,CACX,EAEAJ,EAGcY,oBAAd,WACI,IAAMX,EAAWtN,KAAKsN,WAEtB,MAAO,CACHA,SAAAA,EACAY,aAAcZ,EAAWlE,EAAUK,qBAAuBL,EAAUM,sBACpEyE,cAAeb,EAAWlE,EAAUO,uBAAyBP,EAAUQ,wBACvEwE,sBAAuBd,EAAWlE,EAAUS,uBAAyBT,EAAUU,wBAC/EuE,qBAAsBf,EAAWlE,EAAUW,sBAAwBX,EAAUM,sBAC7E4E,UAAWhB,EAAW,WAAa,QACnCiB,aAAcjB,EAAW,EAAI,EAErC,EAACD,CAAA,CA7CoB,GAAZA,EACME,UAA4B,KCpHxC,IAAMiB,EAAY,WAKrB,SAAAA,IAAsB,KAHdC,SAAwC,IAAIC,IAAK,KACjDC,iBAAW,EAGf3O,KAAK2O,YAAc3G,EAAYI,aACnC,CAACoG,EAEapG,YAAd,WAII,OAHKoG,EAAanG,WACdmG,EAAanG,SAAW,IAAImG,GAEzBA,EAAanG,QACxB,EAEA,IAAA/C,EAAAkJ,EAAA9P,UA0SC,OA1SD4G,EAGOsJ,GAAP,SAAUC,EAAYC,EAAkBC,EAAeC,GAC9ChP,KAAKyO,SAASQ,IAAIJ,IACnB7O,KAAKyO,SAASS,IAAIL,EAAI,IAG1B,IAAMM,EAA6B,CAAEL,SAAAA,EAAUC,MAAAA,EAAOC,QAAAA,GACtDhP,KAAKyO,SAASvK,IAAI2K,GAAKlI,KAAKwI,GAG5BC,EAAEC,UAAUT,GAAGG,EAAOD,EAAUE,EACpC,EAEA1J,EAGOgK,IAAP,SAAWT,GACP,IAAMJ,EAAWzO,KAAKyO,SAASvK,IAAI2K,GAC/BJ,IACAA,EAASc,QAAQ,SAAAC,GAAkC,IAA/BV,EAAQU,EAARV,SAAUC,EAAKS,EAALT,MAAOC,EAAOQ,EAAPR,QACjCI,EAAEC,UAAUC,IAAIP,EAAOD,EAAUE,EACrC,GACAhP,KAAKyO,SAAQ,OAAQI,GAE7B,EAEAvJ,EAGOmK,OAAP,WAAsB,IAAAxI,EAAA,KAClBjH,KAAKyO,SAASc,QAAQ,SAACd,EAAUI,GAC7B5H,EAAKqI,IAAIT,EACb,EACJ,EAEAvJ,EAGOoK,sBAAP,SACIC,EACAC,EACAC,GACI,IAAAC,EAAA,KACEC,EAAa1C,EAAaY,sBAC1B9F,EAAoBnI,KAAK2O,YAAYlG,uBAE3CzI,KAAK4O,GAAG,mBAAoB,SAAUmB,EAAWzB,UAAW,SAACS,GACzD,IAAMiB,EAASjB,EAAMkB,cACfC,EAAYd,EAAEY,GAAQG,KAAK,UAC3BC,EAASF,EAAYG,SAASH,GAAa,EAC3CI,EAAcjB,SAASkB,eAAenH,EAAU6B,cAAcM,cAEpE6D,EAAE,QAAQoB,IAAI,aAAc,SAE5BV,EAAKW,kBAAkBL,EAAQT,EAAyBW,EAAanI,GACrE2H,EAAKY,sBAAsBV,EAAQJ,EAAcQ,EACrD,EACJ,EAEA9K,EAGQmL,kBAAR,SACIL,EACAT,EACAW,EACAnI,GAKA,OAFAnI,KAAK2Q,oBAEGP,GACJ,KAAK,EACDpQ,KAAK4Q,cAAcN,GACnB,MACJ,KAAK,EACDtQ,KAAK6Q,cAAcP,EAAanI,GAChC,MACJ,KAAK,EACDnI,KAAK8Q,cAAcR,GACnB,MACJ,KAAK,EACDtQ,KAAK+Q,mBAAmBT,GACxB,MACJ,QACItQ,KAAKgR,qBAAqBZ,EAAQT,GAG9C,EAEArK,EAGQqL,kBAAR,WACIvB,EAAE,IAAIhG,EAAUyC,YAAYX,sBAAwBsF,IAAI,UAAW,QACnEpB,EAAE,IAAIhG,EAAUyC,YAAYI,iBAAmBuE,IAAI,UAAW,QAC9DpB,EAAE,IAAIhG,EAAUyC,YAAYK,iBAAmBsE,IAAI,UAAW,QAC9DpB,EAAE,IAAIhG,EAAUyC,YAAYO,gCAAkCoE,IAAI,UAAW,QAC7EpB,EAAE,IAAIhG,EAAUyC,YAAYM,sBAAwBqE,IAAI,UAAW,OACvE,EAEAlL,EAGQsL,cAAR,SAAsBN,GAClBlB,EAAE,IAAIhG,EAAUyC,YAAYX,sBAAwBsF,IAAI,UAAW,IACnEpB,EAAE,QAAQoB,IAAI,aAAc,QACxBF,IACAA,EAAYnD,IAAM,GAE1B,EAEA7H,EAGQuL,cAAR,SAAsBP,EAAuCnI,GACzDiH,EAAE,IAAIhG,EAAUyC,YAAYI,iBAAmBuE,IAAI,UAAW,gBAE9D,IAAMS,EAAe7B,EAAE,mBAAmB8B,eAAiB,EACrDC,EAAoB/B,EAAE,yBAAyB8B,eAAiB,EAChEE,EAAmBhC,EAAE,mBAAmB8B,eAAiB,EACzDG,EAAexD,OAAOyD,YAAcL,EAAeE,EAAoBC,EAE7E,GAAId,EAAa,CACblB,EAAE,gBAAgBoB,IAAI,SAAUa,EAAe,MAC/C,IAAME,EAAcpJ,EAAkBN,iBACtC,GAAI0J,EAAa,CACb,IAAMC,EAAgBD,EAAY3E,UAAU,SACxC0D,EAAYnD,MAAQqE,IACpBlB,EAAYnD,IAAMqE,EAE1B,CACJ,CACJ,EAEAlM,EAGQwL,cAAR,SAAsBR,GAClBlB,EAAE,IAAIhG,EAAUyC,YAAYK,iBAAmBsE,IAAI,UAAW,QAC1DF,IACAA,EAAYnD,IAAM,GAE1B,EAEA7H,EAGQyL,mBAAR,SAA2BT,GACvBlB,EAAE,IAAIhG,EAAUyC,YAAYM,sBAAwBqE,IAAI,UAAW,QAC/DF,IACAA,EAAYnD,IAAM,GAE1B,EAEA7H,EAGQ0L,qBAAR,SAA6BZ,EAAgBT,GACzC,IAAM8B,EAAmB9B,EAAwBS,GAEjD,GAAIqB,EAAkB,CAClB,IAAMR,EAAe7B,EAAE,mBAAmB8B,eAAiB,EACrDC,EAAoB/B,EAAE,yBAAyB8B,eAAiB,EAChEE,EAAmBhC,EAAE,mBAAmB8B,eAAiB,EAC3DG,EAAexD,OAAOyD,YAAcL,EAAeE,EAAoBC,EACvEM,EAAgB,EAChBC,EAA0B,MAC1BC,EAAmCxC,EAAE,uBAAuBoB,IAAI,WAAa,MAGnE,GAAVJ,GACAiB,EAAe,IACfO,EAAkB,KACD,GAAVxB,GACPiB,EAAeO,EAAkB,IACjCD,EAAY,MACK,GAAVvB,GAAyB,GAAVA,IACtBsB,EAAgB,IAGpBtC,EAAE,IAAIhG,EAAUyC,YAAYO,gCAAkCoE,IAAI,CAC9D,iBAAkBkB,EAAgB,KAClC,OAAUE,EAAkB,KAC5B,QAAW,iBAGfxC,EAAE,IAAIhG,EAAU6B,cAAcO,sBAAwBgF,IAAI,CACtD,iBAAkBkB,EAAgB,KAClC,OAAUL,EAAe,OAC1BlB,KAAK,YAAawB,GAErB,IAAME,EAAqBxC,SAASkB,eAAenH,EAAU6B,cAAcO,sBACvEqG,IACAA,EAAmB1E,IAAMsE,EAAiBK,IAElD,CACJ,EAEAxM,EAGQoL,sBAAR,SAA8BV,EAAqBJ,EAA4BQ,GAC3E,IAAM2B,EAAsB3C,EAAEY,GAAQgC,aAChCC,EAA2B7C,EAAE,IAAIhG,EAAU6B,cAAcQ,4BAE3DwG,EAAyB1S,QAAUwS,GACnCE,EAAyBC,MAAMH,QAGNlO,IAAzB+L,EAAaQ,IACb6B,EAAyBzB,IAAI,OAAQZ,EAAaQ,GAE1D,EAEA9K,EAGO6M,qBAAP,WAAoC,IAAAC,EAAA,KAC1BrC,EAAa1C,EAAaY,sBAC1B9F,EAAoBnI,KAAK2O,YAAYlG,uBACrC6H,EAAcjB,SAASkB,eAAenH,EAAU6B,cAAcM,cAGpEvL,KAAK4O,GAAG,eAAgB,IAAIxF,EAAU6B,cAAcS,oBAAuBqE,EAAWzB,UAAW,WACzFgC,IACAA,EAAYnD,IAAM,GAClBkF,WAAW,WACP,IAAMd,EAAcpJ,EAAkBN,iBAClC0J,GAAejB,IACfA,EAAYnD,IAAMoE,EAAY3E,UAAU,SAEhD,EAAGxD,EAAUI,qBAErB,GAGAxJ,KAAK4O,GAAG,YAAa,IAAIxF,EAAU6B,cAAcU,iBAAoBoE,EAAWzB,UAAW,WACvFnG,EAAkBP,mBAClB,IAAM2J,EAAcpJ,EAAkBN,iBAElC0J,GAAejB,IACfA,EAAYnD,IAAMoE,EAAY3E,UAAU,UAG5CwF,EAAKE,wBAAwBnK,EACjC,GAGAnI,KAAK4O,GAAG,YAAa,IAAIxF,EAAU6B,cAAcW,iBAAoBmE,EAAWzB,UAAW,WACvFnG,EAAkBR,mBAClB,IAAM4J,EAAcpJ,EAAkBN,iBAElC0J,GAAejB,IACfA,EAAYnD,IAAMoE,EAAY3E,UAAU,UAG5CwF,EAAKE,wBAAwBnK,EACjC,EACJ,EAEA7C,EAGQgN,wBAAR,SAAgCnK,GAC5BiH,EAAE,oBAAoBoB,IAAI,QAASrI,EAAkBL,UAAY,GAAK,QACtEsH,EAAE,oBAAoBoB,IAAI,QAASrI,EAAkBJ,cAAgB,GAAK,OAC9E,EAEAzC,EAGOiN,uBAAP,WACuBlF,EAAaY,sBAEjBX,WACX8B,EAAE,uBAAuBpJ,KAAK,qBAAqBwM,KAAK,wCACxDpD,EAAE,uBAAuBpJ,KAAK,qBAAqBwK,IAAI,CACnD,QAAW,QACX,YAAa,OACb,eAAgB,UAIxBpB,EAAE,uBAAuBpJ,KAAK,KAAKwK,IAAI,UAAW,QAClDpB,EAAE,aAAaqD,SACfrD,EAAE,aAAaoB,IAAI,UAAW,OAClC,EAEAlL,EAGOoN,yBAAP,WACItD,EAAE,0BAA0BuD,SAAS,sBACrCvD,EAAE,uBAAuBwD,SAASC,OAAOzD,EAAE,2BAC3CA,EAAE,0BAA0BoB,IAAI,QAAS,OAC7C,EAAChC,CAAA,CA1ToB,GCJzB,SAAS,EAASrK,GAChB,OAAe,OAARA,GAA+B,iBAARA,GAAoB,gBAAiBA,GAAOA,EAAIpC,cAAgBlD,MAChG,CACA,SAASiU,EAAO9C,EAAQ7C,QACP,IAAX6C,IACFA,EAAS,CAAC,QAEA,IAAR7C,IACFA,EAAM,CAAC,GAET,MAAM4F,EAAW,CAAC,YAAa,cAAe,aAC9ClU,OAAOkE,KAAKoK,GAAKpE,OAAO9E,GAAO8O,EAASC,QAAQ/O,GAAO,GAAGsL,QAAQtL,SACrC,IAAhB+L,EAAO/L,GAAsB+L,EAAO/L,GAAOkJ,EAAIlJ,GAAc,EAASkJ,EAAIlJ,KAAS,EAAS+L,EAAO/L,KAASpF,OAAOkE,KAAKoK,EAAIlJ,IAAM1E,OAAS,GACpJuT,EAAO9C,EAAO/L,GAAMkJ,EAAIlJ,KAG9B,CDZauK,EACMnG,cAAQ,ECY3B,MAAM4K,EAAc,CAClBC,KAAM,CAAC,EACP,gBAAAC,GAAoB,EACpB,mBAAAC,GAAuB,EACvBC,cAAe,CACb,IAAAC,GAAQ,EACRC,SAAU,IAEZC,cAAa,IACJ,KAETC,iBAAgB,IACP,GAETlD,eAAc,IACL,KAETmD,YAAW,KACF,CACL,SAAAC,GAAa,IAGjBC,cAAa,KACJ,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,CAAC,EACR,YAAAC,GAAgB,EAChBC,qBAAoB,IACX,KAIbC,gBAAe,KACN,CAAC,GAEVC,WAAU,IACD,KAETC,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGZ,SAAS,IACP,MAAMC,EAA0B,oBAAbxF,SAA2BA,SAAW,CAAC,EAE1D,OADAyD,EAAO+B,EAAK5B,GACL4B,CACT,CACA,MAAMC,EAAY,CAChBzF,SAAU4D,EACVtF,UAAW,CACTD,UAAW,IAEb0G,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEVG,QAAS,CACP,YAAAC,GAAgB,EAChB,SAAAC,GAAa,EACb,EAAAC,GAAM,EACN,IAAAC,GAAQ,GAEVC,YAAa,WACX,OAAOpV,IACT,EACA,gBAAAmT,GAAoB,EACpB,mBAAAC,GAAuB,EACvBiC,iBAAgB,KACP,CACLC,iBAAgB,IACP,KAIb,KAAAC,GAAS,EACT,IAAAC,GAAQ,EACRC,OAAQ,CAAC,EACT,UAAApD,GAAc,EACd,YAAAqD,GAAgB,EAChBC,WAAU,KACD,CAAC,GAEVC,sBAAsBC,GACM,oBAAfxD,YACTwD,IACO,MAEFxD,WAAWwD,EAAU,GAE9B,oBAAAC,CAAqBjH,GACO,oBAAfwD,YAGXqD,aAAa7G,EACf,GAEF,SAAS,IACP,MAAMkH,EAAwB,oBAAXlI,OAAyBA,OAAS,CAAC,EAEtD,OADAiF,EAAOiD,EAAKjB,GACLiB,CACT,CCvHA,SAAS,EAASF,EAAUG,GAI1B,YAHc,IAAVA,IACFA,EAAQ,GAEH3D,WAAWwD,EAAUG,EAC9B,CACA,SAAS,IACP,OAAOR,KAAKS,KACd,CAsDA,SAAS,EAAS3X,GAChB,MAAoB,iBAANA,GAAwB,OAANA,GAAcA,EAAEyD,aAAkE,WAAnDlD,OAAOH,UAAUwX,SAASxW,KAAKpB,GAAG6X,MAAM,GAAI,EAC7G,CACA,SAASC,EAAOC,GAEd,MAAsB,oBAAXxI,aAAwD,IAAvBA,OAAOyI,YAC1CD,aAAgBC,YAElBD,IAA2B,IAAlBA,EAAKE,UAAoC,KAAlBF,EAAKE,SAC9C,CACA,SAAS,IACP,MAAMC,EAAK3X,OAAO2F,UAAUjF,QAAU,OAAIsE,EAAYW,UAAU,IAC1DuO,EAAW,CAAC,YAAa,cAAe,aAC9C,IAAK,IAAIvU,EAAI,EAAGA,EAAIgG,UAAUjF,OAAQf,GAAK,EAAG,CAC5C,MAAMiY,EAAajY,EAAI,GAAKgG,UAAUjF,QAAUf,OAAIqF,EAAYW,UAAUhG,GAC1E,GAAIiY,UAAoDL,EAAOK,GAAa,CAC1E,MAAMC,EAAY7X,OAAOkE,KAAKlE,OAAO4X,IAAa1N,OAAO9E,GAAO8O,EAASC,QAAQ/O,GAAO,GACxF,IAAK,IAAI0S,EAAY,EAAGC,EAAMF,EAAUnX,OAAQoX,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,GACpBG,EAAOjY,OAAOkY,yBAAyBN,EAAYI,QAC5ChT,IAATiT,GAAsBA,EAAKzT,aACzB,EAASmT,EAAGK,KAAa,EAASJ,EAAWI,IAC3CJ,EAAWI,GAASG,WACtBR,EAAGK,GAAWJ,EAAWI,GAEzB,EAAOL,EAAGK,GAAUJ,EAAWI,KAEvB,EAASL,EAAGK,KAAa,EAASJ,EAAWI,KACvDL,EAAGK,GAAW,CAAC,EACXJ,EAAWI,GAASG,WACtBR,EAAGK,GAAWJ,EAAWI,GAEzB,EAAOL,EAAGK,GAAUJ,EAAWI,KAGjCL,EAAGK,GAAWJ,EAAWI,GAG/B,CACF,CACF,CACA,OAAOL,CACT,CACA,SAAS,EAAeS,EAAIC,EAASC,GACnCF,EAAGlD,MAAMqD,YAAYF,EAASC,EAChC,CACA,SAASE,EAAqB7H,GAC5B,IAAI,OACF8H,EAAM,eACNC,EAAc,KACdC,GACEhI,EACJ,MAAM3B,EAAS,IACT4J,GAAiBH,EAAOI,UAC9B,IACIC,EADAC,EAAY,KAEhB,MAAMC,EAAWP,EAAOQ,OAAOC,MAC/BT,EAAOU,UAAUjE,MAAMkE,eAAiB,OACxCpK,EAAOiI,qBAAqBwB,EAAOY,gBACnC,MAAMC,EAAMZ,EAAiBE,EAAgB,OAAS,OAChDW,EAAe,CAACC,EAASrI,IACd,SAARmI,GAAkBE,GAAWrI,GAAkB,SAARmI,GAAkBE,GAAWrI,EAEvEsI,EAAU,KACdX,GAAO,IAAInC,MAAO+C,UACA,OAAdX,IACFA,EAAYD,GAEd,MAAMa,EAAWC,KAAKC,IAAID,KAAKE,KAAKhB,EAAOC,GAAaC,EAAU,GAAI,GAChEe,EAAe,GAAMH,KAAKI,IAAIL,EAAWC,KAAKK,IAAM,EAC1D,IAAIC,EAAkBtB,EAAgBmB,GAAgBrB,EAAiBE,GAOvE,GANIW,EAAaW,EAAiBxB,KAChCwB,EAAkBxB,GAEpBD,EAAOU,UAAUgB,SAAS,CACxB,CAACxB,GAAOuB,IAENX,EAAaW,EAAiBxB,GAUhC,OATAD,EAAOU,UAAUjE,MAAMkF,SAAW,SAClC3B,EAAOU,UAAUjE,MAAMkE,eAAiB,GACxC5F,WAAW,KACTiF,EAAOU,UAAUjE,MAAMkF,SAAW,GAClC3B,EAAOU,UAAUgB,SAAS,CACxB,CAACxB,GAAOuB,WAGZlL,EAAOiI,qBAAqBwB,EAAOY,gBAGrCZ,EAAOY,eAAiBrK,EAAO+H,sBAAsB0C,IAEvDA,GACF,CACA,SAAS,EAAoBY,GAC3B,OAAOA,EAAQ1F,cAAc,4BAA8B0F,EAAQC,YAAcD,EAAQC,WAAW3F,cAAc,4BAA8B0F,CAClJ,CACA,SAAS,EAAgBE,EAAStK,QACf,IAAbA,IACFA,EAAW,IAEb,MAAMjB,EAAS,IACTgG,EAAW,IAAIuF,EAAQvF,UAI7B,OAHIhG,EAAOwL,iBAAmBD,aAAmBC,iBAC/CxF,EAASlN,QAAQyS,EAAQE,oBAEtBxK,EAGE+E,EAAS9K,OAAOkO,GAAMA,EAAGsC,QAAQzK,IAF/B+E,CAGX,CAwBA,SAAS2F,EAAYC,GACnB,IAEE,YADAtT,QAAQuT,KAAKD,EAEf,CAAE,MAAOE,GAET,CACF,CACA,SAAS,EAAcC,EAAKC,QACV,IAAZA,IACFA,EAAU,IAEZ,MAAM5C,EAAK5H,SAASuE,cAAcgG,GAElC,OADA3C,EAAG6C,UAAUC,OAAQ3S,MAAM4S,QAAQH,GAAWA,EAtOhD,SAAyBA,GAIvB,YAHgB,IAAZA,IACFA,EAAU,IAELA,EAAQI,OAAOC,MAAM,KAAKnR,OAAOtK,KAAOA,EAAEwb,OACnD,CAiO0D,CAAgBJ,IACjE5C,CACT,CAqCA,SAASkD,EAAalD,EAAI7S,GAExB,OADe,IACDiR,iBAAiB4B,EAAI,MAAM3B,iBAAiBlR,EAC5D,CACA,SAAS,EAAa6S,GACpB,IACIzY,EADA4b,EAAQnD,EAEZ,GAAImD,EAAO,CAGT,IAFA5b,EAAI,EAEuC,QAAnC4b,EAAQA,EAAMC,kBACG,IAAnBD,EAAM7D,WAAgB/X,GAAK,GAEjC,OAAOA,CACT,CAEF,CACA,SAAS,EAAeyY,EAAInI,GAC1B,MAAMwL,EAAU,GAChB,IAAI1H,EAASqE,EAAGsD,cAChB,KAAO3H,GACD9D,EACE8D,EAAO2G,QAAQzK,IAAWwL,EAAQ3T,KAAKiM,GAE3C0H,EAAQ3T,KAAKiM,GAEfA,EAASA,EAAO2H,cAElB,OAAOD,CACT,CAWA,SAASE,EAAiBvD,EAAIwD,EAAMC,GAClC,MAAM7M,EAAS,IACf,OAAI6M,EACKzD,EAAY,UAATwD,EAAmB,cAAgB,gBAAkBE,WAAW9M,EAAOwH,iBAAiB4B,EAAI,MAAM3B,iBAA0B,UAATmF,EAAmB,eAAiB,eAAiBE,WAAW9M,EAAOwH,iBAAiB4B,EAAI,MAAM3B,iBAA0B,UAATmF,EAAmB,cAAgB,kBAE9QxD,EAAG2D,WACZ,CACA,SAAS,EAAkB3D,GACzB,OAAQ7P,MAAM4S,QAAQ/C,GAAMA,EAAK,CAACA,IAAKlO,OAAO/K,KAAOA,EACvD,CASA,SAAS,EAAaiZ,EAAIzE,QACX,IAATA,IACFA,EAAO,IAEmB,oBAAjBqI,aACT5D,EAAG6D,UAAYD,aAAaE,aAAa,OAAQ,CAC/CC,WAAYC,GAAKA,IAChBD,WAAWxI,GAEdyE,EAAG6D,UAAYtI,CAEnB,CCjVA,IAAI0I,EAgBAC,EAqDAC,EA5DJ,SAASC,IAIP,OAHKH,IACHA,EAVJ,WACE,MAAMrN,EAAS,IACTwB,EAAW,IACjB,MAAO,CACLiM,aAAcjM,EAASkM,iBAAmBlM,EAASkM,gBAAgBxH,OAAS,mBAAoB1E,EAASkM,gBAAgBxH,MACzHyH,SAAU,iBAAkB3N,GAAUA,EAAO4N,eAAiBpM,aAAoBxB,EAAO4N,eAE7F,CAGcC,IAELR,CACT,CA6CA,SAASS,EAAUC,GAOjB,YANkB,IAAdA,IACFA,EAAY,CAAC,GAEVT,IACHA,EA/CJ,SAAoBU,GAClB,IAAI,UACFnO,QACY,IAAVmO,EAAmB,CAAC,EAAIA,EAC5B,MAAMX,EAAUG,IACVxN,EAAS,IACTiO,EAAWjO,EAAOF,UAAUmO,SAC5BC,EAAKrO,GAAaG,EAAOF,UAAUD,UACnCsO,EAAS,CACbC,KAAK,EACLC,SAAS,GAELC,EAActO,EAAO4H,OAAOvD,MAC5BkK,EAAevO,EAAO4H,OAAO4G,OAC7BH,EAAUH,EAAGO,MAAM,+BACzB,IAAIC,EAAOR,EAAGO,MAAM,wBACpB,MAAME,EAAOT,EAAGO,MAAM,2BAChBG,GAAUF,GAAQR,EAAGO,MAAM,8BAC3BI,EAAuB,UAAbZ,EAChB,IAAIa,EAAqB,aAAbb,EAqBZ,OAjBKS,GAAQI,GAASzB,EAAQM,OADV,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,YACxGxI,QAAQ,GAAGmJ,KAAeC,MAAmB,IAC9FG,EAAOR,EAAGO,MAAM,uBACXC,IAAMA,EAAO,CAAC,EAAG,EAAG,WACzBI,GAAQ,GAINT,IAAYQ,IACdV,EAAOY,GAAK,UACZZ,EAAOE,SAAU,IAEfK,GAAQE,GAAUD,KACpBR,EAAOY,GAAK,MACZZ,EAAOC,KAAM,GAIRD,CACT,CAMmBa,CAAWjB,IAErBT,CACT,CA4BA,SAAS2B,IAIP,OAHK1B,IACHA,EA3BJ,WACE,MAAMvN,EAAS,IACTmO,EAASL,IACf,IAAIoB,GAAqB,EACzB,SAASC,IACP,MAAMjB,EAAKlO,EAAOF,UAAUD,UAAUuP,cACtC,OAAOlB,EAAG/I,QAAQ,WAAa,GAAK+I,EAAG/I,QAAQ,UAAY,GAAK+I,EAAG/I,QAAQ,WAAa,CAC1F,CACA,GAAIgK,IAAY,CACd,MAAMjB,EAAKmB,OAAOrP,EAAOF,UAAUD,WACnC,GAAIqO,EAAGoB,SAAS,YAAa,CAC3B,MAAOC,EAAOC,GAAStB,EAAG7B,MAAM,YAAY,GAAGA,MAAM,KAAK,GAAGA,MAAM,KAAKoD,IAAIC,GAAO1Q,OAAO0Q,IAC1FR,EAAqBK,EAAQ,IAAgB,KAAVA,GAAgBC,EAAQ,CAC7D,CACF,CACA,MAAMG,EAAY,+CAA+CzP,KAAKF,EAAOF,UAAUD,WACjF+P,EAAkBT,IAExB,MAAO,CACLA,SAAUD,GAAsBU,EAChCV,qBACAW,UAJgBD,GAAmBD,GAAaxB,EAAOC,IAKvDuB,YAEJ,CAGcG,IAELvC,CACT,CAiJA,IAAIwC,EAAgB,CAClB,EAAAhP,CAAGiP,EAAQ7O,EAAS8O,GAClB,MAAMC,EAAO/d,KACb,IAAK+d,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZ/O,EAAwB,OAAO+O,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAKtC,OAJAD,EAAO3D,MAAM,KAAK3K,QAAQR,IACnBgP,EAAKC,gBAAgBjP,KAAQgP,EAAKC,gBAAgBjP,GAAS,IAChEgP,EAAKC,gBAAgBjP,GAAOmP,GAAQlP,KAE/B+O,CACT,EACA,IAAAI,CAAKN,EAAQ7O,EAAS8O,GACpB,MAAMC,EAAO/d,KACb,IAAK+d,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZ/O,EAAwB,OAAO+O,EAC1C,SAASK,IACPL,EAAKzO,IAAIuO,EAAQO,GACbA,EAAYC,uBACPD,EAAYC,eAErB,IAAK,IAAInX,EAAO1C,UAAUjF,OAAQ4H,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQ7C,UAAU6C,GAEzB2H,EAAQvK,MAAMsZ,EAAM5W,EACtB,CAEA,OADAiX,EAAYC,eAAiBrP,EACtB+O,EAAKnP,GAAGiP,EAAQO,EAAaN,EACtC,EACA,KAAAQ,CAAMtP,EAAS8O,GACb,MAAMC,EAAO/d,KACb,IAAK+d,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZ/O,EAAwB,OAAO+O,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAItC,OAHIC,EAAKQ,mBAAmBvL,QAAQhE,GAAW,GAC7C+O,EAAKQ,mBAAmBL,GAAQlP,GAE3B+O,CACT,EACA,MAAAS,CAAOxP,GACL,MAAM+O,EAAO/d,KACb,IAAK+d,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKQ,mBAAoB,OAAOR,EACrC,MAAMhR,EAAQgR,EAAKQ,mBAAmBvL,QAAQhE,GAI9C,OAHIjC,GAAS,GACXgR,EAAKQ,mBAAmBE,OAAO1R,EAAO,GAEjCgR,CACT,EACA,GAAAzO,CAAIuO,EAAQ7O,GACV,MAAM+O,EAAO/d,KACb,OAAK+d,EAAKC,iBAAmBD,EAAKE,UAAkBF,EAC/CA,EAAKC,iBACVH,EAAO3D,MAAM,KAAK3K,QAAQR,SACD,IAAZC,EACT+O,EAAKC,gBAAgBjP,GAAS,GACrBgP,EAAKC,gBAAgBjP,IAC9BgP,EAAKC,gBAAgBjP,GAAOQ,QAAQ,CAACJ,EAAcpC,MAC7CoC,IAAiBH,GAAWG,EAAakP,gBAAkBlP,EAAakP,iBAAmBrP,IAC7F+O,EAAKC,gBAAgBjP,GAAO0P,OAAO1R,EAAO,OAK3CgR,GAZ2BA,CAapC,EACA,IAAAW,GACE,MAAMX,EAAO/d,KACb,IAAK+d,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKC,gBAAiB,OAAOD,EAClC,IAAIF,EACAxY,EACAsZ,EACJ,IAAK,IAAIC,EAAQpa,UAAUjF,OAAQ4H,EAAO,IAAIC,MAAMwX,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACpF1X,EAAK0X,GAASra,UAAUqa,GAyB1B,MAvBuB,iBAAZ1X,EAAK,IAAmBC,MAAM4S,QAAQ7S,EAAK,KACpD0W,EAAS1W,EAAK,GACd9B,EAAO8B,EAAKgP,MAAM,EAAGhP,EAAK5H,QAC1Bof,EAAUZ,IAEVF,EAAS1W,EAAK,GAAG0W,OACjBxY,EAAO8B,EAAK,GAAG9B,KACfsZ,EAAUxX,EAAK,GAAGwX,SAAWZ,GAE/B1Y,EAAKzE,QAAQ+d,IACOvX,MAAM4S,QAAQ6D,GAAUA,EAASA,EAAO3D,MAAM,MACtD3K,QAAQR,IACdgP,EAAKQ,oBAAsBR,EAAKQ,mBAAmBhf,QACrDwe,EAAKQ,mBAAmBhP,QAAQJ,IAC9BA,EAAa1K,MAAMka,EAAS,CAAC5P,KAAU1J,MAGvC0Y,EAAKC,iBAAmBD,EAAKC,gBAAgBjP,IAC/CgP,EAAKC,gBAAgBjP,GAAOQ,QAAQJ,IAClCA,EAAa1K,MAAMka,EAAStZ,OAI3B0Y,CACT,GA6WF,MAAMe,EAAuB,CAAC5F,EAAS6F,EAAWC,KAC5CD,IAAc7F,EAAQY,UAAUmF,SAASD,GAC3C9F,EAAQY,UAAUC,IAAIiF,IACZD,GAAa7F,EAAQY,UAAUmF,SAASD,IAClD9F,EAAQY,UAAUrH,OAAOuM,IAgHvBE,GAAqB,CAAChG,EAAS6F,EAAWC,KAC1CD,IAAc7F,EAAQY,UAAUmF,SAASD,GAC3C9F,EAAQY,UAAUC,IAAIiF,IACZD,GAAa7F,EAAQY,UAAUmF,SAASD,IAClD9F,EAAQY,UAAUrH,OAAOuM,IA4DvBG,GAAuB,CAAC7H,EAAQ8H,KACpC,IAAK9H,GAAUA,EAAO2G,YAAc3G,EAAOQ,OAAQ,OACnD,MACMoB,EAAUkG,EAAQC,QADI/H,EAAOgI,UAAY,eAAiB,IAAIhI,EAAOQ,OAAOyH,cAElF,GAAIrG,EAAS,CACX,IAAIsG,EAAStG,EAAQ1F,cAAc,IAAI8D,EAAOQ,OAAO2H,uBAChDD,GAAUlI,EAAOgI,YAChBpG,EAAQC,WACVqG,EAAStG,EAAQC,WAAW3F,cAAc,IAAI8D,EAAOQ,OAAO2H,sBAG5D7J,sBAAsB,KAChBsD,EAAQC,aACVqG,EAAStG,EAAQC,WAAW3F,cAAc,IAAI8D,EAAOQ,OAAO2H,sBACxDD,GAAQA,EAAO/M,aAKvB+M,GAAQA,EAAO/M,QACrB,GAEIiN,GAAS,CAACpI,EAAQvK,KACtB,IAAKuK,EAAOqI,OAAO5S,GAAQ,OAC3B,MAAMqS,EAAU9H,EAAOqI,OAAO5S,GAAOyG,cAAc,oBAC/C4L,GAASA,EAAQQ,gBAAgB,YAEjCC,GAAUvI,IACd,IAAKA,GAAUA,EAAO2G,YAAc3G,EAAOQ,OAAQ,OACnD,IAAIgI,EAASxI,EAAOQ,OAAOiI,oBAC3B,MAAMnJ,EAAMU,EAAOqI,OAAOpgB,OAC1B,IAAKqX,IAAQkJ,GAAUA,EAAS,EAAG,OACnCA,EAASrH,KAAKE,IAAImH,EAAQlJ,GAC1B,MAAMzI,EAAgD,SAAhCmJ,EAAOQ,OAAO3J,cAA2BmJ,EAAO0I,uBAAyBvH,KAAKwH,KAAK3I,EAAOQ,OAAO3J,eACjH+R,EAAc5I,EAAO4I,YAC3B,GAAI5I,EAAOQ,OAAOqI,MAAQ7I,EAAOQ,OAAOqI,KAAKC,KAAO,EAAG,CACrD,MAAMC,EAAeH,EACfI,EAAiB,CAACD,EAAeP,GASvC,OARAQ,EAAe3Z,QAAQS,MAAMmZ,KAAK,CAChChhB,OAAQugB,IACPxC,IAAI,CAACkD,EAAGhiB,IACF6hB,EAAelS,EAAgB3P,SAExC8Y,EAAOqI,OAAOpQ,QAAQ,CAAC2J,EAAS1a,KAC1B8hB,EAAenD,SAASjE,EAAQuH,SAASf,GAAOpI,EAAQ9Y,IAGhE,CACA,MAAMkiB,EAAuBR,EAAc/R,EAAgB,EAC3D,GAAImJ,EAAOQ,OAAO6I,QAAUrJ,EAAOQ,OAAO8I,KACxC,IAAK,IAAIpiB,EAAI0hB,EAAcJ,EAAQthB,GAAKkiB,EAAuBZ,EAAQthB,GAAK,EAAG,CAC7E,MAAMqiB,GAAariB,EAAIoY,EAAMA,GAAOA,GAChCiK,EAAYX,GAAeW,EAAYH,IAAsBhB,GAAOpI,EAAQuJ,EAClF,MAEA,IAAK,IAAIriB,EAAIia,KAAKC,IAAIwH,EAAcJ,EAAQ,GAAIthB,GAAKia,KAAKE,IAAI+H,EAAuBZ,EAAQlJ,EAAM,GAAIpY,GAAK,EACtGA,IAAM0hB,IAAgB1hB,EAAIkiB,GAAwBliB,EAAI0hB,IACxDR,GAAOpI,EAAQ9Y,IA4JvB,IAAIsiB,GAAS,CACXC,WApvBF,WACE,MAAMzJ,EAAStX,KACf,IAAIkS,EACAmK,EACJ,MAAMpF,EAAKK,EAAOL,GAEhB/E,OADiC,IAAxBoF,EAAOQ,OAAO5F,OAAiD,OAAxBoF,EAAOQ,OAAO5F,MACtDoF,EAAOQ,OAAO5F,MAEd+E,EAAG+J,YAGX3E,OADkC,IAAzB/E,EAAOQ,OAAOuE,QAAmD,OAAzB/E,EAAOQ,OAAOuE,OACtD/E,EAAOQ,OAAOuE,OAEdpF,EAAGgK,aAEA,IAAV/O,GAAeoF,EAAO4J,gBAA6B,IAAX7E,GAAgB/E,EAAO6J,eAKnEjP,EAAQA,EAAQ7B,SAAS8J,EAAalD,EAAI,iBAAmB,EAAG,IAAM5G,SAAS8J,EAAalD,EAAI,kBAAoB,EAAG,IACvHoF,EAASA,EAAShM,SAAS8J,EAAalD,EAAI,gBAAkB,EAAG,IAAM5G,SAAS8J,EAAalD,EAAI,mBAAqB,EAAG,IACrHpK,OAAOrJ,MAAM0O,KAAQA,EAAQ,GAC7BrF,OAAOrJ,MAAM6Y,KAASA,EAAS,GACnCxd,OAAOmG,OAAOsS,EAAQ,CACpBpF,QACAmK,SACA5B,KAAMnD,EAAO4J,eAAiBhP,EAAQmK,IAE1C,EAwtBE+E,aAttBF,WACE,MAAM9J,EAAStX,KACf,SAASqhB,EAA0BhL,EAAMiL,GACvC,OAAO3G,WAAWtE,EAAKf,iBAAiBgC,EAAOiK,kBAAkBD,KAAW,EAC9E,CACA,MAAMxJ,EAASR,EAAOQ,QAChB,UACJE,EAAS,SACTwJ,EACA/G,KAAMgH,EACNC,aAAcC,EAAG,SACjBC,GACEtK,EACEuK,EAAYvK,EAAOwK,SAAWhK,EAAOgK,QAAQC,QAC7CC,EAAuBH,EAAYvK,EAAOwK,QAAQnC,OAAOpgB,OAAS+X,EAAOqI,OAAOpgB,OAChFogB,EAAS,EAAgB6B,EAAU,IAAIlK,EAAOQ,OAAOyH,4BACrD0C,EAAeJ,EAAYvK,EAAOwK,QAAQnC,OAAOpgB,OAASogB,EAAOpgB,OACvE,IAAI2iB,EAAW,GACf,MAAMC,EAAa,GACbC,EAAkB,GACxB,IAAIC,EAAevK,EAAOwK,mBACE,mBAAjBD,IACTA,EAAevK,EAAOwK,mBAAmB5iB,KAAK4X,IAEhD,IAAIiL,EAAczK,EAAO0K,kBACE,mBAAhBD,IACTA,EAAczK,EAAO0K,kBAAkB9iB,KAAK4X,IAE9C,MAAMmL,EAAyBnL,EAAO4K,SAAS3iB,OACzCmjB,EAA2BpL,EAAO6K,WAAW5iB,OACnD,IAAI2O,EAAe4J,EAAO5J,aACtByU,GAAiBN,EACjBO,EAAgB,EAChB7V,EAAQ,EACZ,QAA0B,IAAf0U,EACT,OAE0B,iBAAjBvT,GAA6BA,EAAa8E,QAAQ,MAAQ,EACnE9E,EAAeyM,WAAWzM,EAAa2U,QAAQ,IAAK,KAAO,IAAMpB,EAChC,iBAAjBvT,IAChBA,EAAeyM,WAAWzM,IAE5BoJ,EAAOwL,aAAe5U,EAGtByR,EAAOpQ,QAAQ2J,IACTyI,EACFzI,EAAQnF,MAAMgP,WAAa,GAE3B7J,EAAQnF,MAAMiP,YAAc,GAE9B9J,EAAQnF,MAAMkP,aAAe,GAC7B/J,EAAQnF,MAAMmP,UAAY,KAIxBpL,EAAOqL,gBAAkBrL,EAAOsL,UAClC,EAAepL,EAAW,kCAAmC,IAC7D,EAAeA,EAAW,iCAAkC,KAE9D,MAAMqL,EAAcvL,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,GAAK9I,EAAO6I,KAQlE,IAAImD,EAPAD,EACF/L,EAAO6I,KAAKoD,WAAW5D,GACdrI,EAAO6I,MAChB7I,EAAO6I,KAAKqD,cAKd,MAAMC,EAAgD,SAAzB3L,EAAO3J,eAA4B2J,EAAO4L,aAAe7kB,OAAOkE,KAAK+U,EAAO4L,aAAa3a,OAAO9E,QACnE,IAA1C6T,EAAO4L,YAAYzf,GAAKkK,eACrC5O,OAAS,EACZ,IAAK,IAAIf,EAAI,EAAGA,EAAIyjB,EAAczjB,GAAK,EAAG,CAExC,IAAImlB,EAKJ,GANAL,EAAY,EAER3D,EAAOnhB,KAAImlB,EAAQhE,EAAOnhB,IAC1B6kB,GACF/L,EAAO6I,KAAKyD,YAAYplB,EAAGmlB,EAAOhE,IAEhCA,EAAOnhB,IAAyC,SAAnC2b,EAAawJ,EAAO,WAArC,CAEA,GAA6B,SAAzB7L,EAAO3J,cAA0B,CAC/BsV,IACF9D,EAAOnhB,GAAGuV,MAAMuD,EAAOiK,kBAAkB,UAAY,IAEvD,MAAMsC,EAAcxO,iBAAiBsO,GAC/BG,EAAmBH,EAAM5P,MAAMgQ,UAC/BC,EAAyBL,EAAM5P,MAAMkQ,gBAO3C,GANIH,IACFH,EAAM5P,MAAMgQ,UAAY,QAEtBC,IACFL,EAAM5P,MAAMkQ,gBAAkB,QAE5BnM,EAAOoM,aACTZ,EAAYhM,EAAO4J,eAAiB1G,EAAiBmJ,EAAO,SAAS,GAAQnJ,EAAiBmJ,EAAO,UAAU,OAC1G,CAEL,MAAMzR,EAAQmP,EAA0BwC,EAAa,SAC/CM,EAAc9C,EAA0BwC,EAAa,gBACrDO,EAAe/C,EAA0BwC,EAAa,iBACtDd,EAAa1B,EAA0BwC,EAAa,eACpDb,EAAc3B,EAA0BwC,EAAa,gBACrDQ,EAAYR,EAAYvO,iBAAiB,cAC/C,GAAI+O,GAA2B,eAAdA,EACff,EAAYpR,EAAQ6Q,EAAaC,MAC5B,CACL,MAAM,YACJhC,EAAW,YACXpG,GACE+I,EACJL,EAAYpR,EAAQiS,EAAcC,EAAerB,EAAaC,GAAepI,EAAcoG,EAC7F,CACF,CACI8C,IACFH,EAAM5P,MAAMgQ,UAAYD,GAEtBE,IACFL,EAAM5P,MAAMkQ,gBAAkBD,GAE5BlM,EAAOoM,eAAcZ,EAAY7K,KAAK6L,MAAMhB,GAClD,MACEA,GAAa7B,GAAc3J,EAAO3J,cAAgB,GAAKD,GAAgB4J,EAAO3J,cAC1E2J,EAAOoM,eAAcZ,EAAY7K,KAAK6L,MAAMhB,IAC5C3D,EAAOnhB,KACTmhB,EAAOnhB,GAAGuV,MAAMuD,EAAOiK,kBAAkB,UAAY,GAAG+B,OAGxD3D,EAAOnhB,KACTmhB,EAAOnhB,GAAG+lB,gBAAkBjB,GAE9BlB,EAAgBzb,KAAK2c,GACjBxL,EAAOqL,gBACTR,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAI1U,EAC9C,IAAlB0U,GAA6B,IAANpkB,IAASmkB,EAAgBA,EAAgBlB,EAAa,EAAIvT,GAC3E,IAAN1P,IAASmkB,EAAgBA,EAAgBlB,EAAa,EAAIvT,GAC1DuK,KAAK+L,IAAI7B,GAAiB,OAAUA,EAAgB,GACpD7K,EAAOoM,eAAcvB,EAAgBlK,KAAK6L,MAAM3B,IAChD5V,EAAQ+K,EAAO2M,iBAAmB,GAAGvC,EAASvb,KAAKgc,GACvDR,EAAWxb,KAAKgc,KAEZ7K,EAAOoM,eAAcvB,EAAgBlK,KAAK6L,MAAM3B,KAC/C5V,EAAQ0L,KAAKE,IAAIrB,EAAOQ,OAAO4M,mBAAoB3X,IAAUuK,EAAOQ,OAAO2M,iBAAmB,GAAGvC,EAASvb,KAAKgc,GACpHR,EAAWxb,KAAKgc,GAChBA,EAAgBA,EAAgBW,EAAYpV,GAE9CoJ,EAAOwL,aAAeQ,EAAYpV,EAClC0U,EAAgBU,EAChBvW,GAAS,CArE2D,CAsEtE,CAaA,GAZAuK,EAAOwL,YAAcrK,KAAKC,IAAIpB,EAAOwL,YAAarB,GAAcc,EAC5DZ,GAAOC,IAA+B,UAAlB9J,EAAO6M,QAAwC,cAAlB7M,EAAO6M,UAC1D3M,EAAUjE,MAAM7B,MAAQ,GAAGoF,EAAOwL,YAAc5U,OAE9C4J,EAAO8M,iBACT5M,EAAUjE,MAAMuD,EAAOiK,kBAAkB,UAAY,GAAGjK,EAAOwL,YAAc5U,OAE3EmV,GACF/L,EAAO6I,KAAK0E,kBAAkBvB,EAAWpB,IAItCpK,EAAOqL,eAAgB,CAC1B,MAAM2B,EAAgB,GACtB,IAAK,IAAItmB,EAAI,EAAGA,EAAI0jB,EAAS3iB,OAAQf,GAAK,EAAG,CAC3C,IAAIumB,EAAiB7C,EAAS1jB,GAC1BsZ,EAAOoM,eAAca,EAAiBtM,KAAK6L,MAAMS,IACjD7C,EAAS1jB,IAAM8Y,EAAOwL,YAAcrB,GACtCqD,EAAcne,KAAKoe,EAEvB,CACA7C,EAAW4C,EACPrM,KAAK6L,MAAMhN,EAAOwL,YAAcrB,GAAchJ,KAAK6L,MAAMpC,EAASA,EAAS3iB,OAAS,IAAM,GAC5F2iB,EAASvb,KAAK2Q,EAAOwL,YAAcrB,EAEvC,CACA,GAAII,GAAa/J,EAAO8I,KAAM,CAC5B,MAAMnG,EAAO2H,EAAgB,GAAKlU,EAClC,GAAI4J,EAAO2M,eAAiB,EAAG,CAC7B,MAAMO,EAASvM,KAAKwH,MAAM3I,EAAOwK,QAAQmD,aAAe3N,EAAOwK,QAAQoD,aAAepN,EAAO2M,gBACvFU,EAAY1K,EAAO3C,EAAO2M,eAChC,IAAK,IAAIjmB,EAAI,EAAGA,EAAIwmB,EAAQxmB,GAAK,EAC/B0jB,EAASvb,KAAKub,EAASA,EAAS3iB,OAAS,GAAK4lB,EAElD,CACA,IAAK,IAAI3mB,EAAI,EAAGA,EAAI8Y,EAAOwK,QAAQmD,aAAe3N,EAAOwK,QAAQoD,YAAa1mB,GAAK,EACnD,IAA1BsZ,EAAO2M,gBACTvC,EAASvb,KAAKub,EAASA,EAAS3iB,OAAS,GAAKkb,GAEhD0H,EAAWxb,KAAKwb,EAAWA,EAAW5iB,OAAS,GAAKkb,GACpDnD,EAAOwL,aAAerI,CAE1B,CAEA,GADwB,IAApByH,EAAS3iB,SAAc2iB,EAAW,CAAC,IAClB,IAAjBhU,EAAoB,CACtB,MAAMjK,EAAMqT,EAAO4J,gBAAkBS,EAAM,aAAerK,EAAOiK,kBAAkB,eACnF5B,EAAO5W,OAAO,CAACyX,EAAG4E,MACXtN,EAAOsL,UAAWtL,EAAO8I,OAC1BwE,IAAezF,EAAOpgB,OAAS,GAIlCgQ,QAAQ2J,IACTA,EAAQnF,MAAM9P,GAAO,GAAGiK,OAE5B,CACA,GAAI4J,EAAOqL,gBAAkBrL,EAAOuN,qBAAsB,CACxD,IAAIC,EAAgB,EACpBlD,EAAgB7S,QAAQgW,IACtBD,GAAiBC,GAAkBrX,GAAgB,KAErDoX,GAAiBpX,EACjB,MAAMsX,EAAUF,EAAgB7D,EAAa6D,EAAgB7D,EAAa,EAC1ES,EAAWA,EAAS5E,IAAImI,GAClBA,GAAQ,GAAWpD,EACnBoD,EAAOD,EAAgBA,EAAUjD,EAC9BkD,EAEX,CACA,GAAI3N,EAAO4N,yBAA0B,CACnC,IAAIJ,EAAgB,EACpBlD,EAAgB7S,QAAQgW,IACtBD,GAAiBC,GAAkBrX,GAAgB,KAErDoX,GAAiBpX,EACjB,MAAMyX,GAAc7N,EAAOwK,oBAAsB,IAAMxK,EAAO0K,mBAAqB,GACnF,GAAI8C,EAAgBK,EAAalE,EAAY,CAC3C,MAAMmE,GAAmBnE,EAAa6D,EAAgBK,GAAc,EACpEzD,EAAS3S,QAAQ,CAACkW,EAAMI,KACtB3D,EAAS2D,GAAaJ,EAAOG,IAE/BzD,EAAW5S,QAAQ,CAACkW,EAAMI,KACxB1D,EAAW0D,GAAaJ,EAAOG,GAEnC,CACF,CAOA,GANA/mB,OAAOmG,OAAOsS,EAAQ,CACpBqI,SACAuC,WACAC,aACAC,oBAEEtK,EAAOqL,gBAAkBrL,EAAOsL,UAAYtL,EAAOuN,qBAAsB,CAC3E,EAAerN,EAAW,mCAAuCkK,EAAS,GAAb,MAC7D,EAAelK,EAAW,iCAAqCV,EAAOmD,KAAO,EAAI2H,EAAgBA,EAAgB7iB,OAAS,GAAK,EAAnE,MAC5D,MAAMumB,GAAiBxO,EAAO4K,SAAS,GACjC6D,GAAmBzO,EAAO6K,WAAW,GAC3C7K,EAAO4K,SAAW5K,EAAO4K,SAAS5E,IAAIne,GAAKA,EAAI2mB,GAC/CxO,EAAO6K,WAAa7K,EAAO6K,WAAW7E,IAAIne,GAAKA,EAAI4mB,EACrD,CAeA,GAdI9D,IAAiBD,GACnB1K,EAAOoH,KAAK,sBAEVwD,EAAS3iB,SAAWkjB,IAClBnL,EAAOQ,OAAOkO,eAAe1O,EAAO2O,gBACxC3O,EAAOoH,KAAK,yBAEVyD,EAAW5iB,SAAWmjB,GACxBpL,EAAOoH,KAAK,0BAEV5G,EAAOoO,qBACT5O,EAAO6O,qBAET7O,EAAOoH,KAAK,mBACPmD,GAAc/J,EAAOsL,SAA8B,UAAlBtL,EAAO6M,QAAwC,SAAlB7M,EAAO6M,QAAoB,CAC5F,MAAMyB,EAAsB,GAAGtO,EAAOuO,wCAChCC,EAA6BhP,EAAOL,GAAG6C,UAAUmF,SAASmH,GAC5DnE,GAAgBnK,EAAOyO,wBACpBD,GAA4BhP,EAAOL,GAAG6C,UAAUC,IAAIqM,GAChDE,GACThP,EAAOL,GAAG6C,UAAUrH,OAAO2T,EAE/B,CACF,EAscEI,iBApcF,SAA0BzO,GACxB,MAAMT,EAAStX,KACTymB,EAAe,GACf5E,EAAYvK,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAC1D,IACIvjB,EADAkoB,EAAY,EAEK,iBAAV3O,EACTT,EAAOqP,cAAc5O,IACF,IAAVA,GACTT,EAAOqP,cAAcrP,EAAOQ,OAAOC,OAErC,MAAM6O,EAAkB7Z,GAClB8U,EACKvK,EAAOqI,OAAOrI,EAAOuP,oBAAoB9Z,IAE3CuK,EAAOqI,OAAO5S,GAGvB,GAAoC,SAAhCuK,EAAOQ,OAAO3J,eAA4BmJ,EAAOQ,OAAO3J,cAAgB,EAC1E,GAAImJ,EAAOQ,OAAOqL,gBACf7L,EAAOwP,eAAiB,IAAIvX,QAAQoU,IACnC8C,EAAa9f,KAAKgd,UAGpB,IAAKnlB,EAAI,EAAGA,EAAIia,KAAKwH,KAAK3I,EAAOQ,OAAO3J,eAAgB3P,GAAK,EAAG,CAC9D,MAAMuO,EAAQuK,EAAO4I,YAAc1hB,EACnC,GAAIuO,EAAQuK,EAAOqI,OAAOpgB,SAAWsiB,EAAW,MAChD4E,EAAa9f,KAAKigB,EAAgB7Z,GACpC,MAGF0Z,EAAa9f,KAAKigB,EAAgBtP,EAAO4I,cAI3C,IAAK1hB,EAAI,EAAGA,EAAIioB,EAAalnB,OAAQf,GAAK,EACxC,QAA+B,IAApBioB,EAAajoB,GAAoB,CAC1C,MAAM6d,EAASoK,EAAajoB,GAAGuoB,aAC/BL,EAAYrK,EAASqK,EAAYrK,EAASqK,CAC5C,EAIEA,GAA2B,IAAdA,KAAiBpP,EAAOU,UAAUjE,MAAMsI,OAAS,GAAGqK,MACvE,EAyZEP,mBAvZF,WACE,MAAM7O,EAAStX,KACT2f,EAASrI,EAAOqI,OAEhBqH,EAAc1P,EAAOgI,UAAYhI,EAAO4J,eAAiB5J,EAAOU,UAAUiP,WAAa3P,EAAOU,UAAUkP,UAAY,EAC1H,IAAK,IAAI1oB,EAAI,EAAGA,EAAImhB,EAAOpgB,OAAQf,GAAK,EACtCmhB,EAAOnhB,GAAG2oB,mBAAqB7P,EAAO4J,eAAiBvB,EAAOnhB,GAAGyoB,WAAatH,EAAOnhB,GAAG0oB,WAAaF,EAAc1P,EAAO8P,uBAE9H,EAgZEC,qBAvYF,SAA8B3P,QACV,IAAdA,IACFA,EAAY1X,MAAQA,KAAK0X,WAAa,GAExC,MAAMJ,EAAStX,KACT8X,EAASR,EAAOQ,QAChB,OACJ6H,EACA+B,aAAcC,EAAG,SACjBO,GACE5K,EACJ,GAAsB,IAAlBqI,EAAOpgB,OAAc,YACkB,IAAhCogB,EAAO,GAAGwH,mBAAmC7P,EAAO6O,qBAC/D,IAAImB,GAAgB5P,EAChBiK,IAAK2F,EAAe5P,GACxBJ,EAAOiQ,qBAAuB,GAC9BjQ,EAAOwP,cAAgB,GACvB,IAAI5Y,EAAe4J,EAAO5J,aACE,iBAAjBA,GAA6BA,EAAa8E,QAAQ,MAAQ,EACnE9E,EAAeyM,WAAWzM,EAAa2U,QAAQ,IAAK,KAAO,IAAMvL,EAAOmD,KACvC,iBAAjBvM,IAChBA,EAAeyM,WAAWzM,IAE5B,IAAK,IAAI1P,EAAI,EAAGA,EAAImhB,EAAOpgB,OAAQf,GAAK,EAAG,CACzC,MAAMmlB,EAAQhE,EAAOnhB,GACrB,IAAIgpB,EAAc7D,EAAMwD,kBACpBrP,EAAOsL,SAAWtL,EAAOqL,iBAC3BqE,GAAe7H,EAAO,GAAGwH,mBAE3B,MAAMM,GAAiBH,GAAgBxP,EAAOqL,eAAiB7L,EAAOoQ,eAAiB,GAAKF,IAAgB7D,EAAMY,gBAAkBrW,GAC9HyZ,GAAyBL,EAAepF,EAAS,IAAMpK,EAAOqL,eAAiB7L,EAAOoQ,eAAiB,GAAKF,IAAgB7D,EAAMY,gBAAkBrW,GACpJ0Z,IAAgBN,EAAeE,GAC/BK,EAAaD,EAActQ,EAAO8K,gBAAgB5jB,GAClDspB,EAAiBF,GAAe,GAAKA,GAAetQ,EAAOmD,KAAOnD,EAAO8K,gBAAgB5jB,GACzFupB,EAAYH,GAAe,GAAKA,EAActQ,EAAOmD,KAAO,GAAKoN,EAAa,GAAKA,GAAcvQ,EAAOmD,MAAQmN,GAAe,GAAKC,GAAcvQ,EAAOmD,KAC3JsN,IACFzQ,EAAOwP,cAAcngB,KAAKgd,GAC1BrM,EAAOiQ,qBAAqB5gB,KAAKnI,IAEnCsgB,EAAqB6E,EAAOoE,EAAWjQ,EAAOkQ,mBAC9ClJ,EAAqB6E,EAAOmE,EAAgBhQ,EAAOmQ,wBACnDtE,EAAMnL,SAAWmJ,GAAO8F,EAAgBA,EACxC9D,EAAMuE,iBAAmBvG,GAAOgG,EAAwBA,CAC1D,CACF,EA4VEQ,eA1VF,SAAwBzQ,GACtB,MAAMJ,EAAStX,KACf,QAAyB,IAAd0X,EAA2B,CACpC,MAAM0Q,EAAa9Q,EAAOoK,cAAgB,EAAI,EAE9ChK,EAAYJ,GAAUA,EAAOI,WAAaJ,EAAOI,UAAY0Q,GAAc,CAC7E,CACA,MAAMtQ,EAASR,EAAOQ,OAChBuQ,EAAiB/Q,EAAOgR,eAAiBhR,EAAOoQ,eACtD,IAAI,SACFlP,EAAQ,YACR+P,EAAW,MACXC,EAAK,aACLC,GACEnR,EACJ,MAAMoR,EAAeH,EACfI,EAASH,EACf,GAAuB,IAAnBH,EACF7P,EAAW,EACX+P,GAAc,EACdC,GAAQ,MACH,CACLhQ,GAAYd,EAAYJ,EAAOoQ,gBAAkBW,EACjD,MAAMO,EAAqBnQ,KAAK+L,IAAI9M,EAAYJ,EAAOoQ,gBAAkB,EACnEmB,EAAepQ,KAAK+L,IAAI9M,EAAYJ,EAAOgR,gBAAkB,EACnEC,EAAcK,GAAsBpQ,GAAY,EAChDgQ,EAAQK,GAAgBrQ,GAAY,EAChCoQ,IAAoBpQ,EAAW,GAC/BqQ,IAAcrQ,EAAW,EAC/B,CACA,GAAIV,EAAO8I,KAAM,CACf,MAAMkI,EAAkBxR,EAAOuP,oBAAoB,GAC7CkC,EAAiBzR,EAAOuP,oBAAoBvP,EAAOqI,OAAOpgB,OAAS,GACnEypB,EAAsB1R,EAAO6K,WAAW2G,GACxCG,EAAqB3R,EAAO6K,WAAW4G,GACvCG,EAAe5R,EAAO6K,WAAW7K,EAAO6K,WAAW5iB,OAAS,GAC5D4pB,EAAe1Q,KAAK+L,IAAI9M,GAE5B+Q,EADEU,GAAgBH,GACFG,EAAeH,GAAuBE,GAEtCC,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACxC,CACA5pB,OAAOmG,OAAOsS,EAAQ,CACpBkB,WACAiQ,eACAF,cACAC,WAEE1Q,EAAOoO,qBAAuBpO,EAAOqL,gBAAkBrL,EAAOsR,aAAY9R,EAAO+P,qBAAqB3P,GACtG6Q,IAAgBG,GAClBpR,EAAOoH,KAAK,yBAEV8J,IAAUG,GACZrR,EAAOoH,KAAK,oBAEVgK,IAAiBH,GAAeI,IAAWH,IAC7ClR,EAAOoH,KAAK,YAEdpH,EAAOoH,KAAK,WAAYlG,EAC1B,EA8RE6Q,oBArRF,WACE,MAAM/R,EAAStX,MACT,OACJ2f,EAAM,OACN7H,EAAM,SACN0J,EAAQ,YACRtB,GACE5I,EACEuK,EAAYvK,EAAOwK,SAAWhK,EAAOgK,QAAQC,QAC7CsB,EAAc/L,EAAO6I,MAAQrI,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,EAC/DkJ,EAAmBxa,GAChB,EAAgB0S,EAAU,IAAI1J,EAAOyH,aAAazQ,kBAAyBA,KAAY,GAEhG,IAAIya,EACAC,EACAC,EACJ,GAAI5H,EACF,GAAI/J,EAAO8I,KAAM,CACf,IAAIwE,EAAalF,EAAc5I,EAAOwK,QAAQmD,aAC1CG,EAAa,IAAGA,EAAa9N,EAAOwK,QAAQnC,OAAOpgB,OAAS6lB,GAC5DA,GAAc9N,EAAOwK,QAAQnC,OAAOpgB,SAAQ6lB,GAAc9N,EAAOwK,QAAQnC,OAAOpgB,QACpFgqB,EAAcD,EAAiB,6BAA6BlE,MAC9D,MACEmE,EAAcD,EAAiB,6BAA6BpJ,YAG1DmD,GACFkG,EAAc5J,EAAO3Z,KAAKkT,GAAWA,EAAQuH,SAAWP,GACxDuJ,EAAY9J,EAAO3Z,KAAKkT,GAAWA,EAAQuH,SAAWP,EAAc,GACpEsJ,EAAY7J,EAAO3Z,KAAKkT,GAAWA,EAAQuH,SAAWP,EAAc,IAEpEqJ,EAAc5J,EAAOO,GAGrBqJ,IACGlG,IAEHoG,EDrmBN,SAAwBxS,EAAInI,GAC1B,MAAM4a,EAAU,GAChB,KAAOzS,EAAG0S,oBAAoB,CAC5B,MAAMnoB,EAAOyV,EAAG0S,mBACZ7a,EACEtN,EAAK+X,QAAQzK,IAAW4a,EAAQ/iB,KAAKnF,GACpCkoB,EAAQ/iB,KAAKnF,GACpByV,EAAKzV,CACP,CACA,OAAOkoB,CACT,CC2lBkBE,CAAeL,EAAa,IAAIzR,EAAOyH,4BAA4B,GAC3EzH,EAAO8I,OAAS6I,IAClBA,EAAY9J,EAAO,IAIrB6J,EDtnBN,SAAwBvS,EAAInI,GAC1B,MAAM+a,EAAU,GAChB,KAAO5S,EAAG6S,wBAAwB,CAChC,MAAMvnB,EAAO0U,EAAG6S,uBACZhb,EACEvM,EAAKgX,QAAQzK,IAAW+a,EAAQljB,KAAKpE,GACpCsnB,EAAQljB,KAAKpE,GACpB0U,EAAK1U,CACP,CACA,OAAOsnB,CACT,CC4mBkBE,CAAeR,EAAa,IAAIzR,EAAOyH,4BAA4B,GAC3EzH,EAAO8I,MAAuB,KAAd4I,IAClBA,EAAY7J,EAAOA,EAAOpgB,OAAS,MAIzCogB,EAAOpQ,QAAQ2J,IACbgG,GAAmBhG,EAASA,IAAYqQ,EAAazR,EAAOkS,kBAC5D9K,GAAmBhG,EAASA,IAAYuQ,EAAW3R,EAAOmS,gBAC1D/K,GAAmBhG,EAASA,IAAYsQ,EAAW1R,EAAOoS,kBAE5D5S,EAAO6S,mBACT,EA+NEC,kBAtIF,SAA2BC,GACzB,MAAM/S,EAAStX,KACT0X,EAAYJ,EAAOoK,aAAepK,EAAOI,WAAaJ,EAAOI,WAC7D,SACJwK,EAAQ,OACRpK,EACAoI,YAAaoK,EACbzJ,UAAW0J,EACX1E,UAAW2E,GACTlT,EACJ,IACIuO,EADA3F,EAAcmK,EAElB,MAAMI,EAAsBC,IAC1B,IAAI7J,EAAY6J,EAASpT,EAAOwK,QAAQmD,aAOxC,OANIpE,EAAY,IACdA,EAAYvJ,EAAOwK,QAAQnC,OAAOpgB,OAASshB,GAEzCA,GAAavJ,EAAOwK,QAAQnC,OAAOpgB,SACrCshB,GAAavJ,EAAOwK,QAAQnC,OAAOpgB,QAE9BshB,GAKT,QAH2B,IAAhBX,IACTA,EA/CJ,SAAmC5I,GACjC,MAAM,WACJ6K,EAAU,OACVrK,GACER,EACEI,EAAYJ,EAAOoK,aAAepK,EAAOI,WAAaJ,EAAOI,UACnE,IAAIwI,EACJ,IAAK,IAAI1hB,EAAI,EAAGA,EAAI2jB,EAAW5iB,OAAQf,GAAK,OACT,IAAtB2jB,EAAW3jB,EAAI,GACpBkZ,GAAayK,EAAW3jB,IAAMkZ,EAAYyK,EAAW3jB,EAAI,IAAM2jB,EAAW3jB,EAAI,GAAK2jB,EAAW3jB,IAAM,EACtG0hB,EAAc1hB,EACLkZ,GAAayK,EAAW3jB,IAAMkZ,EAAYyK,EAAW3jB,EAAI,KAClE0hB,EAAc1hB,EAAI,GAEXkZ,GAAayK,EAAW3jB,KACjC0hB,EAAc1hB,GAOlB,OAHIsZ,EAAO6S,sBACLzK,EAAc,QAA4B,IAAhBA,KAA6BA,EAAc,GAEpEA,CACT,CAwBkB0K,CAA0BtT,IAEtC4K,EAASlP,QAAQ0E,IAAc,EACjCmO,EAAY3D,EAASlP,QAAQ0E,OACxB,CACL,MAAMmT,EAAOpS,KAAKE,IAAIb,EAAO4M,mBAAoBxE,GACjD2F,EAAYgF,EAAOpS,KAAK6L,OAAOpE,EAAc2K,GAAQ/S,EAAO2M,eAC9D,CAEA,GADIoB,GAAa3D,EAAS3iB,SAAQsmB,EAAY3D,EAAS3iB,OAAS,GAC5D2gB,IAAgBoK,IAAkBhT,EAAOQ,OAAO8I,KAKlD,YAJIiF,IAAc2E,IAChBlT,EAAOuO,UAAYA,EACnBvO,EAAOoH,KAAK,qBAIhB,GAAIwB,IAAgBoK,GAAiBhT,EAAOQ,OAAO8I,MAAQtJ,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAEjG,YADAzK,EAAOuJ,UAAY4J,EAAoBvK,IAGzC,MAAMmD,EAAc/L,EAAO6I,MAAQrI,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,EAGrE,IAAIS,EACJ,GAAIvJ,EAAOwK,SAAWhK,EAAOgK,QAAQC,SAAWjK,EAAO8I,KACrDC,EAAY4J,EAAoBvK,QAC3B,GAAImD,EAAa,CACtB,MAAMyH,EAAqBxT,EAAOqI,OAAO3Z,KAAKkT,GAAWA,EAAQuH,SAAWP,GAC5E,IAAI6K,EAAmB1a,SAASya,EAAmBE,aAAa,2BAA4B,IACxFne,OAAOrJ,MAAMunB,KACfA,EAAmBtS,KAAKC,IAAIpB,EAAOqI,OAAO3M,QAAQ8X,GAAqB,IAEzEjK,EAAYpI,KAAK6L,MAAMyG,EAAmBjT,EAAOqI,KAAKC,KACxD,MAAO,GAAI9I,EAAOqI,OAAOO,GAAc,CACrC,MAAMkF,EAAa9N,EAAOqI,OAAOO,GAAa8K,aAAa,2BAEzDnK,EADEuE,EACU/U,SAAS+U,EAAY,IAErBlF,CAEhB,MACEW,EAAYX,EAEdrhB,OAAOmG,OAAOsS,EAAQ,CACpBkT,oBACA3E,YACA0E,oBACA1J,YACAyJ,gBACApK,gBAEE5I,EAAO2T,aACTpL,GAAQvI,GAEVA,EAAOoH,KAAK,qBACZpH,EAAOoH,KAAK,oBACRpH,EAAO2T,aAAe3T,EAAOQ,OAAOoT,sBAClCX,IAAsB1J,GACxBvJ,EAAOoH,KAAK,mBAEdpH,EAAOoH,KAAK,eAEhB,EAkDEyM,mBAhDF,SAA4BlU,EAAImU,GAC9B,MAAM9T,EAAStX,KACT8X,EAASR,EAAOQ,OACtB,IAAI6L,EAAQ1M,EAAGoI,QAAQ,IAAIvH,EAAOyH,6BAC7BoE,GAASrM,EAAOgI,WAAa8L,GAAQA,EAAK7rB,OAAS,GAAK6rB,EAAKjO,SAASlG,IACzE,IAAImU,EAAKjV,MAAMiV,EAAKpY,QAAQiE,GAAM,EAAGmU,EAAK7rB,SAASgQ,QAAQ8b,KACpD1H,GAAS0H,EAAO9R,SAAW8R,EAAO9R,QAAQ,IAAIzB,EAAOyH,8BACxDoE,EAAQ0H,KAId,IACIjG,EADAkG,GAAa,EAEjB,GAAI3H,EACF,IAAK,IAAInlB,EAAI,EAAGA,EAAI8Y,EAAOqI,OAAOpgB,OAAQf,GAAK,EAC7C,GAAI8Y,EAAOqI,OAAOnhB,KAAOmlB,EAAO,CAC9B2H,GAAa,EACblG,EAAa5mB,EACb,KACF,CAGJ,IAAImlB,IAAS2H,EAUX,OAFAhU,EAAOiU,kBAAe1nB,OACtByT,EAAOkU,kBAAe3nB,GARtByT,EAAOiU,aAAe5H,EAClBrM,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAC1CzK,EAAOkU,aAAenb,SAASsT,EAAMqH,aAAa,2BAA4B,IAE9E1T,EAAOkU,aAAepG,EAOtBtN,EAAO2T,0BAA+C5nB,IAAxByT,EAAOkU,cAA8BlU,EAAOkU,eAAiBlU,EAAO4I,aACpG5I,EAAOmU,qBAEX,GAgMA,SAASC,GAAelc,GACtB,IAAI,OACF8H,EAAM,aACNqU,EAAY,UACZC,EAAS,KACTC,GACErc,EACJ,MAAM,YACJ0Q,EAAW,cACXoK,GACEhT,EACJ,IAAIa,EAAMyT,EACLzT,IAC8BA,EAA7B+H,EAAcoK,EAAqB,OAAgBpK,EAAcoK,EAAqB,OAAkB,SAE9GhT,EAAOoH,KAAK,aAAamN,KACrBF,GAAwB,UAARxT,EAClBb,EAAOoH,KAAK,uBAAuBmN,KAC1BF,GAAgBzL,IAAgBoK,IACzChT,EAAOoH,KAAK,wBAAwBmN,KACxB,SAAR1T,EACFb,EAAOoH,KAAK,sBAAsBmN,KAElCvU,EAAOoH,KAAK,sBAAsBmN,KAGxC,CAudA,IAAIlI,GAAQ,CACVmI,QAzaF,SAAiB/e,EAAOgL,EAAO4T,EAAcI,EAAUC,QACvC,IAAVjf,IACFA,EAAQ,QAEW,IAAjB4e,IACFA,GAAe,GAEI,iBAAV5e,IACTA,EAAQsD,SAAStD,EAAO,KAE1B,MAAMuK,EAAStX,KACf,IAAIolB,EAAarY,EACbqY,EAAa,IAAGA,EAAa,GACjC,MAAM,OACJtN,EAAM,SACNoK,EAAQ,WACRC,EAAU,cACVmI,EAAa,YACbpK,EACAwB,aAAcC,EAAG,UACjB3J,EAAS,QACT+J,GACEzK,EACJ,IAAKyK,IAAYgK,IAAaC,GAAW1U,EAAO2G,WAAa3G,EAAO2U,WAAanU,EAAOoU,+BACtF,OAAO,OAEY,IAAVnU,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,MAAM8S,EAAOpS,KAAKE,IAAIrB,EAAOQ,OAAO4M,mBAAoBU,GACxD,IAAIS,EAAYgF,EAAOpS,KAAK6L,OAAOc,EAAayF,GAAQvT,EAAOQ,OAAO2M,gBAClEoB,GAAa3D,EAAS3iB,SAAQsmB,EAAY3D,EAAS3iB,OAAS,GAChE,MAAMmY,GAAawK,EAAS2D,GAE5B,GAAI/N,EAAO6S,oBACT,IAAK,IAAInsB,EAAI,EAAGA,EAAI2jB,EAAW5iB,OAAQf,GAAK,EAAG,CAC7C,MAAM2tB,GAAuB1T,KAAK6L,MAAkB,IAAZ5M,GAClC0U,EAAiB3T,KAAK6L,MAAsB,IAAhBnC,EAAW3jB,IACvC6tB,EAAqB5T,KAAK6L,MAA0B,IAApBnC,EAAW3jB,EAAI,SACpB,IAAtB2jB,EAAW3jB,EAAI,GACpB2tB,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9HhH,EAAa5mB,EACJ2tB,GAAuBC,GAAkBD,EAAsBE,IACxEjH,EAAa5mB,EAAI,GAEV2tB,GAAuBC,IAChChH,EAAa5mB,EAEjB,CAGF,GAAI8Y,EAAO2T,aAAe7F,IAAelF,EAAa,CACpD,IAAK5I,EAAOgV,iBAAmB3K,EAAMjK,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAOoQ,eAAiBhQ,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAOoQ,gBAC1J,OAAO,EAET,IAAKpQ,EAAOiV,gBAAkB7U,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAOgR,iBAC1EpI,GAAe,KAAOkF,EACzB,OAAO,CAGb,CAOA,IAAIwG,EANAxG,KAAgBkF,GAAiB,IAAMqB,GACzCrU,EAAOoH,KAAK,0BAIdpH,EAAO6Q,eAAezQ,GAEQkU,EAA1BxG,EAAalF,EAAyB,OAAgBkF,EAAalF,EAAyB,OAAwB,QAGxH,MAAM2B,EAAYvK,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAG1D,KAFyBF,IAAamK,KAEZrK,IAAQjK,IAAcJ,EAAOI,YAAciK,GAAOjK,IAAcJ,EAAOI,WAc/F,OAbAJ,EAAO8S,kBAAkBhF,GAErBtN,EAAOsR,YACT9R,EAAOkP,mBAETlP,EAAO+R,sBACe,UAAlBvR,EAAO6M,QACTrN,EAAOkV,aAAa9U,GAEJ,UAAdkU,IACFtU,EAAOmV,gBAAgBd,EAAcC,GACrCtU,EAAOoV,cAAcf,EAAcC,KAE9B,EAET,GAAI9T,EAAOsL,QAAS,CAClB,MAAMuJ,EAAMrV,EAAO4J,eACbjjB,EAAI0jB,EAAMjK,GAAaA,EAC7B,GAAc,IAAVK,EACE8J,IACFvK,EAAOU,UAAUjE,MAAMkE,eAAiB,OACxCX,EAAOsV,mBAAoB,GAEzB/K,IAAcvK,EAAOuV,2BAA6BvV,EAAOQ,OAAOgV,aAAe,GACjFxV,EAAOuV,2BAA4B,EACnCjX,sBAAsB,KACpBoC,EAAU2U,EAAM,aAAe,aAAe1uB,KAGhD+Z,EAAU2U,EAAM,aAAe,aAAe1uB,EAE5C4jB,GACFjM,sBAAsB,KACpB0B,EAAOU,UAAUjE,MAAMkE,eAAiB,GACxCX,EAAOsV,mBAAoB,QAG1B,CACL,IAAKtV,EAAO4D,QAAQI,aAMlB,OALAjE,EAAqB,CACnBC,SACAC,eAAgBtZ,EAChBuZ,KAAMmV,EAAM,OAAS,SAEhB,EAET3U,EAAUgB,SAAS,CACjB,CAAC2T,EAAM,OAAS,OAAQ1uB,EACxB8uB,SAAU,UAEd,CACA,OAAO,CACT,CACA,MACM/P,EADUF,IACSE,SA0BzB,OAzBI6E,IAAcmK,GAAWhP,GAAY1F,EAAOgI,WAC9ChI,EAAOwK,QAAQhB,QAAO,GAAO,EAAOsE,GAEtC9N,EAAOqP,cAAc5O,GACrBT,EAAOkV,aAAa9U,GACpBJ,EAAO8S,kBAAkBhF,GACzB9N,EAAO+R,sBACP/R,EAAOoH,KAAK,wBAAyB3G,EAAOgU,GAC5CzU,EAAOmV,gBAAgBd,EAAcC,GACvB,IAAV7T,EACFT,EAAOoV,cAAcf,EAAcC,GACzBtU,EAAO2U,YACjB3U,EAAO2U,WAAY,EACd3U,EAAO0V,gCACV1V,EAAO0V,8BAAgC,SAAuBhvB,GACvDsZ,IAAUA,EAAO2G,WAClBjgB,EAAEgS,SAAWhQ,OACjBsX,EAAOU,UAAU5E,oBAAoB,gBAAiBkE,EAAO0V,+BAC7D1V,EAAO0V,8BAAgC,YAChC1V,EAAO0V,8BACd1V,EAAOoV,cAAcf,EAAcC,GACrC,GAEFtU,EAAOU,UAAU7E,iBAAiB,gBAAiBmE,EAAO0V,iCAErD,CACT,EA8QEC,YA5QF,SAAqBlgB,EAAOgL,EAAO4T,EAAcI,QACjC,IAAVhf,IACFA,EAAQ,QAEW,IAAjB4e,IACFA,GAAe,GAEI,iBAAV5e,IAETA,EADsBsD,SAAStD,EAAO,KAGxC,MAAMuK,EAAStX,KACf,GAAIsX,EAAO2G,UAAW,YACD,IAAVlG,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,MAAMsL,EAAc/L,EAAO6I,MAAQ7I,EAAOQ,OAAOqI,MAAQ7I,EAAOQ,OAAOqI,KAAKC,KAAO,EACnF,IAAI8M,EAAWngB,EACf,GAAIuK,EAAOQ,OAAO8I,KAChB,GAAItJ,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAE1CmL,GAAsB5V,EAAOwK,QAAQmD,iBAChC,CACL,IAAIkI,EACJ,GAAI9J,EAAa,CACf,MAAM+B,EAAa8H,EAAW5V,EAAOQ,OAAOqI,KAAKC,KACjD+M,EAAmB7V,EAAOqI,OAAO3Z,KAAKkT,GAA6D,EAAlDA,EAAQ8R,aAAa,6BAAmC5F,GAAY3E,MACvH,MACE0M,EAAmB7V,EAAOuP,oBAAoBqG,GAEhD,MAAME,EAAO/J,EAAc5K,KAAKwH,KAAK3I,EAAOqI,OAAOpgB,OAAS+X,EAAOQ,OAAOqI,KAAKC,MAAQ9I,EAAOqI,OAAOpgB,QAC/F,eACJ4jB,GACE7L,EAAOQ,OACX,IAAI3J,EAAgBmJ,EAAOQ,OAAO3J,cACZ,SAAlBA,EACFA,EAAgBmJ,EAAO0I,wBAEvB7R,EAAgBsK,KAAKwH,KAAKtF,WAAWrD,EAAOQ,OAAO3J,cAAe,KAC9DgV,GAAkBhV,EAAgB,GAAM,IAC1CA,GAAgC,IAGpC,IAAIkf,EAAcD,EAAOD,EAAmBhf,EAO5C,GANIgV,IACFkK,EAAcA,GAAeF,EAAmB1U,KAAKwH,KAAK9R,EAAgB,IAExE4d,GAAY5I,GAAkD,SAAhC7L,EAAOQ,OAAO3J,gBAA6BkV,IAC3EgK,GAAc,GAEZA,EAAa,CACf,MAAMzB,EAAYzI,EAAiBgK,EAAmB7V,EAAO4I,YAAc,OAAS,OAASiN,EAAmB7V,EAAO4I,YAAc,EAAI5I,EAAOQ,OAAO3J,cAAgB,OAAS,OAChLmJ,EAAOgW,QAAQ,CACb1B,YACAE,SAAS,EACTf,iBAAgC,SAAda,EAAuBuB,EAAmB,EAAIA,EAAmBC,EAAO,EAC1FG,eAA8B,SAAd3B,EAAuBtU,EAAOuJ,eAAYhd,GAE9D,CACA,GAAIwf,EAAa,CACf,MAAM+B,EAAa8H,EAAW5V,EAAOQ,OAAOqI,KAAKC,KACjD8M,EAAW5V,EAAOqI,OAAO3Z,KAAKkT,GAA6D,EAAlDA,EAAQ8R,aAAa,6BAAmC5F,GAAY3E,MAC/G,MACEyM,EAAW5V,EAAOuP,oBAAoBqG,EAE1C,CAKF,OAHAtX,sBAAsB,KACpB0B,EAAOwU,QAAQoB,EAAUnV,EAAO4T,EAAcI,KAEzCzU,CACT,EAsMEkW,UAnMF,SAAmBzV,EAAO4T,EAAcI,QACjB,IAAjBJ,IACFA,GAAe,GAEjB,MAAMrU,EAAStX,MACT,QACJ+hB,EAAO,OACPjK,EAAM,UACNmU,GACE3U,EACJ,IAAKyK,GAAWzK,EAAO2G,UAAW,OAAO3G,OACpB,IAAVS,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,IAAI0V,EAAW3V,EAAO2M,eACO,SAAzB3M,EAAO3J,eAAsD,IAA1B2J,EAAO2M,gBAAwB3M,EAAO4V,qBAC3ED,EAAWhV,KAAKC,IAAIpB,EAAO0I,qBAAqB,WAAW,GAAO,IAEpE,MAAM2N,EAAYrW,EAAO4I,YAAcpI,EAAO4M,mBAAqB,EAAI+I,EACjE5L,EAAYvK,EAAOwK,SAAWhK,EAAOgK,QAAQC,QACnD,GAAIjK,EAAO8I,KAAM,CACf,GAAIqL,IAAcpK,GAAa/J,EAAO8V,oBAAqB,OAAO,EAMlE,GALAtW,EAAOgW,QAAQ,CACb1B,UAAW,SAGbtU,EAAOuW,YAAcvW,EAAOU,UAAU8V,WAClCxW,EAAO4I,cAAgB5I,EAAOqI,OAAOpgB,OAAS,GAAKuY,EAAOsL,QAI5D,OAHAxN,sBAAsB,KACpB0B,EAAOwU,QAAQxU,EAAO4I,YAAcyN,EAAW5V,EAAO4T,EAAcI,MAE/D,CAEX,CACA,OAAIjU,EAAO6I,QAAUrJ,EAAOkR,MACnBlR,EAAOwU,QAAQ,EAAG/T,EAAO4T,EAAcI,GAEzCzU,EAAOwU,QAAQxU,EAAO4I,YAAcyN,EAAW5V,EAAO4T,EAAcI,EAC7E,EA8JEgC,UA3JF,SAAmBhW,EAAO4T,EAAcI,QACjB,IAAjBJ,IACFA,GAAe,GAEjB,MAAMrU,EAAStX,MACT,OACJ8X,EAAM,SACNoK,EAAQ,WACRC,EAAU,aACVT,EAAY,QACZK,EAAO,UACPkK,GACE3U,EACJ,IAAKyK,GAAWzK,EAAO2G,UAAW,OAAO3G,OACpB,IAAVS,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,MAAM8J,EAAYvK,EAAOwK,SAAWhK,EAAOgK,QAAQC,QACnD,GAAIjK,EAAO8I,KAAM,CACf,GAAIqL,IAAcpK,GAAa/J,EAAO8V,oBAAqB,OAAO,EAClEtW,EAAOgW,QAAQ,CACb1B,UAAW,SAGbtU,EAAOuW,YAAcvW,EAAOU,UAAU8V,UACxC,CAEA,SAASE,EAAUC,GACjB,OAAIA,EAAM,GAAWxV,KAAK6L,MAAM7L,KAAK+L,IAAIyJ,IAClCxV,KAAK6L,MAAM2J,EACpB,CACA,MAAM9B,EAAsB6B,EALVtM,EAAepK,EAAOI,WAAaJ,EAAOI,WAMtDwW,EAAqBhM,EAAS5E,IAAI2Q,GAAOD,EAAUC,IACnDE,EAAarW,EAAOsW,UAAYtW,EAAOsW,SAASrM,QACtD,IAAIsM,EAAWnM,EAASgM,EAAmBlb,QAAQmZ,GAAuB,GAC1E,QAAwB,IAAbkC,IAA6BvW,EAAOsL,SAAW+K,GAAa,CACrE,IAAIG,EACJpM,EAAS3S,QAAQ,CAACkW,EAAMI,KAClBsG,GAAuB1G,IAEzB6I,EAAgBzI,UAGS,IAAlByI,IACTD,EAAWF,EAAajM,EAASoM,GAAiBpM,EAASoM,EAAgB,EAAIA,EAAgB,EAAIA,GAEvG,CACA,IAAIC,EAAY,EAShB,QARwB,IAAbF,IACTE,EAAYpM,EAAWnP,QAAQqb,GAC3BE,EAAY,IAAGA,EAAYjX,EAAO4I,YAAc,GACvB,SAAzBpI,EAAO3J,eAAsD,IAA1B2J,EAAO2M,gBAAwB3M,EAAO4V,qBAC3Ea,EAAYA,EAAYjX,EAAO0I,qBAAqB,YAAY,GAAQ,EACxEuO,EAAY9V,KAAKC,IAAI6V,EAAW,KAGhCzW,EAAO6I,QAAUrJ,EAAOiR,YAAa,CACvC,MAAMiG,EAAYlX,EAAOQ,OAAOgK,SAAWxK,EAAOQ,OAAOgK,QAAQC,SAAWzK,EAAOwK,QAAUxK,EAAOwK,QAAQnC,OAAOpgB,OAAS,EAAI+X,EAAOqI,OAAOpgB,OAAS,EACvJ,OAAO+X,EAAOwU,QAAQ0C,EAAWzW,EAAO4T,EAAcI,EACxD,CAAO,OAAIjU,EAAO8I,MAA+B,IAAvBtJ,EAAO4I,aAAqBpI,EAAOsL,SAC3DxN,sBAAsB,KACpB0B,EAAOwU,QAAQyC,EAAWxW,EAAO4T,EAAcI,MAE1C,GAEFzU,EAAOwU,QAAQyC,EAAWxW,EAAO4T,EAAcI,EACxD,EA0FE0C,WAvFF,SAAoB1W,EAAO4T,EAAcI,QAClB,IAAjBJ,IACFA,GAAe,GAEjB,MAAMrU,EAAStX,KACf,IAAIsX,EAAO2G,UAIX,YAHqB,IAAVlG,IACTA,EAAQT,EAAOQ,OAAOC,OAEjBT,EAAOwU,QAAQxU,EAAO4I,YAAanI,EAAO4T,EAAcI,EACjE,EA8EE2C,eA3EF,SAAwB3W,EAAO4T,EAAcI,EAAU4C,QAChC,IAAjBhD,IACFA,GAAe,QAEC,IAAdgD,IACFA,EAAY,IAEd,MAAMrX,EAAStX,KACf,GAAIsX,EAAO2G,UAAW,YACD,IAAVlG,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,IAAIhL,EAAQuK,EAAO4I,YACnB,MAAM2K,EAAOpS,KAAKE,IAAIrB,EAAOQ,OAAO4M,mBAAoB3X,GAClD8Y,EAAYgF,EAAOpS,KAAK6L,OAAOvX,EAAQ8d,GAAQvT,EAAOQ,OAAO2M,gBAC7D/M,EAAYJ,EAAOoK,aAAepK,EAAOI,WAAaJ,EAAOI,UACnE,GAAIA,GAAaJ,EAAO4K,SAAS2D,GAAY,CAG3C,MAAM+I,EAActX,EAAO4K,SAAS2D,GAEhCnO,EAAYkX,GADCtX,EAAO4K,SAAS2D,EAAY,GACH+I,GAAeD,IACvD5hB,GAASuK,EAAOQ,OAAO2M,eAE3B,KAAO,CAGL,MAAM4J,EAAW/W,EAAO4K,SAAS2D,EAAY,GAEzCnO,EAAY2W,IADI/W,EAAO4K,SAAS2D,GACOwI,GAAYM,IACrD5hB,GAASuK,EAAOQ,OAAO2M,eAE3B,CAGA,OAFA1X,EAAQ0L,KAAKC,IAAI3L,EAAO,GACxBA,EAAQ0L,KAAKE,IAAI5L,EAAOuK,EAAO6K,WAAW5iB,OAAS,GAC5C+X,EAAOwU,QAAQ/e,EAAOgL,EAAO4T,EAAcI,EACpD,EAwCEN,oBAtCF,WACE,MAAMnU,EAAStX,KACf,GAAIsX,EAAO2G,UAAW,OACtB,MAAM,OACJnG,EAAM,SACN0J,GACElK,EACEnJ,EAAyC,SAAzB2J,EAAO3J,cAA2BmJ,EAAO0I,uBAAyBlI,EAAO3J,cAC/F,IACI0S,EADAgO,EAAevX,EAAOwX,sBAAsBxX,EAAOkU,cAEvD,MAAMuD,EAAgBzX,EAAOgI,UAAY,eAAiB,IAAIxH,EAAOyH,aAC/DyP,EAAS1X,EAAO6I,MAAQ7I,EAAOQ,OAAOqI,MAAQ7I,EAAOQ,OAAOqI,KAAKC,KAAO,EAC9E,GAAItI,EAAO8I,KAAM,CACf,GAAItJ,EAAO2U,UAAW,OACtBpL,EAAYxQ,SAASiH,EAAOiU,aAAaP,aAAa,2BAA4B,IAC9ElT,EAAOqL,eACT7L,EAAO2V,YAAYpM,GACVgO,GAAgBG,GAAU1X,EAAOqI,OAAOpgB,OAAS4O,GAAiB,GAAKmJ,EAAOQ,OAAOqI,KAAKC,KAAO,GAAK9I,EAAOqI,OAAOpgB,OAAS4O,IACtImJ,EAAOgW,UACPuB,EAAevX,EAAO2X,cAAc,EAAgBzN,EAAU,GAAGuN,8BAA0ClO,OAAe,IAC1H,EAAS,KACPvJ,EAAOwU,QAAQ+C,MAGjBvX,EAAOwU,QAAQ+C,EAEnB,MACEvX,EAAOwU,QAAQ+C,EAEnB,GAgUIjO,GAAO,CACTsO,WArTF,SAAoB3B,EAAgBvB,GAClC,MAAM1U,EAAStX,MACT,OACJ8X,EAAM,SACN0J,GACElK,EACJ,IAAKQ,EAAO8I,MAAQtJ,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAAS,OACrE,MAAMwB,EAAa,KACF,EAAgB/B,EAAU,IAAI1J,EAAOyH,4BAC7ChQ,QAAQ,CAAC0H,EAAIlK,KAClBkK,EAAGjD,aAAa,0BAA2BjH,MAazCsW,EAAc/L,EAAO6I,MAAQrI,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,EACjEtI,EAAOqX,qBAAuBrX,EAAO2M,eAAiB,GAAKpB,IAXtC,MACvB,MAAM1D,EAAS,EAAgB6B,EAAU,IAAI1J,EAAOsX,mBACpDzP,EAAOpQ,QAAQ0H,IACbA,EAAGxE,WAEDkN,EAAOpgB,OAAS,IAClB+X,EAAO+X,eACP/X,EAAO8J,iBAKTkO,GAEF,MAAM7K,EAAiB3M,EAAO2M,gBAAkBpB,EAAcvL,EAAOqI,KAAKC,KAAO,GAC3EmP,EAAkBjY,EAAOqI,OAAOpgB,OAASklB,IAAmB,EAC5D+K,EAAiBnM,GAAe/L,EAAOqI,OAAOpgB,OAASuY,EAAOqI,KAAKC,OAAS,EAC5EqP,EAAiBC,IACrB,IAAK,IAAIlxB,EAAI,EAAGA,EAAIkxB,EAAgBlxB,GAAK,EAAG,CAC1C,MAAM0a,EAAU5B,EAAOgI,UAAY,EAAc,eAAgB,CAACxH,EAAOsX,kBAAoB,EAAc,MAAO,CAACtX,EAAOyH,WAAYzH,EAAOsX,kBAC7I9X,EAAOkK,SAAS3O,OAAOqG,EACzB,GAEEqW,GACEzX,EAAOqX,oBAETM,EADoBhL,EAAiBnN,EAAOqI,OAAOpgB,OAASklB,GAE5DnN,EAAO+X,eACP/X,EAAO8J,gBAEP5H,EAAY,mLAEd+J,KACSiM,GACL1X,EAAOqX,oBAETM,EADoB3X,EAAOqI,KAAKC,KAAO9I,EAAOqI,OAAOpgB,OAASuY,EAAOqI,KAAKC,MAE1E9I,EAAO+X,eACP/X,EAAO8J,gBAEP5H,EAAY,8KAEd+J,KAEAA,IAEFjM,EAAOgW,QAAQ,CACbC,iBACA3B,UAAW9T,EAAOqL,oBAAiBtf,EAAY,OAC/CmoB,WAEJ,EAsPEsB,QApPF,SAAiBzR,GACf,IAAI,eACF0R,EAAc,QACdzB,GAAU,EAAI,UACdF,EAAS,aACTY,EAAY,iBACZzB,EAAgB,QAChBiB,EAAO,aACP2D,EAAY,aACZC,QACY,IAAV/T,EAAmB,CAAC,EAAIA,EAC5B,MAAMvE,EAAStX,KACf,IAAKsX,EAAOQ,OAAO8I,KAAM,OACzBtJ,EAAOoH,KAAK,iBACZ,MAAM,OACJiB,EAAM,eACN4M,EAAc,eACdD,EAAc,SACd9K,EAAQ,OACR1J,GACER,GACE,eACJ6L,EAAc,aACd2J,GACEhV,EAGJ,GAFAR,EAAOiV,gBAAiB,EACxBjV,EAAOgV,gBAAiB,EACpBhV,EAAOwK,SAAWhK,EAAOgK,QAAQC,QAanC,OAZI+J,IACGhU,EAAOqL,gBAAuC,IAArB7L,EAAOuO,UAE1B/N,EAAOqL,gBAAkB7L,EAAOuO,UAAY/N,EAAO3J,cAC5DmJ,EAAOwU,QAAQxU,EAAOwK,QAAQnC,OAAOpgB,OAAS+X,EAAOuO,UAAW,GAAG,GAAO,GACjEvO,EAAOuO,YAAcvO,EAAO4K,SAAS3iB,OAAS,GACvD+X,EAAOwU,QAAQxU,EAAOwK,QAAQmD,aAAc,GAAG,GAAO,GAJtD3N,EAAOwU,QAAQxU,EAAOwK,QAAQnC,OAAOpgB,OAAQ,GAAG,GAAO,IAO3D+X,EAAOiV,eAAiBA,EACxBjV,EAAOgV,eAAiBA,OACxBhV,EAAOoH,KAAK,WAGd,IAAIvQ,EAAgB2J,EAAO3J,cACL,SAAlBA,EACFA,EAAgBmJ,EAAO0I,wBAEvB7R,EAAgBsK,KAAKwH,KAAKtF,WAAW7C,EAAO3J,cAAe,KACvDgV,GAAkBhV,EAAgB,GAAM,IAC1CA,GAAgC,IAGpC,MAAMsW,EAAiB3M,EAAO4V,mBAAqBvf,EAAgB2J,EAAO2M,eAC1E,IAAIoL,EAAe1M,EAAiB1K,KAAKC,IAAI+L,EAAgBhM,KAAKwH,KAAK9R,EAAgB,IAAMsW,EACzFoL,EAAepL,IAAmB,IACpCoL,GAAgBpL,EAAiBoL,EAAepL,GAElDoL,GAAgB/X,EAAOgY,qBACvBxY,EAAOuY,aAAeA,EACtB,MAAMxM,EAAc/L,EAAO6I,MAAQrI,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,EACjET,EAAOpgB,OAAS4O,EAAgB0hB,GAAyC,UAAzBvY,EAAOQ,OAAO6M,QAAsBhF,EAAOpgB,OAAS4O,EAA+B,EAAf0hB,EACtHrW,EAAY,4OACH6J,GAAoC,QAArBvL,EAAOqI,KAAK4P,MACpCvW,EAAY,2EAEd,MAAMwW,EAAuB,GACvBC,EAAsB,GACtB7C,EAAO/J,EAAc5K,KAAKwH,KAAKN,EAAOpgB,OAASuY,EAAOqI,KAAKC,MAAQT,EAAOpgB,OAC1E2wB,EAAoBlE,GAAWoB,EAAON,EAAe3e,IAAkBgV,EAC7E,IAAIjD,EAAcgQ,EAAoBpD,EAAexV,EAAO4I,iBAC5B,IAArB6K,EACTA,EAAmBzT,EAAO2X,cAActP,EAAO3Z,KAAKiR,GAAMA,EAAG6C,UAAUmF,SAASnH,EAAOkS,oBAEvF9J,EAAc6K,EAEhB,MAAMoF,EAAuB,SAAdvE,IAAyBA,EAClCwE,EAAuB,SAAdxE,IAAyBA,EACxC,IAAIyE,EAAkB,EAClBC,EAAiB,EACrB,MACMC,GADiBlN,EAAc1D,EAAOoL,GAAkBtK,OAASsK,IACrB5H,QAA0C,IAAjBqJ,GAAgCre,EAAgB,EAAI,GAAM,GAErI,GAAIoiB,EAA0BV,EAAc,CAC1CQ,EAAkB5X,KAAKC,IAAImX,EAAeU,EAAyB9L,GACnE,IAAK,IAAIjmB,EAAI,EAAGA,EAAIqxB,EAAeU,EAAyB/xB,GAAK,EAAG,CAClE,MAAMuO,EAAQvO,EAAIia,KAAK6L,MAAM9lB,EAAI4uB,GAAQA,EACzC,GAAI/J,EAAa,CACf,MAAMmN,EAAoBpD,EAAOrgB,EAAQ,EACzC,IAAK,IAAIvO,EAAImhB,EAAOpgB,OAAS,EAAGf,GAAK,EAAGA,GAAK,EACvCmhB,EAAOnhB,GAAGiiB,SAAW+P,GAAmBR,EAAqBrpB,KAAKnI,EAK1E,MACEwxB,EAAqBrpB,KAAKymB,EAAOrgB,EAAQ,EAE7C,CACF,MAAO,GAAIwjB,EAA0BpiB,EAAgBif,EAAOyC,EAAc,CACxES,EAAiB7X,KAAKC,IAAI6X,GAA2BnD,EAAsB,EAAfyC,GAAmBpL,GAC3EyL,IACFI,EAAiB7X,KAAKC,IAAI4X,EAAgBniB,EAAgBif,EAAON,EAAe,IAElF,IAAK,IAAItuB,EAAI,EAAGA,EAAI8xB,EAAgB9xB,GAAK,EAAG,CAC1C,MAAMuO,EAAQvO,EAAIia,KAAK6L,MAAM9lB,EAAI4uB,GAAQA,EACrC/J,EACF1D,EAAOpQ,QAAQ,CAACoU,EAAOyB,KACjBzB,EAAMlD,SAAW1T,GAAOkjB,EAAoBtpB,KAAKye,KAGvD6K,EAAoBtpB,KAAKoG,EAE7B,CACF,CAsCA,GArCAuK,EAAOmZ,qBAAsB,EAC7B7a,sBAAsB,KACpB0B,EAAOmZ,qBAAsB,IAEF,UAAzBnZ,EAAOQ,OAAO6M,QAAsBhF,EAAOpgB,OAAS4O,EAA+B,EAAf0hB,IAClEI,EAAoB9S,SAAS4N,IAC/BkF,EAAoBxR,OAAOwR,EAAoBjd,QAAQ+X,GAAmB,GAExEiF,EAAqB7S,SAAS4N,IAChCiF,EAAqBvR,OAAOuR,EAAqBhd,QAAQ+X,GAAmB,IAG5EqF,GACFJ,EAAqBzgB,QAAQxC,IAC3B4S,EAAO5S,GAAO2jB,mBAAoB,EAClClP,EAASmP,QAAQhR,EAAO5S,IACxB4S,EAAO5S,GAAO2jB,mBAAoB,IAGlCP,GACFF,EAAoB1gB,QAAQxC,IAC1B4S,EAAO5S,GAAO2jB,mBAAoB,EAClClP,EAAS3O,OAAO8M,EAAO5S,IACvB4S,EAAO5S,GAAO2jB,mBAAoB,IAGtCpZ,EAAO+X,eACsB,SAAzBvX,EAAO3J,cACTmJ,EAAO8J,eACEiC,IAAgB2M,EAAqBzwB,OAAS,GAAK6wB,GAAUH,EAAoB1wB,OAAS,GAAK4wB,IACxG7Y,EAAOqI,OAAOpQ,QAAQ,CAACoU,EAAOyB,KAC5B9N,EAAO6I,KAAKyD,YAAYwB,EAAYzB,EAAOrM,EAAOqI,UAGlD7H,EAAOoO,qBACT5O,EAAO6O,qBAEL2F,EACF,GAAIkE,EAAqBzwB,OAAS,GAAK6wB,GACrC,QAA8B,IAAnB7C,EAAgC,CACzC,MAAMqD,EAAwBtZ,EAAO6K,WAAWjC,GAE1C2Q,EADoBvZ,EAAO6K,WAAWjC,EAAcmQ,GACzBO,EAC7BhB,EACFtY,EAAOkV,aAAalV,EAAOI,UAAYmZ,IAEvCvZ,EAAOwU,QAAQ5L,EAAczH,KAAKwH,KAAKoQ,GAAkB,GAAG,GAAO,GAC/D7D,IACFlV,EAAOwZ,gBAAgBC,eAAiBzZ,EAAOwZ,gBAAgBC,eAAiBF,EAChFvZ,EAAOwZ,gBAAgBE,iBAAmB1Z,EAAOwZ,gBAAgBE,iBAAmBH,GAG1F,MACE,GAAIrE,EAAc,CAChB,MAAMyE,EAAQ5N,EAAc2M,EAAqBzwB,OAASuY,EAAOqI,KAAKC,KAAO4P,EAAqBzwB,OAClG+X,EAAOwU,QAAQxU,EAAO4I,YAAc+Q,EAAO,GAAG,GAAO,GACrD3Z,EAAOwZ,gBAAgBE,iBAAmB1Z,EAAOI,SACnD,OAEG,GAAIuY,EAAoB1wB,OAAS,GAAK4wB,EAC3C,QAA8B,IAAnB5C,EAAgC,CACzC,MAAMqD,EAAwBtZ,EAAO6K,WAAWjC,GAE1C2Q,EADoBvZ,EAAO6K,WAAWjC,EAAcoQ,GACzBM,EAC7BhB,EACFtY,EAAOkV,aAAalV,EAAOI,UAAYmZ,IAEvCvZ,EAAOwU,QAAQ5L,EAAcoQ,EAAgB,GAAG,GAAO,GACnD9D,IACFlV,EAAOwZ,gBAAgBC,eAAiBzZ,EAAOwZ,gBAAgBC,eAAiBF,EAChFvZ,EAAOwZ,gBAAgBE,iBAAmB1Z,EAAOwZ,gBAAgBE,iBAAmBH,GAG1F,KAAO,CACL,MAAMI,EAAQ5N,EAAc4M,EAAoB1wB,OAASuY,EAAOqI,KAAKC,KAAO6P,EAAoB1wB,OAChG+X,EAAOwU,QAAQxU,EAAO4I,YAAc+Q,EAAO,GAAG,GAAO,EACvD,CAKJ,GAFA3Z,EAAOiV,eAAiBA,EACxBjV,EAAOgV,eAAiBA,EACpBhV,EAAO4Z,YAAc5Z,EAAO4Z,WAAWC,UAAYxB,EAAc,CACnE,MAAMyB,EAAa,CACjB7D,iBACA3B,YACAY,eACAzB,mBACA4E,cAAc,GAEZvoB,MAAM4S,QAAQ1C,EAAO4Z,WAAWC,SAClC7Z,EAAO4Z,WAAWC,QAAQ5hB,QAAQ9Q,KAC3BA,EAAEwf,WAAaxf,EAAEqZ,OAAO8I,MAAMniB,EAAE6uB,QAAQ,IACxC8D,EACHtF,QAASrtB,EAAEqZ,OAAO3J,gBAAkB2J,EAAO3J,eAAgB2d,MAGtDxU,EAAO4Z,WAAWC,mBAAmB7Z,EAAOvV,aAAeuV,EAAO4Z,WAAWC,QAAQrZ,OAAO8I,MACrGtJ,EAAO4Z,WAAWC,QAAQ7D,QAAQ,IAC7B8D,EACHtF,QAASxU,EAAO4Z,WAAWC,QAAQrZ,OAAO3J,gBAAkB2J,EAAO3J,eAAgB2d,GAGzF,CACAxU,EAAOoH,KAAK,UACd,EA4BE2S,YA1BF,WACE,MAAM/Z,EAAStX,MACT,OACJ8X,EAAM,SACN0J,GACElK,EACJ,IAAKQ,EAAO8I,OAASY,GAAYlK,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAAS,OAClFzK,EAAO+X,eACP,MAAMiC,EAAiB,GACvBha,EAAOqI,OAAOpQ,QAAQ2J,IACpB,MAAMnM,OAA4C,IAA7BmM,EAAQqY,iBAAqF,EAAlDrY,EAAQ8R,aAAa,2BAAiC9R,EAAQqY,iBAC9HD,EAAevkB,GAASmM,IAE1B5B,EAAOqI,OAAOpQ,QAAQ2J,IACpBA,EAAQ0G,gBAAgB,6BAE1B0R,EAAe/hB,QAAQ2J,IACrBsI,EAAS3O,OAAOqG,KAElB5B,EAAO+X,eACP/X,EAAOwU,QAAQxU,EAAOuJ,UAAW,EACnC,GA6DA,SAAS2Q,GAAiBla,EAAQvI,EAAO0iB,GACvC,MAAM5jB,EAAS,KACT,OACJiK,GACER,EACEoa,EAAqB5Z,EAAO4Z,mBAC5BC,EAAqB7Z,EAAO6Z,mBAClC,OAAID,KAAuBD,GAAUE,GAAsBF,GAAU5jB,EAAO+jB,WAAaD,IAC5D,YAAvBD,IACF3iB,EAAM8iB,kBACC,EAKb,CACA,SAASC,GAAa/iB,GACpB,MAAMuI,EAAStX,KACTqP,EAAW,IACjB,IAAIrR,EAAI+Q,EACJ/Q,EAAE+zB,gBAAe/zB,EAAIA,EAAE+zB,eAC3B,MAAM1sB,EAAOiS,EAAOwZ,gBACpB,GAAe,gBAAX9yB,EAAEg0B,KAAwB,CAC5B,GAAuB,OAAnB3sB,EAAK4sB,WAAsB5sB,EAAK4sB,YAAcj0B,EAAEi0B,UAClD,OAEF5sB,EAAK4sB,UAAYj0B,EAAEi0B,SACrB,KAAsB,eAAXj0B,EAAEg0B,MAAoD,IAA3Bh0B,EAAEk0B,cAAc3yB,SACpD8F,EAAK8sB,QAAUn0B,EAAEk0B,cAAc,GAAGE,YAEpC,GAAe,eAAXp0B,EAAEg0B,KAGJ,YADAR,GAAiBla,EAAQtZ,EAAGA,EAAEk0B,cAAc,GAAGG,OAGjD,MAAM,OACJva,EAAM,QACNwa,EAAO,QACPvQ,GACEzK,EACJ,IAAKyK,EAAS,OACd,IAAKjK,EAAOya,eAAmC,UAAlBv0B,EAAEw0B,YAAyB,OACxD,GAAIlb,EAAO2U,WAAanU,EAAOoU,+BAC7B,QAEG5U,EAAO2U,WAAanU,EAAOsL,SAAWtL,EAAO8I,MAChDtJ,EAAOgW,UAET,IAAImF,EAAWz0B,EAAEgS,OACjB,GAAiC,YAA7B8H,EAAO4a,oBD59Db,SAA0Bzb,EAAIrE,GAC5B,MAAM/E,EAAS,IACf,IAAI8kB,EAAU/f,EAAOqM,SAAShI,GAQ9B,OAPK0b,GAAW9kB,EAAOwL,iBAAmBzG,aAAkByG,kBAE1DsZ,EADiB,IAAI/f,EAAO0G,oBACT6D,SAASlG,GACvB0b,IACHA,EAlBN,SAA8B1b,EAAI2b,GAEhC,MAAMC,EAAgB,CAACD,GACvB,KAAOC,EAActzB,OAAS,GAAG,CAC/B,MAAMuzB,EAAiBD,EAAc5B,QACrC,GAAIha,IAAO6b,EACT,OAAO,EAETD,EAAclsB,QAAQmsB,EAAejf,YAAcif,EAAe3Z,WAAa2Z,EAAe3Z,WAAWtF,SAAW,MAASif,EAAexZ,iBAAmBwZ,EAAexZ,mBAAqB,GACrM,CACF,CAQgByZ,CAAqB9b,EAAIrE,KAGhC+f,CACT,CCk9DSK,CAAiBP,EAAUnb,EAAOU,WAAY,OAErD,GAAI,UAAWha,GAAiB,IAAZA,EAAEi1B,MAAa,OACnC,GAAI,WAAYj1B,GAAKA,EAAEk1B,OAAS,EAAG,OACnC,GAAI7tB,EAAK8tB,WAAa9tB,EAAK+tB,QAAS,OAGpC,MAAMC,IAAyBvb,EAAOwb,gBAA4C,KAA1Bxb,EAAOwb,eAEzDC,EAAYv1B,EAAEw1B,aAAex1B,EAAEw1B,eAAiBx1B,EAAEotB,KACpDiI,GAAwBr1B,EAAEgS,QAAUhS,EAAEgS,OAAOmJ,YAAcoa,IAC7Dd,EAAWc,EAAU,IAEvB,MAAME,EAAoB3b,EAAO2b,kBAAoB3b,EAAO2b,kBAAoB,IAAI3b,EAAOwb,iBACrFI,KAAoB11B,EAAEgS,SAAUhS,EAAEgS,OAAOmJ,YAG/C,GAAIrB,EAAO6b,YAAcD,EAlF3B,SAAwB5kB,EAAU8kB,GAahC,YAZa,IAATA,IACFA,EAAO5zB,MAET,SAAS6zB,EAAc5c,GACrB,IAAKA,GAAMA,IAAO,KAAiBA,IAAO,IAAa,OAAO,KAC1DA,EAAG6c,eAAc7c,EAAKA,EAAG6c,cAC7B,MAAMC,EAAQ9c,EAAGoI,QAAQvQ,GACzB,OAAKilB,GAAU9c,EAAG+c,YAGXD,GAASF,EAAc5c,EAAG+c,cAAc1f,MAFtC,IAGX,CACOuf,CAAcD,EACvB,CAoE4CK,CAAeR,EAAmBhB,GAAYA,EAASpT,QAAQoU,IAEvG,YADAnc,EAAO4c,YAAa,GAGtB,GAAIpc,EAAOqc,eACJ1B,EAASpT,QAAQvH,EAAOqc,cAAe,OAE9C7B,EAAQ8B,SAAWp2B,EAAEq0B,MACrBC,EAAQ+B,SAAWr2B,EAAEs2B,MACrB,MAAM7C,EAASa,EAAQ8B,SACjBG,EAASjC,EAAQ+B,SAIvB,IAAK7C,GAAiBla,EAAQtZ,EAAGyzB,GAC/B,OAEF5yB,OAAOmG,OAAOK,EAAM,CAClB8tB,WAAW,EACXC,SAAS,EACToB,qBAAqB,EACrBC,iBAAa5wB,EACb6wB,iBAAa7wB,IAEfyuB,EAAQb,OAASA,EACjBa,EAAQiC,OAASA,EACjBlvB,EAAKsvB,eAAiB,IACtBrd,EAAO4c,YAAa,EACpB5c,EAAOyJ,aACPzJ,EAAOsd,oBAAiB/wB,EACpBiU,EAAO6W,UAAY,IAAGtpB,EAAKwvB,oBAAqB,GACpD,IAAIhD,GAAiB,EACjBY,EAASlZ,QAAQlU,EAAKyvB,qBACxBjD,GAAiB,EACS,WAAtBY,EAASlf,WACXlO,EAAK8tB,WAAY,IAGjB9jB,EAASgE,eAAiBhE,EAASgE,cAAckG,QAAQlU,EAAKyvB,oBAAsBzlB,EAASgE,gBAAkBof,IAA+B,UAAlBz0B,EAAEw0B,aAA6C,UAAlBx0B,EAAEw0B,cAA4BC,EAASlZ,QAAQlU,EAAKyvB,qBAC/MzlB,EAASgE,cAAcC,OAEzB,MAAMyhB,EAAuBlD,GAAkBva,EAAO0d,gBAAkBld,EAAOmd,0BAC1End,EAAOod,gCAAiCH,GAA0BtC,EAAS0C,mBAC9En3B,EAAE6zB,iBAEA/Z,EAAOsW,UAAYtW,EAAOsW,SAASrM,SAAWzK,EAAO8W,UAAY9W,EAAO2U,YAAcnU,EAAOsL,SAC/F9L,EAAO8W,SAAS0D,eAElBxa,EAAOoH,KAAK,aAAc1gB,EAC5B,CAEA,SAASo3B,GAAYrmB,GACnB,MAAMM,EAAW,IACXiI,EAAStX,KACTqF,EAAOiS,EAAOwZ,iBACd,OACJhZ,EAAM,QACNwa,EACA5Q,aAAcC,EAAG,QACjBI,GACEzK,EACJ,IAAKyK,EAAS,OACd,IAAKjK,EAAOya,eAAuC,UAAtBxjB,EAAMyjB,YAAyB,OAC5D,IAOI6C,EAPAr3B,EAAI+Q,EAER,GADI/Q,EAAE+zB,gBAAe/zB,EAAIA,EAAE+zB,eACZ,gBAAX/zB,EAAEg0B,KAAwB,CAC5B,GAAqB,OAAjB3sB,EAAK8sB,QAAkB,OAE3B,GADWn0B,EAAEi0B,YACF5sB,EAAK4sB,UAAW,MAC7B,CAEA,GAAe,cAAXj0B,EAAEg0B,MAEJ,GADAqD,EAAc,IAAIr3B,EAAEs3B,gBAAgBtvB,KAAK/H,GAAKA,EAAEm0B,aAAe/sB,EAAK8sB,UAC/DkD,GAAeA,EAAYjD,aAAe/sB,EAAK8sB,QAAS,YAE7DkD,EAAcr3B,EAEhB,IAAKqH,EAAK8tB,UAIR,YAHI9tB,EAAKqvB,aAAervB,EAAKovB,aAC3Bnd,EAAOoH,KAAK,oBAAqB1gB,IAIrC,MAAMq0B,EAAQgD,EAAYhD,MACpBiC,EAAQe,EAAYf,MAC1B,GAAIt2B,EAAEu3B,wBAGJ,OAFAjD,EAAQb,OAASY,OACjBC,EAAQiC,OAASD,GAGnB,IAAKhd,EAAO0d,eAaV,OAZKh3B,EAAEgS,OAAOuJ,QAAQlU,EAAKyvB,qBACzBxd,EAAO4c,YAAa,QAElB7uB,EAAK8tB,YACPt0B,OAAOmG,OAAOstB,EAAS,CACrBb,OAAQY,EACRkC,OAAQD,EACRF,SAAU/B,EACVgC,SAAUC,IAEZjvB,EAAKsvB,eAAiB,MAI1B,GAAI7c,EAAO0d,sBAAwB1d,EAAO8I,KACxC,GAAItJ,EAAO6J,cAET,GAAImT,EAAQhC,EAAQiC,QAAUjd,EAAOI,WAAaJ,EAAOgR,gBAAkBgM,EAAQhC,EAAQiC,QAAUjd,EAAOI,WAAaJ,EAAOoQ,eAG9H,OAFAriB,EAAK8tB,WAAY,OACjB9tB,EAAK+tB,SAAU,OAGZ,IAAIzR,IAAQ0Q,EAAQC,EAAQb,SAAWna,EAAOI,WAAaJ,EAAOgR,gBAAkB+J,EAAQC,EAAQb,SAAWna,EAAOI,WAAaJ,EAAOoQ,gBAC/I,OACK,IAAK/F,IAAQ0Q,EAAQC,EAAQb,QAAUna,EAAOI,WAAaJ,EAAOgR,gBAAkB+J,EAAQC,EAAQb,QAAUna,EAAOI,WAAaJ,EAAOoQ,gBAC9I,MACF,CAKF,GAHIrY,EAASgE,eAAiBhE,EAASgE,cAAckG,QAAQlU,EAAKyvB,oBAAsBzlB,EAASgE,gBAAkBrV,EAAEgS,QAA4B,UAAlBhS,EAAEw0B,aAC/HnjB,EAASgE,cAAcC,OAErBjE,EAASgE,eACPrV,EAAEgS,SAAWX,EAASgE,eAAiBrV,EAAEgS,OAAOuJ,QAAQlU,EAAKyvB,mBAG/D,OAFAzvB,EAAK+tB,SAAU,OACf9b,EAAO4c,YAAa,GAIpB7uB,EAAKmvB,qBACPld,EAAOoH,KAAK,YAAa1gB,GAE3Bs0B,EAAQmD,UAAYnD,EAAQ8B,SAC5B9B,EAAQoD,UAAYpD,EAAQ+B,SAC5B/B,EAAQ8B,SAAW/B,EACnBC,EAAQ+B,SAAWC,EACnB,MAAMqB,EAAQrD,EAAQ8B,SAAW9B,EAAQb,OACnCmE,EAAQtD,EAAQ+B,SAAW/B,EAAQiC,OACzC,GAAIjd,EAAOQ,OAAO6W,WAAalW,KAAKod,KAAKF,GAAS,EAAIC,GAAS,GAAKte,EAAOQ,OAAO6W,UAAW,OAC7F,QAAgC,IAArBtpB,EAAKovB,YAA6B,CAC3C,IAAIqB,EACAxe,EAAO4J,gBAAkBoR,EAAQ+B,WAAa/B,EAAQiC,QAAUjd,EAAO6J,cAAgBmR,EAAQ8B,WAAa9B,EAAQb,OACtHpsB,EAAKovB,aAAc,EAGfkB,EAAQA,EAAQC,EAAQA,GAAS,KACnCE,EAA4D,IAA/Crd,KAAKsd,MAAMtd,KAAK+L,IAAIoR,GAAQnd,KAAK+L,IAAImR,IAAgBld,KAAKK,GACvEzT,EAAKovB,YAAcnd,EAAO4J,eAAiB4U,EAAahe,EAAOge,WAAa,GAAKA,EAAahe,EAAOge,WAG3G,CASA,GARIzwB,EAAKovB,aACPnd,EAAOoH,KAAK,oBAAqB1gB,QAEH,IAArBqH,EAAKqvB,cACVpC,EAAQ8B,WAAa9B,EAAQb,QAAUa,EAAQ+B,WAAa/B,EAAQiC,SACtElvB,EAAKqvB,aAAc,IAGnBrvB,EAAKovB,aAA0B,cAAXz2B,EAAEg0B,MAAwB3sB,EAAK2wB,gCAErD,YADA3wB,EAAK8tB,WAAY,GAGnB,IAAK9tB,EAAKqvB,YACR,OAEFpd,EAAO4c,YAAa,GACfpc,EAAOsL,SAAWplB,EAAEi4B,YACvBj4B,EAAE6zB,iBAEA/Z,EAAOoe,2BAA6Bpe,EAAOqe,QAC7Cn4B,EAAEo4B,kBAEJ,IAAIvF,EAAOvZ,EAAO4J,eAAiByU,EAAQC,EACvCS,EAAc/e,EAAO4J,eAAiBoR,EAAQ8B,SAAW9B,EAAQmD,UAAYnD,EAAQ+B,SAAW/B,EAAQoD,UACxG5d,EAAOwe,iBACTzF,EAAOpY,KAAK+L,IAAIqM,IAASlP,EAAM,GAAK,GACpC0U,EAAc5d,KAAK+L,IAAI6R,IAAgB1U,EAAM,GAAK,IAEpD2Q,EAAQzB,KAAOA,EACfA,GAAQ/Y,EAAOye,WACX5U,IACFkP,GAAQA,EACRwF,GAAeA,GAEjB,MAAMG,EAAuBlf,EAAOmf,iBACpCnf,EAAOsd,eAAiB/D,EAAO,EAAI,OAAS,OAC5CvZ,EAAOmf,iBAAmBJ,EAAc,EAAI,OAAS,OACrD,MAAMK,EAASpf,EAAOQ,OAAO8I,OAAS9I,EAAOsL,QACvCuT,EAA2C,SAA5Brf,EAAOmf,kBAA+Bnf,EAAOgV,gBAA8C,SAA5BhV,EAAOmf,kBAA+Bnf,EAAOiV,eACjI,IAAKlnB,EAAK+tB,QAAS,CAQjB,GAPIsD,GAAUC,GACZrf,EAAOgW,QAAQ,CACb1B,UAAWtU,EAAOsd,iBAGtBvvB,EAAK0rB,eAAiBzZ,EAAOsf,eAC7Btf,EAAOqP,cAAc,GACjBrP,EAAO2U,UAAW,CACpB,MAAM4K,EAAM,IAAIhpB,OAAOuH,YAAY,gBAAiB,CAClD0hB,SAAS,EACTb,YAAY,EACZc,OAAQ,CACNC,mBAAmB,KAGvB1f,EAAOU,UAAUif,cAAcJ,EACjC,CACAxxB,EAAK6xB,qBAAsB,GAEvBpf,EAAOqf,aAAyC,IAA1B7f,EAAOgV,iBAAqD,IAA1BhV,EAAOiV,gBACjEjV,EAAO8f,eAAc,GAEvB9f,EAAOoH,KAAK,kBAAmB1gB,EACjC,CAGA,IADA,IAAIwX,MAAO+C,WACmB,IAA1BT,EAAOuf,gBAA4BhyB,EAAK+tB,SAAW/tB,EAAKwvB,oBAAsB2B,IAAyBlf,EAAOmf,kBAAoBC,GAAUC,GAAgBle,KAAK+L,IAAIqM,IAAS,EAUhL,OATAhyB,OAAOmG,OAAOstB,EAAS,CACrBb,OAAQY,EACRkC,OAAQD,EACRF,SAAU/B,EACVgC,SAAUC,EACVvD,eAAgB1rB,EAAK2rB,mBAEvB3rB,EAAKiyB,eAAgB,OACrBjyB,EAAK0rB,eAAiB1rB,EAAK2rB,kBAG7B1Z,EAAOoH,KAAK,aAAc1gB,GAC1BqH,EAAK+tB,SAAU,EACf/tB,EAAK2rB,iBAAmBH,EAAOxrB,EAAK0rB,eACpC,IAAIwG,GAAsB,EACtBC,EAAkB1f,EAAO0f,gBAiD7B,GAhDI1f,EAAO0d,sBACTgC,EAAkB,GAEhB3G,EAAO,GACL6F,GAAUC,GAA8BtxB,EAAKwvB,oBAAsBxvB,EAAK2rB,kBAAoBlZ,EAAOqL,eAAiB7L,EAAOoQ,eAAiBpQ,EAAO8K,gBAAgB9K,EAAO4I,YAAc,IAA+B,SAAzBpI,EAAO3J,eAA4BmJ,EAAOqI,OAAOpgB,OAASuY,EAAO3J,eAAiB,EAAImJ,EAAO8K,gBAAgB9K,EAAO4I,YAAc,GAAK5I,EAAOQ,OAAO5J,aAAe,GAAKoJ,EAAOQ,OAAO5J,aAAeoJ,EAAOoQ,iBAC7YpQ,EAAOgW,QAAQ,CACb1B,UAAW,OACXY,cAAc,EACdzB,iBAAkB,IAGlB1lB,EAAK2rB,iBAAmB1Z,EAAOoQ,iBACjC6P,GAAsB,EAClBzf,EAAO2f,aACTpyB,EAAK2rB,iBAAmB1Z,EAAOoQ,eAAiB,IAAMpQ,EAAOoQ,eAAiBriB,EAAK0rB,eAAiBF,IAAS2G,KAGxG3G,EAAO,IACZ6F,GAAUC,GAA8BtxB,EAAKwvB,oBAAsBxvB,EAAK2rB,kBAAoBlZ,EAAOqL,eAAiB7L,EAAOgR,eAAiBhR,EAAO8K,gBAAgB9K,EAAO8K,gBAAgB7iB,OAAS,GAAK+X,EAAOQ,OAAO5J,cAAyC,SAAzB4J,EAAO3J,eAA4BmJ,EAAOqI,OAAOpgB,OAASuY,EAAO3J,eAAiB,EAAImJ,EAAO8K,gBAAgB9K,EAAO8K,gBAAgB7iB,OAAS,GAAK+X,EAAOQ,OAAO5J,aAAe,GAAKoJ,EAAOgR,iBACnahR,EAAOgW,QAAQ,CACb1B,UAAW,OACXY,cAAc,EACdzB,iBAAkBzT,EAAOqI,OAAOpgB,QAAmC,SAAzBuY,EAAO3J,cAA2BmJ,EAAO0I,uBAAyBvH,KAAKwH,KAAKtF,WAAW7C,EAAO3J,cAAe,QAGvJ9I,EAAK2rB,iBAAmB1Z,EAAOgR,iBACjCiP,GAAsB,EAClBzf,EAAO2f,aACTpyB,EAAK2rB,iBAAmB1Z,EAAOgR,eAAiB,GAAKhR,EAAOgR,eAAiBjjB,EAAK0rB,eAAiBF,IAAS2G,KAI9GD,IACFv5B,EAAEu3B,yBAA0B,IAIzBje,EAAOgV,gBAA4C,SAA1BhV,EAAOsd,gBAA6BvvB,EAAK2rB,iBAAmB3rB,EAAK0rB,iBAC7F1rB,EAAK2rB,iBAAmB3rB,EAAK0rB,iBAE1BzZ,EAAOiV,gBAA4C,SAA1BjV,EAAOsd,gBAA6BvvB,EAAK2rB,iBAAmB3rB,EAAK0rB,iBAC7F1rB,EAAK2rB,iBAAmB3rB,EAAK0rB,gBAE1BzZ,EAAOiV,gBAAmBjV,EAAOgV,iBACpCjnB,EAAK2rB,iBAAmB3rB,EAAK0rB,gBAI3BjZ,EAAO6W,UAAY,EAAG,CACxB,KAAIlW,KAAK+L,IAAIqM,GAAQ/Y,EAAO6W,WAAatpB,EAAKwvB,oBAW5C,YADAxvB,EAAK2rB,iBAAmB3rB,EAAK0rB,gBAT7B,IAAK1rB,EAAKwvB,mBAMR,OALAxvB,EAAKwvB,oBAAqB,EAC1BvC,EAAQb,OAASa,EAAQ8B,SACzB9B,EAAQiC,OAASjC,EAAQ+B,SACzBhvB,EAAK2rB,iBAAmB3rB,EAAK0rB,oBAC7BuB,EAAQzB,KAAOvZ,EAAO4J,eAAiBoR,EAAQ8B,SAAW9B,EAAQb,OAASa,EAAQ+B,SAAW/B,EAAQiC,OAO5G,CACKzc,EAAO4f,eAAgB5f,EAAOsL,WAG/BtL,EAAOsW,UAAYtW,EAAOsW,SAASrM,SAAWzK,EAAO8W,UAAYtW,EAAOoO,uBAC1E5O,EAAO8S,oBACP9S,EAAO+R,uBAELvR,EAAOsW,UAAYtW,EAAOsW,SAASrM,SAAWzK,EAAO8W,UACvD9W,EAAO8W,SAASgH,cAGlB9d,EAAO6Q,eAAe9iB,EAAK2rB,kBAE3B1Z,EAAOkV,aAAannB,EAAK2rB,kBAC3B,CAEA,SAAS2G,GAAW5oB,GAClB,MAAMuI,EAAStX,KACTqF,EAAOiS,EAAOwZ,gBACpB,IAEIuE,EAFAr3B,EAAI+Q,EAIR,GAHI/Q,EAAE+zB,gBAAe/zB,EAAIA,EAAE+zB,eAEK,aAAX/zB,EAAEg0B,MAAkC,gBAAXh0B,EAAEg0B,MAO9C,GADAqD,EAAc,IAAIr3B,EAAEs3B,gBAAgBtvB,KAAK/H,GAAKA,EAAEm0B,aAAe/sB,EAAK8sB,UAC/DkD,GAAeA,EAAYjD,aAAe/sB,EAAK8sB,QAAS,WAN5C,CACjB,GAAqB,OAAjB9sB,EAAK8sB,QAAkB,OAC3B,GAAIn0B,EAAEi0B,YAAc5sB,EAAK4sB,UAAW,OACpCoD,EAAcr3B,CAChB,CAIA,GAAI,CAAC,gBAAiB,aAAc,eAAgB,eAAemf,SAASnf,EAAEg0B,SAC5D,CAAC,gBAAiB,eAAe7U,SAASnf,EAAEg0B,QAAU1a,EAAO8D,QAAQ4B,WAAY1F,EAAO8D,QAAQoC,WAE9G,OAGJnY,EAAK4sB,UAAY,KACjB5sB,EAAK8sB,QAAU,KACf,MAAM,OACJra,EAAM,QACNwa,EACA5Q,aAAcC,EAAG,WACjBQ,EAAU,QACVJ,GACEzK,EACJ,IAAKyK,EAAS,OACd,IAAKjK,EAAOya,eAAmC,UAAlBv0B,EAAEw0B,YAAyB,OAKxD,GAJIntB,EAAKmvB,qBACPld,EAAOoH,KAAK,WAAY1gB,GAE1BqH,EAAKmvB,qBAAsB,GACtBnvB,EAAK8tB,UAMR,OALI9tB,EAAK+tB,SAAWtb,EAAOqf,YACzB7f,EAAO8f,eAAc,GAEvB/xB,EAAK+tB,SAAU,OACf/tB,EAAKqvB,aAAc,GAKjB5c,EAAOqf,YAAc9xB,EAAK+tB,SAAW/tB,EAAK8tB,aAAwC,IAA1B7b,EAAOgV,iBAAqD,IAA1BhV,EAAOiV,iBACnGjV,EAAO8f,eAAc,GAIvB,MAAMQ,EAAe,IACfC,EAAWD,EAAevyB,EAAKsvB,eAGrC,GAAIrd,EAAO4c,WAAY,CACrB,MAAM4D,EAAW95B,EAAEotB,MAAQptB,EAAEw1B,cAAgBx1B,EAAEw1B,eAC/Clc,EAAO6T,mBAAmB2M,GAAYA,EAAS,IAAM95B,EAAEgS,OAAQ8nB,GAC/DxgB,EAAOoH,KAAK,YAAa1gB,GACrB65B,EAAW,KAAOD,EAAevyB,EAAK0yB,cAAgB,KACxDzgB,EAAOoH,KAAK,wBAAyB1gB,EAEzC,CAKA,GAJAqH,EAAK0yB,cAAgB,IACrB,EAAS,KACFzgB,EAAO2G,YAAW3G,EAAO4c,YAAa,MAExC7uB,EAAK8tB,YAAc9tB,EAAK+tB,UAAY9b,EAAOsd,gBAAmC,IAAjBtC,EAAQzB,OAAexrB,EAAKiyB,eAAiBjyB,EAAK2rB,mBAAqB3rB,EAAK0rB,iBAAmB1rB,EAAKiyB,cAIpK,OAHAjyB,EAAK8tB,WAAY,EACjB9tB,EAAK+tB,SAAU,OACf/tB,EAAKqvB,aAAc,GAMrB,IAAIsD,EAMJ,GATA3yB,EAAK8tB,WAAY,EACjB9tB,EAAK+tB,SAAU,EACf/tB,EAAKqvB,aAAc,EAGjBsD,EADElgB,EAAO4f,aACI/V,EAAMrK,EAAOI,WAAaJ,EAAOI,WAEhCrS,EAAK2rB,iBAEjBlZ,EAAOsL,QACT,OAEF,GAAItL,EAAOsW,UAAYtW,EAAOsW,SAASrM,QAIrC,YAHAzK,EAAO8W,SAASuJ,WAAW,CACzBK,eAMJ,MAAMC,EAAcD,IAAe1gB,EAAOgR,iBAAmBhR,EAAOQ,OAAO8I,KAC3E,IAAIsX,EAAY,EACZ/S,EAAY7N,EAAO8K,gBAAgB,GACvC,IAAK,IAAI5jB,EAAI,EAAGA,EAAI2jB,EAAW5iB,OAAQf,GAAKA,EAAIsZ,EAAO4M,mBAAqB,EAAI5M,EAAO2M,eAAgB,CACrG,MAAMkJ,EAAYnvB,EAAIsZ,EAAO4M,mBAAqB,EAAI,EAAI5M,EAAO2M,oBACxB,IAA9BtC,EAAW3jB,EAAImvB,IACpBsK,GAAeD,GAAc7V,EAAW3jB,IAAMw5B,EAAa7V,EAAW3jB,EAAImvB,MAC5EuK,EAAY15B,EACZ2mB,EAAYhD,EAAW3jB,EAAImvB,GAAaxL,EAAW3jB,KAE5Cy5B,GAAeD,GAAc7V,EAAW3jB,MACjD05B,EAAY15B,EACZ2mB,EAAYhD,EAAWA,EAAW5iB,OAAS,GAAK4iB,EAAWA,EAAW5iB,OAAS,GAEnF,CACA,IAAI44B,EAAmB,KACnBC,EAAkB,KAClBtgB,EAAO6I,SACLrJ,EAAOiR,YACT6P,EAAkBtgB,EAAOgK,SAAWhK,EAAOgK,QAAQC,SAAWzK,EAAOwK,QAAUxK,EAAOwK,QAAQnC,OAAOpgB,OAAS,EAAI+X,EAAOqI,OAAOpgB,OAAS,EAChI+X,EAAOkR,QAChB2P,EAAmB,IAIvB,MAAME,GAASL,EAAa7V,EAAW+V,IAAc/S,EAC/CwI,EAAYuK,EAAYpgB,EAAO4M,mBAAqB,EAAI,EAAI5M,EAAO2M,eACzE,GAAIoT,EAAW/f,EAAOwgB,aAAc,CAElC,IAAKxgB,EAAOygB,WAEV,YADAjhB,EAAOwU,QAAQxU,EAAO4I,aAGM,SAA1B5I,EAAOsd,iBACLyD,GAASvgB,EAAO0gB,gBAAiBlhB,EAAOwU,QAAQhU,EAAO6I,QAAUrJ,EAAOkR,MAAQ2P,EAAmBD,EAAYvK,GAAgBrW,EAAOwU,QAAQoM,IAEtH,SAA1B5gB,EAAOsd,iBACLyD,EAAQ,EAAIvgB,EAAO0gB,gBACrBlhB,EAAOwU,QAAQoM,EAAYvK,GACE,OAApByK,GAA4BC,EAAQ,GAAK5f,KAAK+L,IAAI6T,GAASvgB,EAAO0gB,gBAC3ElhB,EAAOwU,QAAQsM,GAEf9gB,EAAOwU,QAAQoM,GAGrB,KAAO,CAEL,IAAKpgB,EAAO2gB,YAEV,YADAnhB,EAAOwU,QAAQxU,EAAO4I,cAGE5I,EAAOohB,YAAe16B,EAAEgS,SAAWsH,EAAOohB,WAAWC,QAAU36B,EAAEgS,SAAWsH,EAAOohB,WAAWE,QAExF,SAA1BthB,EAAOsd,gBACTtd,EAAOwU,QAA6B,OAArBqM,EAA4BA,EAAmBD,EAAYvK,GAE9C,SAA1BrW,EAAOsd,gBACTtd,EAAOwU,QAA4B,OAApBsM,EAA2BA,EAAkBF,IAErDl6B,EAAEgS,SAAWsH,EAAOohB,WAAWC,OACxCrhB,EAAOwU,QAAQoM,EAAYvK,GAE3BrW,EAAOwU,QAAQoM,EAEnB,CACF,CAEA,SAASW,KACP,MAAMvhB,EAAStX,MACT,OACJ8X,EAAM,GACNb,GACEK,EACJ,GAAIL,GAAyB,IAAnBA,EAAG2D,YAAmB,OAG5B9C,EAAO4L,aACTpM,EAAOwhB,gBAIT,MAAM,eACJxM,EAAc,eACdC,EAAc,SACdrK,GACE5K,EACEuK,EAAYvK,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAG1DzK,EAAOgV,gBAAiB,EACxBhV,EAAOiV,gBAAiB,EACxBjV,EAAOyJ,aACPzJ,EAAO8J,eACP9J,EAAO+R,sBACP,MAAM0P,EAAgBlX,GAAa/J,EAAO8I,OACZ,SAAzB9I,EAAO3J,eAA4B2J,EAAO3J,cAAgB,KAAMmJ,EAAOkR,OAAUlR,EAAOiR,aAAgBjR,EAAOQ,OAAOqL,gBAAmB4V,EAGxIzhB,EAAOQ,OAAO8I,OAASiB,EACzBvK,EAAO2V,YAAY3V,EAAOuJ,UAAW,GAAG,GAAO,GAE/CvJ,EAAOwU,QAAQxU,EAAO4I,YAAa,GAAG,GAAO,GAL/C5I,EAAOwU,QAAQxU,EAAOqI,OAAOpgB,OAAS,EAAG,GAAG,GAAO,GAQjD+X,EAAO0hB,UAAY1hB,EAAO0hB,SAASC,SAAW3hB,EAAO0hB,SAASE,SAChExjB,aAAa4B,EAAO0hB,SAASG,eAC7B7hB,EAAO0hB,SAASG,cAAgB9mB,WAAW,KACrCiF,EAAO0hB,UAAY1hB,EAAO0hB,SAASC,SAAW3hB,EAAO0hB,SAASE,QAChE5hB,EAAO0hB,SAASI,UAEjB,MAGL9hB,EAAOiV,eAAiBA,EACxBjV,EAAOgV,eAAiBA,EACpBhV,EAAOQ,OAAOkO,eAAiB9D,IAAa5K,EAAO4K,UACrD5K,EAAO2O,eAEX,CAEA,SAASoT,GAAQr7B,GACf,MAAMsZ,EAAStX,KACVsX,EAAOyK,UACPzK,EAAO4c,aACN5c,EAAOQ,OAAOwhB,eAAet7B,EAAE6zB,iBAC/Bva,EAAOQ,OAAOyhB,0BAA4BjiB,EAAO2U,YACnDjuB,EAAEo4B,kBACFp4B,EAAEw7B,6BAGR,CAEA,SAASC,KACP,MAAMniB,EAAStX,MACT,UACJgY,EAAS,aACT0J,EAAY,QACZK,GACEzK,EACJ,IAAKyK,EAAS,OAWd,IAAI2X,EAVJpiB,EAAOqiB,kBAAoBriB,EAAOI,UAC9BJ,EAAO4J,eACT5J,EAAOI,WAAaM,EAAU4hB,WAE9BtiB,EAAOI,WAAaM,EAAU6hB,UAGP,IAArBviB,EAAOI,YAAiBJ,EAAOI,UAAY,GAC/CJ,EAAO8S,oBACP9S,EAAO+R,sBAEP,MAAMhB,EAAiB/Q,EAAOgR,eAAiBhR,EAAOoQ,eAEpDgS,EADqB,IAAnBrR,EACY,GAEC/Q,EAAOI,UAAYJ,EAAOoQ,gBAAkBW,EAEzDqR,IAAgBpiB,EAAOkB,UACzBlB,EAAO6Q,eAAezG,GAAgBpK,EAAOI,UAAYJ,EAAOI,WAElEJ,EAAOoH,KAAK,eAAgBpH,EAAOI,WAAW,EAChD,CAEA,SAASoiB,GAAO97B,GACd,MAAMsZ,EAAStX,KACfmf,GAAqB7H,EAAQtZ,EAAEgS,QAC3BsH,EAAOQ,OAAOsL,SAA2C,SAAhC9L,EAAOQ,OAAO3J,gBAA6BmJ,EAAOQ,OAAOsR,YAGtF9R,EAAOwJ,QACT,CAEA,SAASiZ,KACP,MAAMziB,EAAStX,KACXsX,EAAO0iB,gCACX1iB,EAAO0iB,+BAAgC,EACnC1iB,EAAOQ,OAAO0d,sBAChBle,EAAOL,GAAGlD,MAAMkmB,YAAc,QAElC,CAEA,MAAMpc,GAAS,CAACvG,EAAQ4G,KACtB,MAAM7O,EAAW,KACX,OACJyI,EAAM,GACNb,EAAE,UACFe,EAAS,OACTgE,GACE1E,EACE4iB,IAAYpiB,EAAOqe,OACnBgE,EAAuB,OAAXjc,EAAkB,mBAAqB,sBACnDkc,EAAelc,EAChBjH,GAAoB,iBAAPA,IAGlB5H,EAAS8qB,GAAW,aAAc7iB,EAAOyiB,qBAAsB,CAC7DM,SAAS,EACTH,YAEFjjB,EAAGkjB,GAAW,aAAc7iB,EAAOwa,aAAc,CAC/CuI,SAAS,IAEXpjB,EAAGkjB,GAAW,cAAe7iB,EAAOwa,aAAc,CAChDuI,SAAS,IAEXhrB,EAAS8qB,GAAW,YAAa7iB,EAAO8d,YAAa,CACnDiF,SAAS,EACTH,YAEF7qB,EAAS8qB,GAAW,cAAe7iB,EAAO8d,YAAa,CACrDiF,SAAS,EACTH,YAEF7qB,EAAS8qB,GAAW,WAAY7iB,EAAOqgB,WAAY,CACjD0C,SAAS,IAEXhrB,EAAS8qB,GAAW,YAAa7iB,EAAOqgB,WAAY,CAClD0C,SAAS,IAEXhrB,EAAS8qB,GAAW,gBAAiB7iB,EAAOqgB,WAAY,CACtD0C,SAAS,IAEXhrB,EAAS8qB,GAAW,cAAe7iB,EAAOqgB,WAAY,CACpD0C,SAAS,IAEXhrB,EAAS8qB,GAAW,aAAc7iB,EAAOqgB,WAAY,CACnD0C,SAAS,IAEXhrB,EAAS8qB,GAAW,eAAgB7iB,EAAOqgB,WAAY,CACrD0C,SAAS,IAEXhrB,EAAS8qB,GAAW,cAAe7iB,EAAOqgB,WAAY,CACpD0C,SAAS,KAIPviB,EAAOwhB,eAAiBxhB,EAAOyhB,2BACjCtiB,EAAGkjB,GAAW,QAAS7iB,EAAO+hB,SAAS,GAErCvhB,EAAOsL,SACTpL,EAAUmiB,GAAW,SAAU7iB,EAAOmiB,UAIpC3hB,EAAOwiB,qBACThjB,EAAO8iB,GAAcpe,EAAOC,KAAOD,EAAOE,QAAU,0CAA4C,wBAAyB2c,IAAU,GAEnIvhB,EAAO8iB,GAAc,iBAAkBvB,IAAU,GAInD5hB,EAAGkjB,GAAW,OAAQ7iB,EAAOwiB,OAAQ,CACnCI,SAAS,MA4BPK,GAAgB,CAACjjB,EAAQQ,IACtBR,EAAO6I,MAAQrI,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,EAsO1D,IAIIoa,GAAW,CACbC,MAAM,EACN7O,UAAW,aACX0K,gBAAgB,EAChBoE,sBAAuB,mBACvBhI,kBAAmB,UACnB5F,aAAc,EACd/U,MAAO,IACPqL,SAAS,EACTkX,sBAAsB,EACtBK,gBAAgB,EAChBxE,QAAQ,EACRyE,gBAAgB,EAChBC,aAAc,SACd9Y,SAAS,EACT+S,kBAAmB,wDAEnB5iB,MAAO,KACPmK,OAAQ,KAER6P,gCAAgC,EAEhCxe,UAAW,KACXoE,IAAK,KAEL4f,oBAAoB,EACpBC,mBAAoB,GAEpBvI,YAAY,EAEZxE,gBAAgB,EAEhBkW,kBAAkB,EAElBnW,OAAQ,QAIRjB,iBAAa7f,EACbk3B,gBAAiB,SAEjB7sB,aAAc,EACdC,cAAe,EACfsW,eAAgB,EAChBC,mBAAoB,EACpBgJ,oBAAoB,EACpBvK,gBAAgB,EAChBkC,sBAAsB,EACtB/C,mBAAoB,EAEpBE,kBAAmB,EAEnBmI,qBAAqB,EACrBjF,0BAA0B,EAE1BM,eAAe,EAEf9B,cAAc,EAEdqS,WAAY,EACZT,WAAY,GACZvD,eAAe,EACfkG,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBF,aAAc,IACdZ,cAAc,EACd1C,gBAAgB,EAChBrG,UAAW,EACXuH,0BAA0B,EAC1BjB,0BAA0B,EAC1BC,+BAA+B,EAC/BM,qBAAqB,EAErBwF,mBAAmB,EAEnBvD,YAAY,EACZD,gBAAiB,IAEjBtR,qBAAqB,EAErBiR,YAAY,EAEZmC,eAAe,EACfC,0BAA0B,EAC1B9N,qBAAqB,EAErB7K,MAAM,EACNuO,oBAAoB,EACpBW,qBAAsB,EACtBlC,qBAAqB,EAErBjN,QAAQ,EAER4L,gBAAgB,EAChBD,gBAAgB,EAChB6H,aAAc,KAEdR,WAAW,EACXL,eAAgB,oBAChBG,kBAAmB,KAEnBwH,kBAAkB,EAClB1U,wBAAyB,GAEzBF,uBAAwB,UAExB9G,WAAY,eACZ6P,gBAAiB,qBACjBpF,iBAAkB,sBAClBhC,kBAAmB,uBACnBC,uBAAwB,6BACxBgC,eAAgB,oBAChBC,eAAgB,oBAChBgR,aAAc,iBACdzb,mBAAoB,wBACpBM,oBAAqB,EAErBmL,oBAAoB,EAEpBiQ,cAAc,GAGhB,SAASC,GAAmBtjB,EAAQujB,GAClC,OAAO,SAAsBl3B,QACf,IAARA,IACFA,EAAM,CAAC,GAET,MAAMm3B,EAAkBz8B,OAAOkE,KAAKoB,GAAK,GACnCo3B,EAAep3B,EAAIm3B,GACG,iBAAjBC,GAA8C,OAAjBA,IAIR,IAA5BzjB,EAAOwjB,KACTxjB,EAAOwjB,GAAmB,CACxBvZ,SAAS,IAGW,eAApBuZ,GAAoCxjB,EAAOwjB,IAAoBxjB,EAAOwjB,GAAiBvZ,UAAYjK,EAAOwjB,GAAiB1C,SAAW9gB,EAAOwjB,GAAiB3C,SAChK7gB,EAAOwjB,GAAiBE,MAAO,GAE7B,CAAC,aAAc,aAAaxoB,QAAQsoB,IAAoB,GAAKxjB,EAAOwjB,IAAoBxjB,EAAOwjB,GAAiBvZ,UAAYjK,EAAOwjB,GAAiBrkB,KACtJa,EAAOwjB,GAAiBE,MAAO,GAE3BF,KAAmBxjB,GAAU,YAAayjB,GAIT,iBAA5BzjB,EAAOwjB,IAAmC,YAAaxjB,EAAOwjB,KACvExjB,EAAOwjB,GAAiBvZ,SAAU,GAE/BjK,EAAOwjB,KAAkBxjB,EAAOwjB,GAAmB,CACtDvZ,SAAS,IAEX,EAAOsZ,EAAkBl3B,IATvB,EAAOk3B,EAAkBl3B,IAfzB,EAAOk3B,EAAkBl3B,EAyB7B,CACF,CAGA,MAAMs3B,GAAa,CACjB7d,gBACAkD,UACApJ,UA1/Dc,CACdkf,aAlKF,SAA4B8E,QACb,IAATA,IACFA,EAAO17B,KAAKkhB,eAAiB,IAAM,KAErC,MACM,OACJpJ,EACA4J,aAAcC,EAAG,UACjBjK,EAAS,UACTM,GALahY,KAOf,GAAI8X,EAAOgjB,iBACT,OAAOnZ,GAAOjK,EAAYA,EAE5B,GAAII,EAAOsL,QACT,OAAO1L,EAET,IAAIsZ,EDhkCN,SAAsB/Z,EAAIykB,QACX,IAATA,IACFA,EAAO,KAET,MAAM7tB,EAAS,IACf,IAAI8tB,EACAC,EACAC,EACJ,MAAMC,EAtBR,SAA0B7kB,GACxB,MAAMpJ,EAAS,IACf,IAAIkG,EAUJ,OATIlG,EAAOwH,mBACTtB,EAAQlG,EAAOwH,iBAAiB4B,EAAI,QAEjClD,GAASkD,EAAG8kB,eACfhoB,EAAQkD,EAAG8kB,cAERhoB,IACHA,EAAQkD,EAAGlD,OAENA,CACT,CASmB,CAAiBkD,GA6BlC,OA5BIpJ,EAAOmuB,iBACTJ,EAAeE,EAAS/X,WAAa+X,EAAS7X,gBAC1C2X,EAAa1hB,MAAM,KAAK3a,OAAS,IACnCq8B,EAAeA,EAAa1hB,MAAM,MAAMoD,IAAIle,GAAKA,EAAEyjB,QAAQ,IAAK,MAAMoZ,KAAK,OAI7EJ,EAAkB,IAAIhuB,EAAOmuB,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAE5EC,EAAkBC,EAASI,cAAgBJ,EAASK,YAAcL,EAASM,aAAeN,EAASO,aAAeP,EAAS/X,WAAa+X,EAASxmB,iBAAiB,aAAauN,QAAQ,aAAc,sBACrM8Y,EAASE,EAAgB3lB,WAAWgE,MAAM,MAE/B,MAATwhB,IAE0BE,EAAxB/tB,EAAOmuB,gBAAgCH,EAAgBS,IAEhC,KAAlBX,EAAOp8B,OAA8Bob,WAAWghB,EAAO,KAE5ChhB,WAAWghB,EAAO,KAE3B,MAATD,IAE0BE,EAAxB/tB,EAAOmuB,gBAAgCH,EAAgBU,IAEhC,KAAlBZ,EAAOp8B,OAA8Bob,WAAWghB,EAAO,KAE5ChhB,WAAWghB,EAAO,KAEjCC,GAAgB,CACzB,CC0hCyB,CAAa5jB,EAAW0jB,GAG/C,OAFA1K,GAdehxB,KAcYonB,wBACvBzF,IAAKqP,GAAoBA,GACtBA,GAAoB,CAC7B,EA8IExE,aA5IF,SAAsB9U,EAAWiY,GAC/B,MAAMrY,EAAStX,MAEb0hB,aAAcC,EAAG,OACjB7J,EAAM,UACNE,EAAS,SACTQ,GACElB,EACJ,IA0BIoiB,EA1BA8C,EAAI,EACJv9B,EAAI,EAEJqY,EAAO4J,eACTsb,EAAI7a,GAAOjK,EAAYA,EAEvBzY,EAAIyY,EAEFI,EAAOoM,eACTsY,EAAI/jB,KAAK6L,MAAMkY,GACfv9B,EAAIwZ,KAAK6L,MAAMrlB,IAEjBqY,EAAOqiB,kBAAoBriB,EAAOI,UAClCJ,EAAOI,UAAYJ,EAAO4J,eAAiBsb,EAAIv9B,EAC3C6Y,EAAOsL,QACTpL,EAAUV,EAAO4J,eAAiB,aAAe,aAAe5J,EAAO4J,gBAAkBsb,GAAKv9B,EACpF6Y,EAAOgjB,mBACbxjB,EAAO4J,eACTsb,GAAKllB,EAAO8P,wBAEZnoB,GAAKqY,EAAO8P,wBAEdpP,EAAUjE,MAAMgQ,UAAY,eAAeyY,QAAQv9B,aAKrD,MAAMopB,EAAiB/Q,EAAOgR,eAAiBhR,EAAOoQ,eAEpDgS,EADqB,IAAnBrR,EACY,GAEC3Q,EAAYJ,EAAOoQ,gBAAkBW,EAElDqR,IAAgBlhB,GAClBlB,EAAO6Q,eAAezQ,GAExBJ,EAAOoH,KAAK,eAAgBpH,EAAOI,UAAWiY,EAChD,EAgGEjI,aA9FF,WACE,OAAQ1nB,KAAKkiB,SAAS,EACxB,EA6FEoG,aA3FF,WACE,OAAQtoB,KAAKkiB,SAASliB,KAAKkiB,SAAS3iB,OAAS,EAC/C,EA0FEk9B,YAxFF,SAAqB/kB,EAAWK,EAAO4T,EAAc+Q,EAAiB3Q,QAClD,IAAdrU,IACFA,EAAY,QAEA,IAAVK,IACFA,EAAQ/X,KAAK8X,OAAOC,YAED,IAAjB4T,IACFA,GAAe,QAEO,IAApB+Q,IACFA,GAAkB,GAEpB,MAAMplB,EAAStX,MACT,OACJ8X,EAAM,UACNE,GACEV,EACJ,GAAIA,EAAO2U,WAAanU,EAAOoU,+BAC7B,OAAO,EAET,MAAMxE,EAAepQ,EAAOoQ,eACtBY,EAAehR,EAAOgR,eAC5B,IAAIqU,EAKJ,GAJiDA,EAA7CD,GAAmBhlB,EAAYgQ,EAA6BA,EAAsBgV,GAAmBhlB,EAAY4Q,EAA6BA,EAAiC5Q,EAGnLJ,EAAO6Q,eAAewU,GAClB7kB,EAAOsL,QAAS,CAClB,MAAMuJ,EAAMrV,EAAO4J,eACnB,GAAc,IAAVnJ,EACFC,EAAU2U,EAAM,aAAe,cAAgBgQ,MAC1C,CACL,IAAKrlB,EAAO4D,QAAQI,aAMlB,OALAjE,EAAqB,CACnBC,SACAC,gBAAiBolB,EACjBnlB,KAAMmV,EAAM,OAAS,SAEhB,EAET3U,EAAUgB,SAAS,CACjB,CAAC2T,EAAM,OAAS,QAASgQ,EACzB5P,SAAU,UAEd,CACA,OAAO,CACT,CAiCA,OAhCc,IAAVhV,GACFT,EAAOqP,cAAc,GACrBrP,EAAOkV,aAAamQ,GAChBhR,IACFrU,EAAOoH,KAAK,wBAAyB3G,EAAOgU,GAC5CzU,EAAOoH,KAAK,oBAGdpH,EAAOqP,cAAc5O,GACrBT,EAAOkV,aAAamQ,GAChBhR,IACFrU,EAAOoH,KAAK,wBAAyB3G,EAAOgU,GAC5CzU,EAAOoH,KAAK,oBAETpH,EAAO2U,YACV3U,EAAO2U,WAAY,EACd3U,EAAOslB,oCACVtlB,EAAOslB,kCAAoC,SAAuB5+B,GAC3DsZ,IAAUA,EAAO2G,WAClBjgB,EAAEgS,SAAWhQ,OACjBsX,EAAOU,UAAU5E,oBAAoB,gBAAiBkE,EAAOslB,mCAC7DtlB,EAAOslB,kCAAoC,YACpCtlB,EAAOslB,kCACdtlB,EAAO2U,WAAY,EACfN,GACFrU,EAAOoH,KAAK,iBAEhB,GAEFpH,EAAOU,UAAU7E,iBAAiB,gBAAiBmE,EAAOslB,sCAGvD,CACT,GA6/DEC,WAv6De,CACflW,cA7EF,SAAuB9O,EAAU8X,GAC/B,MAAMrY,EAAStX,KACVsX,EAAOQ,OAAOsL,UACjB9L,EAAOU,UAAUjE,MAAM+oB,mBAAqB,GAAGjlB,MAC/CP,EAAOU,UAAUjE,MAAMgpB,gBAA+B,IAAbllB,EAAiB,MAAQ,IAEpEP,EAAOoH,KAAK,gBAAiB7G,EAAU8X,EACzC,EAuEElD,gBAzCF,SAAyBd,EAAcC,QAChB,IAAjBD,IACFA,GAAe,GAEjB,MAAMrU,EAAStX,MACT,OACJ8X,GACER,EACAQ,EAAOsL,UACPtL,EAAOsR,YACT9R,EAAOkP,mBAETkF,GAAe,CACbpU,SACAqU,eACAC,YACAC,KAAM,UAEV,EAwBEa,cAtBF,SAAuBf,EAAcC,QACd,IAAjBD,IACFA,GAAe,GAEjB,MAAMrU,EAAStX,MACT,OACJ8X,GACER,EACJA,EAAO2U,WAAY,EACfnU,EAAOsL,UACX9L,EAAOqP,cAAc,GACrB+E,GAAe,CACbpU,SACAqU,eACAC,YACAC,KAAM,QAEV,GA06DElI,SACA/C,QACAuW,WAxpCe,CACfC,cAjCF,SAAuB4F,GACrB,MAAM1lB,EAAStX,KACf,IAAKsX,EAAOQ,OAAOya,eAAiBjb,EAAOQ,OAAOkO,eAAiB1O,EAAO2lB,UAAY3lB,EAAOQ,OAAOsL,QAAS,OAC7G,MAAMnM,EAAyC,cAApCK,EAAOQ,OAAO4a,kBAAoCpb,EAAOL,GAAKK,EAAOU,UAC5EV,EAAOgI,YACThI,EAAOmZ,qBAAsB,GAE/BxZ,EAAGlD,MAAMmpB,OAAS,OAClBjmB,EAAGlD,MAAMmpB,OAASF,EAAS,WAAa,OACpC1lB,EAAOgI,WACT1J,sBAAsB,KACpB0B,EAAOmZ,qBAAsB,GAGnC,EAoBE0M,gBAlBF,WACE,MAAM7lB,EAAStX,KACXsX,EAAOQ,OAAOkO,eAAiB1O,EAAO2lB,UAAY3lB,EAAOQ,OAAOsL,UAGhE9L,EAAOgI,YACThI,EAAOmZ,qBAAsB,GAE/BnZ,EAA2C,cAApCA,EAAOQ,OAAO4a,kBAAoC,KAAO,aAAa3e,MAAMmpB,OAAS,GACxF5lB,EAAOgI,WACT1J,sBAAsB,KACpB0B,EAAOmZ,qBAAsB,IAGnC,GA2pCE5S,OAxZa,CACbuf,aArBF,WACE,MAAM9lB,EAAStX,MACT,OACJ8X,GACER,EACJA,EAAOwa,aAAeA,GAAaxyB,KAAKgY,GACxCA,EAAO8d,YAAcA,GAAY91B,KAAKgY,GACtCA,EAAOqgB,WAAaA,GAAWr4B,KAAKgY,GACpCA,EAAOyiB,qBAAuBA,GAAqBz6B,KAAKgY,GACpDQ,EAAOsL,UACT9L,EAAOmiB,SAAWA,GAASn6B,KAAKgY,IAElCA,EAAO+hB,QAAUA,GAAQ/5B,KAAKgY,GAC9BA,EAAOwiB,OAASA,GAAOx6B,KAAKgY,GAC5BuG,GAAOvG,EAAQ,KACjB,EAOE+lB,aANF,WAEExf,GADe7d,KACA,MACjB,GA0ZE0jB,YAlRgB,CAChBoV,cAhIF,WACE,MAAMxhB,EAAStX,MACT,UACJ6gB,EAAS,YACToK,EAAW,OACXnT,EAAM,GACNb,GACEK,EACEoM,EAAc5L,EAAO4L,YAC3B,IAAKA,GAAeA,GAAmD,IAApC7kB,OAAOkE,KAAK2gB,GAAankB,OAAc,OAC1E,MAAM8P,EAAW,IAGX0rB,EAA6C,WAA3BjjB,EAAOijB,iBAAiCjjB,EAAOijB,gBAA2C,YAAzBjjB,EAAOijB,gBAC1FuC,EAAsB,CAAC,SAAU,aAAangB,SAASrF,EAAOijB,mBAAqBjjB,EAAOijB,gBAAkBzjB,EAAOL,GAAK5H,EAASmE,cAAcsE,EAAOijB,iBACtJwC,EAAajmB,EAAOkmB,cAAc9Z,EAAaqX,EAAiBuC,GACtE,IAAKC,GAAcjmB,EAAOmmB,oBAAsBF,EAAY,OAC5D,MACMG,GADuBH,KAAc7Z,EAAcA,EAAY6Z,QAAc15B,IAClCyT,EAAOqmB,eAClDC,EAAcrD,GAAcjjB,EAAQQ,GACpC+lB,EAAatD,GAAcjjB,EAAQomB,GACnCI,EAAgBxmB,EAAOQ,OAAOqf,WAC9B4G,EAAeL,EAAiBvG,WAChC6G,EAAalmB,EAAOiK,QACtB6b,IAAgBC,GAClB5mB,EAAG6C,UAAUrH,OAAO,GAAGqF,EAAOuO,6BAA8B,GAAGvO,EAAOuO,qCACtE/O,EAAO2mB,yBACGL,GAAeC,IACzB5mB,EAAG6C,UAAUC,IAAI,GAAGjC,EAAOuO,+BACvBqX,EAAiBvd,KAAK4P,MAAuC,WAA/B2N,EAAiBvd,KAAK4P,OAAsB2N,EAAiBvd,KAAK4P,MAA6B,WAArBjY,EAAOqI,KAAK4P,OACtH9Y,EAAG6C,UAAUC,IAAI,GAAGjC,EAAOuO,qCAE7B/O,EAAO2mB,wBAELH,IAAkBC,EACpBzmB,EAAO6lB,mBACGW,GAAiBC,GAC3BzmB,EAAO8f,gBAIT,CAAC,aAAc,aAAc,aAAa7nB,QAAQnL,IAChD,QAAsC,IAA3Bs5B,EAAiBt5B,GAAuB,OACnD,MAAM85B,EAAmBpmB,EAAO1T,IAAS0T,EAAO1T,GAAM2d,QAChDoc,EAAkBT,EAAiBt5B,IAASs5B,EAAiBt5B,GAAM2d,QACrEmc,IAAqBC,GACvB7mB,EAAOlT,GAAMg6B,WAEVF,GAAoBC,GACvB7mB,EAAOlT,GAAMi6B,WAGjB,MAAMC,EAAmBZ,EAAiB9R,WAAa8R,EAAiB9R,YAAc9T,EAAO8T,UACvF2S,EAAczmB,EAAO8I,OAAS8c,EAAiBvvB,gBAAkB2J,EAAO3J,eAAiBmwB,GACzFE,EAAU1mB,EAAO8I,KACnB0d,GAAoBrT,GACtB3T,EAAOmnB,kBAET,EAAOnnB,EAAOQ,OAAQ4lB,GACtB,MAAMgB,EAAYpnB,EAAOQ,OAAOiK,QAC1B4c,EAAUrnB,EAAOQ,OAAO8I,KAC9B/hB,OAAOmG,OAAOsS,EAAQ,CACpB0d,eAAgB1d,EAAOQ,OAAOkd,eAC9B1I,eAAgBhV,EAAOQ,OAAOwU,eAC9BC,eAAgBjV,EAAOQ,OAAOyU,iBAE5ByR,IAAeU,EACjBpnB,EAAO8mB,WACGJ,GAAcU,GACxBpnB,EAAO+mB,SAET/mB,EAAOmmB,kBAAoBF,EAC3BjmB,EAAOoH,KAAK,oBAAqBgf,GAC7BzS,IACEsT,GACFjnB,EAAO+Z,cACP/Z,EAAO4X,WAAWrO,GAClBvJ,EAAO8J,iBACGod,GAAWG,GACrBrnB,EAAO4X,WAAWrO,GAClBvJ,EAAO8J,gBACEod,IAAYG,GACrBrnB,EAAO+Z,eAGX/Z,EAAOoH,KAAK,aAAcgf,EAC5B,EA2CEF,cAzCF,SAAuB9Z,EAAakQ,EAAMgL,GAIxC,QAHa,IAAThL,IACFA,EAAO,WAEJlQ,GAAwB,cAATkQ,IAAyBgL,EAAa,OAC1D,IAAIrB,GAAa,EACjB,MAAM1vB,EAAS,IACTgxB,EAAyB,WAATjL,EAAoB/lB,EAAOyD,YAAcstB,EAAY3d,aACrE6d,EAASjgC,OAAOkE,KAAK2gB,GAAapG,IAAIyhB,IAC1C,GAAqB,iBAAVA,GAA6C,IAAvBA,EAAM/rB,QAAQ,KAAY,CACzD,MAAMgsB,EAAWrkB,WAAWokB,EAAM/wB,OAAO,IAEzC,MAAO,CACLpO,MAFYi/B,EAAgBG,EAG5BD,QAEJ,CACA,MAAO,CACLn/B,MAAOm/B,EACPA,WAGJD,EAAOG,KAAK,CAAC7/B,EAAG8/B,IAAM7uB,SAASjR,EAAEQ,MAAO,IAAMyQ,SAAS6uB,EAAEt/B,MAAO,KAChE,IAAK,IAAIpB,EAAI,EAAGA,EAAIsgC,EAAOv/B,OAAQf,GAAK,EAAG,CACzC,MAAM,MACJugC,EAAK,MACLn/B,GACEk/B,EAAOtgC,GACE,WAATo1B,EACE/lB,EAAO8H,WAAW,eAAe/V,QAAY2Z,UAC/CgkB,EAAawB,GAENn/B,GAASg/B,EAAY5d,cAC9Buc,EAAawB,EAEjB,CACA,OAAOxB,GAAc,KACvB,GAqREtX,cA9KoB,CACpBA,cA9BF,WACE,MAAM3O,EAAStX,MAEbi9B,SAAUkC,EAAS,OACnBrnB,GACER,GACE,mBACJgL,GACExK,EACJ,GAAIwK,EAAoB,CACtB,MAAMyG,EAAiBzR,EAAOqI,OAAOpgB,OAAS,EACxC6/B,EAAqB9nB,EAAO6K,WAAW4G,GAAkBzR,EAAO8K,gBAAgB2G,GAAuC,EAArBzG,EACxGhL,EAAO2lB,SAAW3lB,EAAOmD,KAAO2kB,CAClC,MACE9nB,EAAO2lB,SAAsC,IAA3B3lB,EAAO4K,SAAS3iB,QAEN,IAA1BuY,EAAOwU,iBACThV,EAAOgV,gBAAkBhV,EAAO2lB,WAEJ,IAA1BnlB,EAAOyU,iBACTjV,EAAOiV,gBAAkBjV,EAAO2lB,UAE9BkC,GAAaA,IAAc7nB,EAAO2lB,WACpC3lB,EAAOkR,OAAQ,GAEb2W,IAAc7nB,EAAO2lB,UACvB3lB,EAAOoH,KAAKpH,EAAO2lB,SAAW,OAAS,SAE3C,GAgLEpjB,QAjNY,CACZwlB,WAhDF,WACE,MAAM/nB,EAAStX,MACT,WACJs/B,EAAU,OACVxnB,EAAM,IACN6J,EAAG,GACH1K,EAAE,OACF+E,GACE1E,EAEEioB,EAzBR,SAAwBC,EAASC,GAC/B,MAAMC,EAAgB,GAYtB,OAXAF,EAAQjwB,QAAQowB,IACM,iBAATA,EACT9gC,OAAOkE,KAAK48B,GAAMpwB,QAAQ+vB,IACpBK,EAAKL,IACPI,EAAc/4B,KAAK84B,EAASH,KAGP,iBAATK,GAChBD,EAAc/4B,KAAK84B,EAASE,KAGzBD,CACT,CAWmBE,CAAe,CAAC,cAAe9nB,EAAO8T,UAAW,CAChE,YAAatU,EAAOQ,OAAOsW,UAAYtW,EAAOsW,SAASrM,SACtD,CACD,WAAcjK,EAAOsR,YACpB,CACD,IAAOzH,GACN,CACD,KAAQ7J,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,GACzC,CACD,cAAetI,EAAOqI,MAAQrI,EAAOqI,KAAKC,KAAO,GAA0B,WAArBtI,EAAOqI,KAAK4P,MACjE,CACD,QAAW/T,EAAOE,SACjB,CACD,IAAOF,EAAOC,KACb,CACD,WAAYnE,EAAOsL,SAClB,CACD,SAAYtL,EAAOsL,SAAWtL,EAAOqL,gBACpC,CACD,iBAAkBrL,EAAOoO,sBACvBpO,EAAOuO,wBACXiZ,EAAW34B,QAAQ44B,GACnBtoB,EAAG6C,UAAUC,OAAOulB,GACpBhoB,EAAO2mB,sBACT,EAeE4B,cAbF,WACE,MACM,GACJ5oB,EAAE,WACFqoB,GAHat/B,KAKViX,GAAoB,iBAAPA,IAClBA,EAAG6C,UAAUrH,UAAU6sB,GANRt/B,KAORi+B,uBACT,IAqNM6B,GAAmB,CAAC,EAC1B,MAAMC,GACJ,WAAAh+B,GACE,IAAIkV,EACAa,EACJ,IAAK,IAAI5Q,EAAO1C,UAAUjF,OAAQ4H,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQ7C,UAAU6C,GAEL,IAAhBF,EAAK5H,QAAgB4H,EAAK,GAAGpF,aAAwE,WAAzDlD,OAAOH,UAAUwX,SAASxW,KAAKyH,EAAK,IAAIgP,MAAM,GAAI,GAChG2B,EAAS3Q,EAAK,IAEb8P,EAAIa,GAAU3Q,EAEZ2Q,IAAQA,EAAS,CAAC,GACvBA,EAAS,EAAO,CAAC,EAAGA,GAChBb,IAAOa,EAAOb,KAAIa,EAAOb,GAAKA,GAClC,MAAM5H,EAAW,IACjB,GAAIyI,EAAOb,IAA2B,iBAAda,EAAOb,IAAmB5H,EAASoE,iBAAiBqE,EAAOb,IAAI1X,OAAS,EAAG,CACjG,MAAMygC,EAAU,GAQhB,OAPA3wB,EAASoE,iBAAiBqE,EAAOb,IAAI1H,QAAQqvB,IAC3C,MAAMqB,EAAY,EAAO,CAAC,EAAGnoB,EAAQ,CACnCb,GAAI2nB,IAENoB,EAAQr5B,KAAK,IAAIo5B,GAAOE,MAGnBD,CACT,CAGA,MAAM1oB,EAAStX,KACfsX,EAAON,YAAa,EACpBM,EAAO4D,QAAUG,IACjB/D,EAAO0E,OAASL,EAAU,CACxBjO,UAAWoK,EAAOpK,YAEpB4J,EAAO8D,QAAU0B,IACjBxF,EAAO0G,gBAAkB,CAAC,EAC1B1G,EAAOiH,mBAAqB,GAC5BjH,EAAO4oB,QAAU,IAAI5oB,EAAO6oB,aACxBroB,EAAOooB,SAAW94B,MAAM4S,QAAQlC,EAAOooB,UACzC5oB,EAAO4oB,QAAQv5B,QAAQmR,EAAOooB,SAEhC,MAAM7E,EAAmB,CAAC,EAC1B/jB,EAAO4oB,QAAQ3wB,QAAQ6wB,IACrBA,EAAI,CACFtoB,SACAR,SACA+oB,aAAcjF,GAAmBtjB,EAAQujB,GACzCzsB,GAAI0I,EAAO1I,GAAGtP,KAAKgY,GACnB6G,KAAM7G,EAAO6G,KAAK7e,KAAKgY,GACvBhI,IAAKgI,EAAOhI,IAAIhQ,KAAKgY,GACrBoH,KAAMpH,EAAOoH,KAAKpf,KAAKgY,OAK3B,MAAMgpB,EAAe,EAAO,CAAC,EAAG9F,GAAUa,GAqG1C,OAlGA/jB,EAAOQ,OAAS,EAAO,CAAC,EAAGwoB,EAAcR,GAAkBhoB,GAC3DR,EAAOqmB,eAAiB,EAAO,CAAC,EAAGrmB,EAAOQ,QAC1CR,EAAOipB,aAAe,EAAO,CAAC,EAAGzoB,GAG7BR,EAAOQ,QAAUR,EAAOQ,OAAOlJ,IACjC/P,OAAOkE,KAAKuU,EAAOQ,OAAOlJ,IAAIW,QAAQixB,IACpClpB,EAAO1I,GAAG4xB,EAAWlpB,EAAOQ,OAAOlJ,GAAG4xB,MAGtClpB,EAAOQ,QAAUR,EAAOQ,OAAOwG,OACjChH,EAAOgH,MAAMhH,EAAOQ,OAAOwG,OAI7Bzf,OAAOmG,OAAOsS,EAAQ,CACpByK,QAASzK,EAAOQ,OAAOiK,QACvB9K,KAEAqoB,WAAY,GAEZ3f,OAAQ,GACRwC,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAEjBlB,aAAY,IACyB,eAA5B5J,EAAOQ,OAAO8T,UAEvBzK,WAAU,IAC2B,aAA5B7J,EAAOQ,OAAO8T,UAGvB1L,YAAa,EACbW,UAAW,EAEX0H,aAAa,EACbC,OAAO,EAEP9Q,UAAW,EACXiiB,kBAAmB,EACnBnhB,SAAU,EACVioB,SAAU,EACVxU,WAAW,EACX,qBAAA7E,GAGE,OAAO3O,KAAKioB,MAAM1gC,KAAK0X,UAAY,GAAK,IAAM,GAAK,EACrD,EAEA4U,eAAgBhV,EAAOQ,OAAOwU,eAC9BC,eAAgBjV,EAAOQ,OAAOyU,eAE9BuE,gBAAiB,CACfqC,eAAWtvB,EACXuvB,aAASvvB,EACT2wB,yBAAqB3wB,EACrB8wB,oBAAgB9wB,EAChB4wB,iBAAa5wB,EACbmtB,sBAAkBntB,EAClBktB,oBAAgBltB,EAChBgxB,wBAAoBhxB,EAEpBixB,kBAAmBxd,EAAOQ,OAAOgd,kBAEjCiD,cAAe,EACf4I,kBAAc98B,EAEd+8B,WAAY,GACZ1J,yBAAqBrzB,EACrB6wB,iBAAa7wB,EACbouB,UAAW,KACXE,QAAS,MAGX+B,YAAY,EAEZc,eAAgB1d,EAAOQ,OAAOkd,eAC9B1C,QAAS,CACPb,OAAQ,EACR8C,OAAQ,EACRH,SAAU,EACVC,SAAU,EACVxD,KAAM,GAGRgQ,aAAc,GACdC,aAAc,IAEhBxpB,EAAOoH,KAAK,WAGRpH,EAAOQ,OAAO2iB,MAChBnjB,EAAOmjB,OAKFnjB,CACT,CACA,iBAAAiK,CAAkBwf,GAChB,OAAI/gC,KAAKkhB,eACA6f,EAGF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,gBACfA,EACJ,CACA,aAAA9R,CAAc/V,GACZ,MAAM,SACJsI,EAAQ,OACR1J,GACE9X,KAEE8oB,EAAkB,EADT,EAAgBtH,EAAU,IAAI1J,EAAOyH,4BACR,IAC5C,OAAO,EAAarG,GAAW4P,CACjC,CACA,mBAAAjC,CAAoB9Z,GAClB,OAAO/M,KAAKivB,cAAcjvB,KAAK2f,OAAO3Z,KAAKkT,GAA6D,EAAlDA,EAAQ8R,aAAa,6BAAmCje,GAChH,CACA,qBAAA+hB,CAAsB/hB,GAQpB,OAPI/M,KAAKmgB,MAAQngB,KAAK8X,OAAOqI,MAAQngB,KAAK8X,OAAOqI,KAAKC,KAAO,IAC7B,WAA1BpgB,KAAK8X,OAAOqI,KAAK4P,KACnBhjB,EAAQ0L,KAAK6L,MAAMvX,EAAQ/M,KAAK8X,OAAOqI,KAAKC,MACT,QAA1BpgB,KAAK8X,OAAOqI,KAAK4P,OAC1BhjB,GAAgB0L,KAAKwH,KAAKjgB,KAAK2f,OAAOpgB,OAASS,KAAK8X,OAAOqI,KAAKC,QAG7DrT,CACT,CACA,YAAAsiB,GACE,MACM,SACJ7N,EAAQ,OACR1J,GAHa9X,UAKR2f,OAAS,EAAgB6B,EAAU,IAAI1J,EAAOyH,2BACvD,CACA,MAAA8e,GACE,MAAM/mB,EAAStX,KACXsX,EAAOyK,UACXzK,EAAOyK,SAAU,EACbzK,EAAOQ,OAAOqf,YAChB7f,EAAO8f,gBAET9f,EAAOoH,KAAK,UACd,CACA,OAAA0f,GACE,MAAM9mB,EAAStX,KACVsX,EAAOyK,UACZzK,EAAOyK,SAAU,EACbzK,EAAOQ,OAAOqf,YAChB7f,EAAO6lB,kBAET7lB,EAAOoH,KAAK,WACd,CACA,WAAAsiB,CAAYxoB,EAAUT,GACpB,MAAMT,EAAStX,KACfwY,EAAWC,KAAKE,IAAIF,KAAKC,IAAIF,EAAU,GAAI,GAC3C,MAAMG,EAAMrB,EAAOoQ,eAEbrP,GADMf,EAAOgR,eACI3P,GAAOH,EAAWG,EACzCrB,EAAOmlB,YAAYpkB,OAA0B,IAAVN,EAAwB,EAAIA,GAC/DT,EAAO8S,oBACP9S,EAAO+R,qBACT,CACA,oBAAA4U,GACE,MAAM3mB,EAAStX,KACf,IAAKsX,EAAOQ,OAAOqjB,eAAiB7jB,EAAOL,GAAI,OAC/C,MAAMgqB,EAAM3pB,EAAOL,GAAG+H,UAAU9E,MAAM,KAAKnR,OAAOiW,GACT,IAAhCA,EAAUhM,QAAQ,WAA+E,IAA5DgM,EAAUhM,QAAQsE,EAAOQ,OAAOuO,yBAE9E/O,EAAOoH,KAAK,oBAAqBuiB,EAAIhF,KAAK,KAC5C,CACA,eAAAiF,CAAgBhoB,GACd,MAAM5B,EAAStX,KACf,OAAIsX,EAAO2G,UAAkB,GACtB/E,EAAQ8F,UAAU9E,MAAM,KAAKnR,OAAOiW,GACI,IAAtCA,EAAUhM,QAAQ,iBAAyE,IAAhDgM,EAAUhM,QAAQsE,EAAOQ,OAAOyH,aACjF0c,KAAK,IACV,CACA,iBAAA9R,GACE,MAAM7S,EAAStX,KACf,IAAKsX,EAAOQ,OAAOqjB,eAAiB7jB,EAAOL,GAAI,OAC/C,MAAMkqB,EAAU,GAChB7pB,EAAOqI,OAAOpQ,QAAQ2J,IACpB,MAAMomB,EAAahoB,EAAO4pB,gBAAgBhoB,GAC1CioB,EAAQx6B,KAAK,CACXuS,UACAomB,eAEFhoB,EAAOoH,KAAK,cAAexF,EAASomB,KAEtChoB,EAAOoH,KAAK,gBAAiByiB,EAC/B,CACA,oBAAAnhB,CAAqBohB,EAAMC,QACZ,IAATD,IACFA,EAAO,gBAEK,IAAVC,IACFA,GAAQ,GAEV,MACM,OACJvpB,EAAM,OACN6H,EAAM,WACNwC,EAAU,gBACVC,EACA3H,KAAMgH,EAAU,YAChBvB,GAPalgB,KASf,IAAIshC,EAAM,EACV,GAAoC,iBAAzBxpB,EAAO3J,cAA4B,OAAO2J,EAAO3J,cAC5D,GAAI2J,EAAOqL,eAAgB,CACzB,IACIoe,EADAje,EAAY3D,EAAOO,GAAezH,KAAKwH,KAAKN,EAAOO,GAAaqE,iBAAmB,EAEvF,IAAK,IAAI/lB,EAAI0hB,EAAc,EAAG1hB,EAAImhB,EAAOpgB,OAAQf,GAAK,EAChDmhB,EAAOnhB,KAAO+iC,IAChBje,GAAa7K,KAAKwH,KAAKN,EAAOnhB,GAAG+lB,iBACjC+c,GAAO,EACHhe,EAAY7B,IAAY8f,GAAY,IAG5C,IAAK,IAAI/iC,EAAI0hB,EAAc,EAAG1hB,GAAK,EAAGA,GAAK,EACrCmhB,EAAOnhB,KAAO+iC,IAChBje,GAAa3D,EAAOnhB,GAAG+lB,gBACvB+c,GAAO,EACHhe,EAAY7B,IAAY8f,GAAY,GAG9C,MAEE,GAAa,YAATH,EACF,IAAK,IAAI5iC,EAAI0hB,EAAc,EAAG1hB,EAAImhB,EAAOpgB,OAAQf,GAAK,GAChC6iC,EAAQlf,EAAW3jB,GAAK4jB,EAAgB5jB,GAAK2jB,EAAWjC,GAAeuB,EAAaU,EAAW3jB,GAAK2jB,EAAWjC,GAAeuB,KAEhJ6f,GAAO,QAKX,IAAK,IAAI9iC,EAAI0hB,EAAc,EAAG1hB,GAAK,EAAGA,GAAK,EACrB2jB,EAAWjC,GAAeiC,EAAW3jB,GAAKijB,IAE5D6f,GAAO,GAKf,OAAOA,CACT,CACA,MAAAxgB,GACE,MAAMxJ,EAAStX,KACf,IAAKsX,GAAUA,EAAO2G,UAAW,OACjC,MAAM,SACJiE,EAAQ,OACRpK,GACER,EAcJ,SAASkV,IACP,MAAMgV,EAAiBlqB,EAAOoK,cAAmC,EAApBpK,EAAOI,UAAiBJ,EAAOI,UACtEilB,EAAelkB,KAAKE,IAAIF,KAAKC,IAAI8oB,EAAgBlqB,EAAOgR,gBAAiBhR,EAAOoQ,gBACtFpQ,EAAOkV,aAAamQ,GACpBrlB,EAAO8S,oBACP9S,EAAO+R,qBACT,CACA,IAAIoY,EACJ,GApBI3pB,EAAO4L,aACTpM,EAAOwhB,gBAET,IAAIxhB,EAAOL,GAAGxD,iBAAiB,qBAAqBlE,QAAQ6P,IACtDA,EAAQsiB,UACVviB,GAAqB7H,EAAQ8H,KAGjC9H,EAAOyJ,aACPzJ,EAAO8J,eACP9J,EAAO6Q,iBACP7Q,EAAO+R,sBASHvR,EAAOsW,UAAYtW,EAAOsW,SAASrM,UAAYjK,EAAOsL,QACxDoJ,IACI1U,EAAOsR,YACT9R,EAAOkP,uBAEJ,CACL,IAA8B,SAAzB1O,EAAO3J,eAA4B2J,EAAO3J,cAAgB,IAAMmJ,EAAOkR,QAAU1Q,EAAOqL,eAAgB,CAC3G,MAAMxD,EAASrI,EAAOwK,SAAWhK,EAAOgK,QAAQC,QAAUzK,EAAOwK,QAAQnC,OAASrI,EAAOqI,OACzF8hB,EAAanqB,EAAOwU,QAAQnM,EAAOpgB,OAAS,EAAG,GAAG,GAAO,EAC3D,MACEkiC,EAAanqB,EAAOwU,QAAQxU,EAAO4I,YAAa,GAAG,GAAO,GAEvDuhB,GACHjV,GAEJ,CACI1U,EAAOkO,eAAiB9D,IAAa5K,EAAO4K,UAC9C5K,EAAO2O,gBAET3O,EAAOoH,KAAK,SACd,CACA,eAAA+f,CAAgBkD,EAAcC,QACT,IAAfA,IACFA,GAAa,GAEf,MAAMtqB,EAAStX,KACT6hC,EAAmBvqB,EAAOQ,OAAO8T,UAKvC,OAJK+V,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAE9DF,IAAiBE,GAAqC,eAAjBF,GAAkD,aAAjBA,IAG1ErqB,EAAOL,GAAG6C,UAAUrH,OAAO,GAAG6E,EAAOQ,OAAOuO,yBAAyBwb,KACrEvqB,EAAOL,GAAG6C,UAAUC,IAAI,GAAGzC,EAAOQ,OAAOuO,yBAAyBsb,KAClErqB,EAAO2mB,uBACP3mB,EAAOQ,OAAO8T,UAAY+V,EAC1BrqB,EAAOqI,OAAOpQ,QAAQ2J,IACC,aAAjByoB,EACFzoB,EAAQnF,MAAM7B,MAAQ,GAEtBgH,EAAQnF,MAAMsI,OAAS,KAG3B/E,EAAOoH,KAAK,mBACRkjB,GAAYtqB,EAAOwJ,UAddxJ,CAgBX,CACA,uBAAAwqB,CAAwBlW,GACtB,MAAMtU,EAAStX,KACXsX,EAAOqK,KAAqB,QAAdiK,IAAwBtU,EAAOqK,KAAqB,QAAdiK,IACxDtU,EAAOqK,IAAoB,QAAdiK,EACbtU,EAAOoK,aAA2C,eAA5BpK,EAAOQ,OAAO8T,WAA8BtU,EAAOqK,IACrErK,EAAOqK,KACTrK,EAAOL,GAAG6C,UAAUC,IAAI,GAAGzC,EAAOQ,OAAOuO,6BACzC/O,EAAOL,GAAGkB,IAAM,QAEhBb,EAAOL,GAAG6C,UAAUrH,OAAO,GAAG6E,EAAOQ,OAAOuO,6BAC5C/O,EAAOL,GAAGkB,IAAM,OAElBb,EAAOwJ,SACT,CACA,KAAAihB,CAAM3oB,GACJ,MAAM9B,EAAStX,KACf,GAAIsX,EAAO0qB,QAAS,OAAO,EAG3B,IAAI/qB,EAAKmC,GAAW9B,EAAOQ,OAAOb,GAIlC,GAHkB,iBAAPA,IACTA,EAAK5H,SAASmE,cAAcyD,KAEzBA,EACH,OAAO,EAETA,EAAGK,OAASA,EACRL,EAAGgrB,YAAchrB,EAAGgrB,WAAW3tB,MAAQ2C,EAAGgrB,WAAW3tB,KAAKf,WAAa+D,EAAOQ,OAAO4iB,sBAAsBwH,gBAC7G5qB,EAAOgI,WAAY,GAErB,MAAM6iB,EAAqB,IAClB,KAAK7qB,EAAOQ,OAAOojB,cAAgB,IAAIjhB,OAAOC,MAAM,KAAK+hB,KAAK,OAWvE,IAAIjkB,EAREf,GAAMA,EAAGkC,YAAclC,EAAGkC,WAAW3F,cAC3ByD,EAAGkC,WAAW3F,cAAc2uB,KAInC,EAAgBlrB,EAAIkrB,KAAsB,GAsBnD,OAlBKnqB,GAAaV,EAAOQ,OAAO8iB,iBAC9B5iB,EAAY,EAAc,MAAOV,EAAOQ,OAAOojB,cAC/CjkB,EAAGpE,OAAOmF,GACV,EAAgBf,EAAI,IAAIK,EAAOQ,OAAOyH,cAAchQ,QAAQ2J,IAC1DlB,EAAUnF,OAAOqG,MAGrBra,OAAOmG,OAAOsS,EAAQ,CACpBL,KACAe,YACAwJ,SAAUlK,EAAOgI,YAAcrI,EAAGgrB,WAAW3tB,KAAK8tB,WAAanrB,EAAGgrB,WAAW3tB,KAAO0D,EACpFqqB,OAAQ/qB,EAAOgI,UAAYrI,EAAGgrB,WAAW3tB,KAAO2C,EAChD+qB,SAAS,EAETrgB,IAA8B,QAAzB1K,EAAGkB,IAAI8E,eAA6D,QAAlC9C,EAAalD,EAAI,aACxDyK,aAA0C,eAA5BpK,EAAOQ,OAAO8T,YAAwD,QAAzB3U,EAAGkB,IAAI8E,eAA6D,QAAlC9C,EAAalD,EAAI,cAC9G2K,SAAiD,gBAAvCzH,EAAanC,EAAW,cAE7B,CACT,CACA,IAAAyiB,CAAKxjB,GACH,MAAMK,EAAStX,KACf,GAAIsX,EAAO2T,YAAa,OAAO3T,EAE/B,IAAgB,IADAA,EAAOyqB,MAAM9qB,GACN,OAAOK,EAC9BA,EAAOoH,KAAK,cAGRpH,EAAOQ,OAAO4L,aAChBpM,EAAOwhB,gBAITxhB,EAAO+nB,aAGP/nB,EAAOyJ,aAGPzJ,EAAO8J,eACH9J,EAAOQ,OAAOkO,eAChB1O,EAAO2O,gBAIL3O,EAAOQ,OAAOqf,YAAc7f,EAAOyK,SACrCzK,EAAO8f,gBAIL9f,EAAOQ,OAAO8I,MAAQtJ,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAChEzK,EAAOwU,QAAQxU,EAAOQ,OAAOgV,aAAexV,EAAOwK,QAAQmD,aAAc,EAAG3N,EAAOQ,OAAOoT,oBAAoB,GAAO,GAErH5T,EAAOwU,QAAQxU,EAAOQ,OAAOgV,aAAc,EAAGxV,EAAOQ,OAAOoT,oBAAoB,GAAO,GAIrF5T,EAAOQ,OAAO8I,MAChBtJ,EAAO4X,gBAAWrrB,GAAW,GAI/ByT,EAAO8lB,eACP,MAAMkF,EAAe,IAAIhrB,EAAOL,GAAGxD,iBAAiB,qBAsBpD,OArBI6D,EAAOgI,WACTgjB,EAAa37B,QAAQ2Q,EAAO+qB,OAAO5uB,iBAAiB,qBAEtD6uB,EAAa/yB,QAAQ6P,IACfA,EAAQsiB,SACVviB,GAAqB7H,EAAQ8H,GAE7BA,EAAQjM,iBAAiB,OAAQnV,IAC/BmhB,GAAqB7H,EAAQtZ,EAAEgS,YAIrC6P,GAAQvI,GAGRA,EAAO2T,aAAc,EACrBpL,GAAQvI,GAGRA,EAAOoH,KAAK,QACZpH,EAAOoH,KAAK,aACLpH,CACT,CACA,OAAAirB,CAAQC,EAAgBC,QACC,IAAnBD,IACFA,GAAiB,QAEC,IAAhBC,IACFA,GAAc,GAEhB,MAAMnrB,EAAStX,MACT,OACJ8X,EAAM,GACNb,EAAE,UACFe,EAAS,OACT2H,GACErI,EACJ,YAA6B,IAAlBA,EAAOQ,QAA0BR,EAAO2G,YAGnD3G,EAAOoH,KAAK,iBAGZpH,EAAO2T,aAAc,EAGrB3T,EAAO+lB,eAGHvlB,EAAO8I,MACTtJ,EAAO+Z,cAILoR,IACFnrB,EAAOuoB,gBACH5oB,GAAoB,iBAAPA,GACfA,EAAG2I,gBAAgB,SAEjB5H,GACFA,EAAU4H,gBAAgB,SAExBD,GAAUA,EAAOpgB,QACnBogB,EAAOpQ,QAAQ2J,IACbA,EAAQY,UAAUrH,OAAOqF,EAAOkQ,kBAAmBlQ,EAAOmQ,uBAAwBnQ,EAAOkS,iBAAkBlS,EAAOmS,eAAgBnS,EAAOoS,gBACzIhR,EAAQ0G,gBAAgB,SACxB1G,EAAQ0G,gBAAgB,8BAI9BtI,EAAOoH,KAAK,WAGZ7f,OAAOkE,KAAKuU,EAAO0G,iBAAiBzO,QAAQixB,IAC1ClpB,EAAOhI,IAAIkxB,MAEU,IAAnBgC,IACElrB,EAAOL,IAA2B,iBAAdK,EAAOL,KAC7BK,EAAOL,GAAGK,OAAS,MD/zH3B,SAAqBnT,GACnB,MAAMu+B,EAASv+B,EACftF,OAAOkE,KAAK2/B,GAAQnzB,QAAQtL,IAC1B,IACEy+B,EAAOz+B,GAAO,IAChB,CAAE,MAAOjG,GAET,CACA,WACS0kC,EAAOz+B,EAChB,CAAE,MAAOjG,GAET,GAEJ,CCmzHM2kC,CAAYrrB,IAEdA,EAAO2G,WAAY,GA5CV,IA8CX,CACA,qBAAO2kB,CAAeC,GACpB,EAAO/C,GAAkB+C,EAC3B,CACA,2BAAW/C,GACT,OAAOA,EACT,CACA,mBAAWtF,GACT,OAAOA,EACT,CACA,oBAAOsI,CAAc1C,GACdL,GAAOrhC,UAAUyhC,cAAaJ,GAAOrhC,UAAUyhC,YAAc,IAClE,MAAMD,EAAUH,GAAOrhC,UAAUyhC,YACd,mBAARC,GAAsBF,EAAQltB,QAAQotB,GAAO,GACtDF,EAAQv5B,KAAKy5B,EAEjB,CACA,UAAO2C,CAAI3iC,GACT,OAAIgH,MAAM4S,QAAQ5Z,IAChBA,EAAOmP,QAAQhP,GAAKw/B,GAAO+C,cAAcviC,IAClCw/B,KAETA,GAAO+C,cAAc1iC,GACd2/B,GACT,ECp2HF,SAAS,GAA0BzoB,EAAQqmB,EAAgB7lB,EAAQkrB,GAejE,OAdI1rB,EAAOQ,OAAO8iB,gBAChB/7B,OAAOkE,KAAKigC,GAAYzzB,QAAQtL,IAC9B,IAAK6T,EAAO7T,KAAwB,IAAhB6T,EAAO0jB,KAAe,CACxC,IAAIpiB,EAAU,EAAgB9B,EAAOL,GAAI,IAAI+rB,EAAW/+B,MAAQ,GAC3DmV,IACHA,EAAU,EAAc,MAAO4pB,EAAW/+B,IAC1CmV,EAAQ4F,UAAYgkB,EAAW/+B,GAC/BqT,EAAOL,GAAGpE,OAAOuG,IAEnBtB,EAAO7T,GAAOmV,EACdukB,EAAe15B,GAAOmV,CACxB,IAGGtB,CACT,CCfA,SAASmrB,GAAWzzB,GAClB,IAAI,OACF8H,EAAM,aACN+oB,EAAY,GACZzxB,EAAE,KACF8P,GACElP,EAgBJ,SAAS0zB,EAAMjsB,GACb,IAAIksB,EACJ,OAAIlsB,GAAoB,iBAAPA,GAAmBK,EAAOgI,YACzC6jB,EAAM7rB,EAAOL,GAAGzD,cAAcyD,IAAOK,EAAO+qB,OAAO7uB,cAAcyD,GAC7DksB,GAAYA,GAEdlsB,IACgB,iBAAPA,IAAiBksB,EAAM,IAAI9zB,SAASoE,iBAAiBwD,KAC5DK,EAAOQ,OAAOkjB,mBAAmC,iBAAP/jB,GAAmBksB,GAAOA,EAAI5jC,OAAS,GAA+C,IAA1C+X,EAAOL,GAAGxD,iBAAiBwD,GAAI1X,OACvH4jC,EAAM7rB,EAAOL,GAAGzD,cAAcyD,GACrBksB,GAAsB,IAAfA,EAAI5jC,SACpB4jC,EAAMA,EAAI,KAGVlsB,IAAOksB,EAAYlsB,EAEhBksB,EACT,CACA,SAASC,EAASnsB,EAAIosB,GACpB,MAAMvrB,EAASR,EAAOQ,OAAO4gB,YAC7BzhB,EAAK,EAAkBA,IACpB1H,QAAQ+zB,IACLA,IACFA,EAAMxpB,UAAUupB,EAAW,MAAQ,aAAavrB,EAAOyrB,cAAcrpB,MAAM,MACrD,WAAlBopB,EAAME,UAAsBF,EAAMD,SAAWA,GAC7C/rB,EAAOQ,OAAOkO,eAAiB1O,EAAOyK,SACxCuhB,EAAMxpB,UAAUxC,EAAO2lB,SAAW,MAAQ,UAAUnlB,EAAO2rB,aAInE,CACA,SAAS3iB,IAEP,MAAM,OACJ6X,EAAM,OACNC,GACEthB,EAAOohB,WACX,GAAIphB,EAAOQ,OAAO8I,KAGhB,OAFAwiB,EAASxK,GAAQ,QACjBwK,EAASzK,GAAQ,GAGnByK,EAASxK,EAAQthB,EAAOiR,cAAgBjR,EAAOQ,OAAO6I,QACtDyiB,EAASzK,EAAQrhB,EAAOkR,QAAUlR,EAAOQ,OAAO6I,OAClD,CACA,SAAS+iB,EAAY1lC,GACnBA,EAAE6zB,mBACEva,EAAOiR,aAAgBjR,EAAOQ,OAAO8I,MAAStJ,EAAOQ,OAAO6I,UAChErJ,EAAOyW,YACPrP,EAAK,kBACP,CACA,SAASilB,EAAY3lC,GACnBA,EAAE6zB,mBACEva,EAAOkR,OAAUlR,EAAOQ,OAAO8I,MAAStJ,EAAOQ,OAAO6I,UAC1DrJ,EAAOkW,YACP9O,EAAK,kBACP,CACA,SAAS+b,IACP,MAAM3iB,EAASR,EAAOQ,OAAO4gB,WAK7B,GAJAphB,EAAOQ,OAAO4gB,WAAa,GAA0BphB,EAAQA,EAAOqmB,eAAejF,WAAYphB,EAAOQ,OAAO4gB,WAAY,CACvHC,OAAQ,qBACRC,OAAQ,wBAEJ9gB,EAAO6gB,SAAU7gB,EAAO8gB,OAAS,OACvC,IAAID,EAASuK,EAAMprB,EAAO6gB,QACtBC,EAASsK,EAAMprB,EAAO8gB,QAC1B/5B,OAAOmG,OAAOsS,EAAOohB,WAAY,CAC/BC,SACAC,WAEFD,EAAS,EAAkBA,GAC3BC,EAAS,EAAkBA,GAC3B,MAAMgL,EAAa,CAAC3sB,EAAIkB,KAClBlB,GACFA,EAAG9D,iBAAiB,QAAiB,SAARgF,EAAiBwrB,EAAcD,IAEzDpsB,EAAOyK,SAAW9K,GACrBA,EAAG6C,UAAUC,OAAOjC,EAAO2rB,UAAUvpB,MAAM,OAG/Cye,EAAOppB,QAAQ0H,GAAM2sB,EAAW3sB,EAAI,SACpC2hB,EAAOrpB,QAAQ0H,GAAM2sB,EAAW3sB,EAAI,QACtC,CACA,SAASsrB,IACP,IAAI,OACF5J,EAAM,OACNC,GACEthB,EAAOohB,WACXC,EAAS,EAAkBA,GAC3BC,EAAS,EAAkBA,GAC3B,MAAMiL,EAAgB,CAAC5sB,EAAIkB,KACzBlB,EAAG7D,oBAAoB,QAAiB,SAAR+E,EAAiBwrB,EAAcD,GAC/DzsB,EAAG6C,UAAUrH,UAAU6E,EAAOQ,OAAO4gB,WAAW6K,cAAcrpB,MAAM,OAEtEye,EAAOppB,QAAQ0H,GAAM4sB,EAAc5sB,EAAI,SACvC2hB,EAAOrpB,QAAQ0H,GAAM4sB,EAAc5sB,EAAI,QACzC,CA/GAopB,EAAa,CACX3H,WAAY,CACVC,OAAQ,KACRC,OAAQ,KACRkL,aAAa,EACbP,cAAe,yBACfQ,YAAa,uBACbN,UAAW,qBACXO,wBAAyB,gCAG7B1sB,EAAOohB,WAAa,CAClBC,OAAQ,KACRC,OAAQ,MAmGVhqB,EAAG,OAAQ,MACgC,IAArC0I,EAAOQ,OAAO4gB,WAAW3W,QAE3Bqc,KAEA3D,IACA3Z,OAGJlS,EAAG,8BAA+B,KAChCkS,MAEFlS,EAAG,UAAW,KACZ2zB,MAEF3zB,EAAG,iBAAkB,KACnB,IAAI,OACF+pB,EAAM,OACNC,GACEthB,EAAOohB,WACXC,EAAS,EAAkBA,GAC3BC,EAAS,EAAkBA,GACvBthB,EAAOyK,QACTjB,IAGF,IAAI6X,KAAWC,GAAQ7vB,OAAOkO,KAAQA,GAAI1H,QAAQ0H,GAAMA,EAAG6C,UAAUC,IAAIzC,EAAOQ,OAAO4gB,WAAW+K,cAEpG70B,EAAG,QAAS,CAACq1B,EAAIjmC,KACf,IAAI,OACF26B,EAAM,OACNC,GACEthB,EAAOohB,WACXC,EAAS,EAAkBA,GAC3BC,EAAS,EAAkBA,GAC3B,MAAMnG,EAAWz0B,EAAEgS,OACnB,IAAIk0B,EAAiBtL,EAAOzb,SAASsV,IAAakG,EAAOxb,SAASsV,GAClE,GAAInb,EAAOgI,YAAc4kB,EAAgB,CACvC,MAAM9Y,EAAOptB,EAAEotB,MAAQptB,EAAEw1B,cAAgBx1B,EAAEw1B,eACvCpI,IACF8Y,EAAiB9Y,EAAKplB,KAAKqlB,GAAUsN,EAAOxb,SAASkO,IAAWuN,EAAOzb,SAASkO,IAEpF,CACA,GAAI/T,EAAOQ,OAAO4gB,WAAWoL,cAAgBI,EAAgB,CAC3D,GAAI5sB,EAAO6sB,YAAc7sB,EAAOQ,OAAOqsB,YAAc7sB,EAAOQ,OAAOqsB,WAAWC,YAAc9sB,EAAO6sB,WAAWltB,KAAOwb,GAAYnb,EAAO6sB,WAAWltB,GAAGgI,SAASwT,IAAY,OAC3K,IAAI4R,EACA1L,EAAOp5B,OACT8kC,EAAW1L,EAAO,GAAG7e,UAAUmF,SAAS3H,EAAOQ,OAAO4gB,WAAWqL,aACxDnL,EAAOr5B,SAChB8kC,EAAWzL,EAAO,GAAG9e,UAAUmF,SAAS3H,EAAOQ,OAAO4gB,WAAWqL,cAGjErlB,GADe,IAAb2lB,EACG,iBAEA,kBAEP,IAAI1L,KAAWC,GAAQ7vB,OAAOkO,KAAQA,GAAI1H,QAAQ0H,GAAMA,EAAG6C,UAAUwqB,OAAOhtB,EAAOQ,OAAO4gB,WAAWqL,aACvG,IAEF,MAKM3F,EAAU,KACd9mB,EAAOL,GAAG6C,UAAUC,OAAOzC,EAAOQ,OAAO4gB,WAAWsL,wBAAwB9pB,MAAM,MAClFqoB,KAEF1jC,OAAOmG,OAAOsS,EAAOohB,WAAY,CAC/B2F,OAVa,KACb/mB,EAAOL,GAAG6C,UAAUrH,UAAU6E,EAAOQ,OAAO4gB,WAAWsL,wBAAwB9pB,MAAM,MACrFugB,IACA3Z,KAQAsd,UACAtd,SACA2Z,OACA8H,WAEJ,CCrMA,SAAS,GAAkB1oB,GAIzB,YAHgB,IAAZA,IACFA,EAAU,IAEL,IAAIA,EAAQI,OAAO4I,QAAQ,oBAAqB,QACtDA,QAAQ,KAAM,MACjB,CCFA,SAAS0hB,GAAW/0B,GAClB,IAAI,OACF8H,EAAM,aACN+oB,EAAY,GACZzxB,EAAE,KACF8P,GACElP,EACJ,MAAMg1B,EAAM,oBAqCZ,IAAIC,EApCJpE,EAAa,CACX8D,WAAY,CACVltB,GAAI,KACJytB,cAAe,OACfN,WAAW,EACXN,aAAa,EACba,aAAc,KACdC,kBAAmB,KACnBC,eAAgB,KAChBC,aAAc,KACdC,qBAAqB,EACrB/S,KAAM,UAENgT,gBAAgB,EAChBC,mBAAoB,EACpBC,sBAAuB90B,GAAUA,EACjC+0B,oBAAqB/0B,GAAUA,EAC/Bg1B,YAAa,GAAGZ,WAChBa,kBAAmB,GAAGb,kBACtBc,cAAe,GAAGd,KAClBe,aAAc,GAAGf,YACjBgB,WAAY,GAAGhB,UACfT,YAAa,GAAGS,WAChBiB,qBAAsB,GAAGjB,qBACzBkB,yBAA0B,GAAGlB,yBAC7BmB,eAAgB,GAAGnB,cACnBf,UAAW,GAAGe,SACdoB,gBAAiB,GAAGpB,eACpBqB,cAAe,GAAGrB,aAClBsB,wBAAyB,GAAGtB,gBAGhCltB,EAAO6sB,WAAa,CAClBltB,GAAI,KACJ8uB,QAAS,IAGX,IAAIC,EAAqB,EACzB,SAASC,IACP,OAAQ3uB,EAAOQ,OAAOqsB,WAAWltB,KAAOK,EAAO6sB,WAAWltB,IAAM7P,MAAM4S,QAAQ1C,EAAO6sB,WAAWltB,KAAuC,IAAhCK,EAAO6sB,WAAWltB,GAAG1X,MAC9H,CACA,SAAS2mC,EAAeC,EAAUC,GAChC,MAAM,kBACJf,GACE/tB,EAAOQ,OAAOqsB,WACbgC,IACLA,EAAWA,GAAyB,SAAbC,EAAsB,WAAa,QAAtC,qBAElBD,EAASrsB,UAAUC,IAAI,GAAGsrB,KAAqBe,MAC/CD,EAAWA,GAAyB,SAAbC,EAAsB,WAAa,QAAtC,oBAElBD,EAASrsB,UAAUC,IAAI,GAAGsrB,KAAqBe,KAAYA,KAGjE,CAWA,SAASC,EAAcroC,GACrB,MAAMmoC,EAAWnoC,EAAEgS,OAAOqP,QAAQ,GAAkB/H,EAAOQ,OAAOqsB,WAAWiB,cAC7E,IAAKe,EACH,OAEFnoC,EAAE6zB,iBACF,MAAM9kB,EAAQ,EAAao5B,GAAY7uB,EAAOQ,OAAO2M,eACrD,GAAInN,EAAOQ,OAAO8I,KAAM,CACtB,GAAItJ,EAAOuJ,YAAc9T,EAAO,OAChC,MAAMu5B,GAnBgB/X,EAmBiBjX,EAAOuJ,UAnBblK,EAmBwB5J,GAjB3D4J,GAF8CpX,EAmBoB+X,EAAOqI,OAAOpgB,UAhBlD,GAF9BgvB,GAAwBhvB,GAGf,OACEoX,IAAc4X,EAAY,EAC5B,gBADF,GAeiB,SAAlB+X,EACFhvB,EAAOkW,YACoB,aAAlB8Y,EACThvB,EAAOyW,YAEPzW,EAAO2V,YAAYlgB,EAEvB,MACEuK,EAAOwU,QAAQ/e,GA5BnB,IAA0BwhB,EAAW5X,EAAWpX,CA8BhD,CACA,SAASuhB,IAEP,MAAMa,EAAMrK,EAAOqK,IACb7J,EAASR,EAAOQ,OAAOqsB,WAC7B,GAAI8B,IAAwB,OAC5B,IAGI5tB,EACAiS,EAJArT,EAAKK,EAAO6sB,WAAWltB,GAC3BA,EAAK,EAAkBA,GAIvB,MAAMgL,EAAe3K,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAAUzK,EAAOwK,QAAQnC,OAAOpgB,OAAS+X,EAAOqI,OAAOpgB,OAC9GgnC,EAAQjvB,EAAOQ,OAAO8I,KAAOnI,KAAKwH,KAAKgC,EAAe3K,EAAOQ,OAAO2M,gBAAkBnN,EAAO4K,SAAS3iB,OAY5G,GAXI+X,EAAOQ,OAAO8I,MAChB0J,EAAgBhT,EAAOiT,mBAAqB,EAC5ClS,EAAUf,EAAOQ,OAAO2M,eAAiB,EAAIhM,KAAK6L,MAAMhN,EAAOuJ,UAAYvJ,EAAOQ,OAAO2M,gBAAkBnN,EAAOuJ,gBAC7E,IAArBvJ,EAAOuO,WACvBxN,EAAUf,EAAOuO,UACjByE,EAAgBhT,EAAOkT,oBAEvBF,EAAgBhT,EAAOgT,eAAiB,EACxCjS,EAAUf,EAAO4I,aAAe,GAGd,YAAhBpI,EAAOka,MAAsB1a,EAAO6sB,WAAW4B,SAAWzuB,EAAO6sB,WAAW4B,QAAQxmC,OAAS,EAAG,CAClG,MAAMwmC,EAAUzuB,EAAO6sB,WAAW4B,QAClC,IAAIS,EACAhY,EACAiY,EAsBJ,GArBI3uB,EAAOktB,iBACTP,EAAajqB,EAAiBurB,EAAQ,GAAIzuB,EAAO4J,eAAiB,QAAU,UAAU,GACtFjK,EAAG1H,QAAQ+zB,IACTA,EAAMvvB,MAAMuD,EAAO4J,eAAiB,QAAU,UAAeujB,GAAc3sB,EAAOmtB,mBAAqB,GAA7C,OAExDntB,EAAOmtB,mBAAqB,QAAuBphC,IAAlBymB,IACnC0b,GAAsB3tB,GAAWiS,GAAiB,GAC9C0b,EAAqBluB,EAAOmtB,mBAAqB,EACnDe,EAAqBluB,EAAOmtB,mBAAqB,EACxCe,EAAqB,IAC9BA,EAAqB,IAGzBQ,EAAa/tB,KAAKC,IAAIL,EAAU2tB,EAAoB,GACpDxX,EAAYgY,GAAc/tB,KAAKE,IAAIotB,EAAQxmC,OAAQuY,EAAOmtB,oBAAsB,GAChFwB,GAAYjY,EAAYgY,GAAc,GAExCT,EAAQx2B,QAAQ42B,IACd,MAAMO,EAAkB,IAAI,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,SAASppB,IAAIqpB,GAAU,GAAG7uB,EAAOutB,oBAAoBsB,MAAWrpB,IAAIrC,GAAkB,iBAANA,GAAkBA,EAAEkC,SAAS,KAAOlC,EAAEf,MAAM,KAAOe,GAAG2rB,OACrNT,EAASrsB,UAAUrH,UAAUi0B,KAE3BzvB,EAAG1X,OAAS,EACdwmC,EAAQx2B,QAAQs3B,IACd,MAAMC,EAAc,EAAaD,GAC7BC,IAAgBzuB,EAClBwuB,EAAO/sB,UAAUC,OAAOjC,EAAOutB,kBAAkBnrB,MAAM,MAC9C5C,EAAOgI,WAChBunB,EAAO7yB,aAAa,OAAQ,UAE1B8D,EAAOktB,iBACL8B,GAAeN,GAAcM,GAAetY,GAC9CqY,EAAO/sB,UAAUC,OAAO,GAAGjC,EAAOutB,yBAAyBnrB,MAAM,MAE/D4sB,IAAgBN,GAClBN,EAAeW,EAAQ,QAErBC,IAAgBtY,GAClB0X,EAAeW,EAAQ,eAIxB,CACL,MAAMA,EAASd,EAAQ1tB,GASvB,GARIwuB,GACFA,EAAO/sB,UAAUC,OAAOjC,EAAOutB,kBAAkBnrB,MAAM,MAErD5C,EAAOgI,WACTymB,EAAQx2B,QAAQ,CAAC42B,EAAUW,KACzBX,EAASnyB,aAAa,OAAQ8yB,IAAgBzuB,EAAU,gBAAkB,YAG1EP,EAAOktB,eAAgB,CACzB,MAAM+B,EAAuBhB,EAAQS,GAC/BQ,EAAsBjB,EAAQvX,GACpC,IAAK,IAAIhwB,EAAIgoC,EAAYhoC,GAAKgwB,EAAWhwB,GAAK,EACxCunC,EAAQvnC,IACVunC,EAAQvnC,GAAGsb,UAAUC,OAAO,GAAGjC,EAAOutB,yBAAyBnrB,MAAM,MAGzEgsB,EAAea,EAAsB,QACrCb,EAAec,EAAqB,OACtC,CACF,CACA,GAAIlvB,EAAOktB,eAAgB,CACzB,MAAMiC,EAAuBxuB,KAAKE,IAAIotB,EAAQxmC,OAAQuY,EAAOmtB,mBAAqB,GAC5EiC,GAAiBzC,EAAawC,EAAuBxC,GAAc,EAAIgC,EAAWhC,EAClF0C,EAAaxlB,EAAM,QAAU,OACnCokB,EAAQx2B,QAAQs3B,IACdA,EAAO9yB,MAAMuD,EAAO4J,eAAiBimB,EAAa,OAAS,GAAGD,OAElE,CACF,CACAjwB,EAAG1H,QAAQ,CAAC+zB,EAAO8D,KASjB,GARoB,aAAhBtvB,EAAOka,OACTsR,EAAM7vB,iBAAiB,GAAkBqE,EAAOytB,eAAeh2B,QAAQ83B,IACrEA,EAAWC,YAAcxvB,EAAOotB,sBAAsB7sB,EAAU,KAElEirB,EAAM7vB,iBAAiB,GAAkBqE,EAAO0tB,aAAaj2B,QAAQg4B,IACnEA,EAAQD,YAAcxvB,EAAOqtB,oBAAoBoB,MAGjC,gBAAhBzuB,EAAOka,KAAwB,CACjC,IAAIwV,EAEFA,EADE1vB,EAAOitB,oBACcztB,EAAO4J,eAAiB,WAAa,aAErC5J,EAAO4J,eAAiB,aAAe,WAEhE,MAAMumB,GAASpvB,EAAU,GAAKkuB,EAC9B,IAAImB,EAAS,EACTC,EAAS,EACgB,eAAzBH,EACFE,EAASD,EAETE,EAASF,EAEXnE,EAAM7vB,iBAAiB,GAAkBqE,EAAO2tB,uBAAuBl2B,QAAQq4B,IAC7EA,EAAW7zB,MAAMgQ,UAAY,6BAA6B2jB,aAAkBC,KAC5EC,EAAW7zB,MAAM+oB,mBAAqB,GAAGxlB,EAAOQ,OAAOC,WAE3D,CACoB,WAAhBD,EAAOka,MAAqBla,EAAOgtB,cACrC,EAAaxB,EAAOxrB,EAAOgtB,aAAaxtB,EAAQe,EAAU,EAAGkuB,IAC1C,IAAfa,GAAkB1oB,EAAK,mBAAoB4kB,KAE5B,IAAf8D,GAAkB1oB,EAAK,mBAAoB4kB,GAC/C5kB,EAAK,mBAAoB4kB,IAEvBhsB,EAAOQ,OAAOkO,eAAiB1O,EAAOyK,SACxCuhB,EAAMxpB,UAAUxC,EAAO2lB,SAAW,MAAQ,UAAUnlB,EAAO2rB,YAGjE,CACA,SAASoE,IAEP,MAAM/vB,EAASR,EAAOQ,OAAOqsB,WAC7B,GAAI8B,IAAwB,OAC5B,MAAMhkB,EAAe3K,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAAUzK,EAAOwK,QAAQnC,OAAOpgB,OAAS+X,EAAO6I,MAAQ7I,EAAOQ,OAAOqI,KAAKC,KAAO,EAAI9I,EAAOqI,OAAOpgB,OAASkZ,KAAKwH,KAAK3I,EAAOQ,OAAOqI,KAAKC,MAAQ9I,EAAOqI,OAAOpgB,OAC7N,IAAI0X,EAAKK,EAAO6sB,WAAWltB,GAC3BA,EAAK,EAAkBA,GACvB,IAAI6wB,EAAiB,GACrB,GAAoB,YAAhBhwB,EAAOka,KAAoB,CAC7B,IAAI+V,EAAkBzwB,EAAOQ,OAAO8I,KAAOnI,KAAKwH,KAAKgC,EAAe3K,EAAOQ,OAAO2M,gBAAkBnN,EAAO4K,SAAS3iB,OAChH+X,EAAOQ,OAAOsW,UAAY9W,EAAOQ,OAAOsW,SAASrM,SAAWgmB,EAAkB9lB,IAChF8lB,EAAkB9lB,GAEpB,IAAK,IAAIzjB,EAAI,EAAGA,EAAIupC,EAAiBvpC,GAAK,EACpCsZ,EAAO6sB,aACTmD,GAAkBhwB,EAAO6sB,aAAajlC,KAAK4X,EAAQ9Y,EAAGsZ,EAAOstB,aAG7D0C,GAAkB,IAAIhwB,EAAO4sB,iBAAiBptB,EAAOgI,UAAY,gBAAkB,aAAaxH,EAAOstB,kBAAkBttB,EAAO4sB,gBAGtI,CACoB,aAAhB5sB,EAAOka,OAEP8V,EADEhwB,EAAO+sB,eACQ/sB,EAAO+sB,eAAenlC,KAAK4X,EAAQQ,EAAOytB,aAAcztB,EAAO0tB,YAE/D,gBAAgB1tB,EAAOytB,wCAAkDztB,EAAO0tB,uBAGjF,gBAAhB1tB,EAAOka,OAEP8V,EADEhwB,EAAO8sB,kBACQ9sB,EAAO8sB,kBAAkBllC,KAAK4X,EAAQQ,EAAO2tB,sBAE7C,gBAAgB3tB,EAAO2tB,iCAG5CnuB,EAAO6sB,WAAW4B,QAAU,GAC5B9uB,EAAG1H,QAAQ+zB,IACW,WAAhBxrB,EAAOka,MACT,EAAasR,EAAOwE,GAAkB,IAEpB,YAAhBhwB,EAAOka,MACT1a,EAAO6sB,WAAW4B,QAAQp/B,QAAQ28B,EAAM7vB,iBAAiB,GAAkBqE,EAAOstB,iBAGlE,WAAhBttB,EAAOka,MACTtT,EAAK,mBAAoBzH,EAAG,GAEhC,CACA,SAASwjB,IACPnjB,EAAOQ,OAAOqsB,WAAa,GAA0B7sB,EAAQA,EAAOqmB,eAAewG,WAAY7sB,EAAOQ,OAAOqsB,WAAY,CACvHltB,GAAI,sBAEN,MAAMa,EAASR,EAAOQ,OAAOqsB,WAC7B,IAAKrsB,EAAOb,GAAI,OAChB,IAAIA,EACqB,iBAAda,EAAOb,IAAmBK,EAAOgI,YAC1CrI,EAAKK,EAAOL,GAAGzD,cAAcsE,EAAOb,KAEjCA,GAA2B,iBAAda,EAAOb,KACvBA,EAAK,IAAI5H,SAASoE,iBAAiBqE,EAAOb,MAEvCA,IACHA,EAAKa,EAAOb,IAETA,GAAoB,IAAdA,EAAG1X,SACV+X,EAAOQ,OAAOkjB,mBAA0C,iBAAdljB,EAAOb,IAAmB7P,MAAM4S,QAAQ/C,IAAOA,EAAG1X,OAAS,IACvG0X,EAAK,IAAIK,EAAOL,GAAGxD,iBAAiBqE,EAAOb,KAEvCA,EAAG1X,OAAS,IACd0X,EAAKA,EAAGjR,KAAKs9B,GACP,EAAeA,EAAO,WAAW,KAAOhsB,EAAOL,MAKrD7P,MAAM4S,QAAQ/C,IAAqB,IAAdA,EAAG1X,SAAc0X,EAAKA,EAAG,IAClDpY,OAAOmG,OAAOsS,EAAO6sB,WAAY,CAC/BltB,OAEFA,EAAK,EAAkBA,GACvBA,EAAG1H,QAAQ+zB,IACW,YAAhBxrB,EAAOka,MAAsBla,EAAOssB,WACtCd,EAAMxpB,UAAUC,QAAQjC,EAAO6tB,gBAAkB,IAAIzrB,MAAM,MAE7DopB,EAAMxpB,UAAUC,IAAIjC,EAAOwtB,cAAgBxtB,EAAOka,MAClDsR,EAAMxpB,UAAUC,IAAIzC,EAAO4J,eAAiBpJ,EAAO8tB,gBAAkB9tB,EAAO+tB,eACxD,YAAhB/tB,EAAOka,MAAsBla,EAAOktB,iBACtC1B,EAAMxpB,UAAUC,IAAI,GAAGjC,EAAOwtB,gBAAgBxtB,EAAOka,gBACrDgU,EAAqB,EACjBluB,EAAOmtB,mBAAqB,IAC9BntB,EAAOmtB,mBAAqB,IAGZ,gBAAhBntB,EAAOka,MAA0Bla,EAAOitB,qBAC1CzB,EAAMxpB,UAAUC,IAAIjC,EAAO4tB,0BAEzB5tB,EAAOssB,WACTd,EAAMnwB,iBAAiB,QAASkzB,GAE7B/uB,EAAOyK,SACVuhB,EAAMxpB,UAAUC,IAAIjC,EAAO2rB,aAGjC,CACA,SAASlB,IACP,MAAMzqB,EAASR,EAAOQ,OAAOqsB,WAC7B,GAAI8B,IAAwB,OAC5B,IAAIhvB,EAAKK,EAAO6sB,WAAWltB,GACvBA,IACFA,EAAK,EAAkBA,GACvBA,EAAG1H,QAAQ+zB,IACTA,EAAMxpB,UAAUrH,OAAOqF,EAAOisB,aAC9BT,EAAMxpB,UAAUrH,OAAOqF,EAAOwtB,cAAgBxtB,EAAOka,MACrDsR,EAAMxpB,UAAUrH,OAAO6E,EAAO4J,eAAiBpJ,EAAO8tB,gBAAkB9tB,EAAO+tB,eAC3E/tB,EAAOssB,YACTd,EAAMxpB,UAAUrH,WAAWqF,EAAO6tB,gBAAkB,IAAIzrB,MAAM,MAC9DopB,EAAMlwB,oBAAoB,QAASizB,OAIrC/uB,EAAO6sB,WAAW4B,SAASzuB,EAAO6sB,WAAW4B,QAAQx2B,QAAQ+zB,GAASA,EAAMxpB,UAAUrH,UAAUqF,EAAOutB,kBAAkBnrB,MAAM,MACrI,CACAtL,EAAG,kBAAmB,KACpB,IAAK0I,EAAO6sB,aAAe7sB,EAAO6sB,WAAWltB,GAAI,OACjD,MAAMa,EAASR,EAAOQ,OAAOqsB,WAC7B,IAAI,GACFltB,GACEK,EAAO6sB,WACXltB,EAAK,EAAkBA,GACvBA,EAAG1H,QAAQ+zB,IACTA,EAAMxpB,UAAUrH,OAAOqF,EAAO8tB,gBAAiB9tB,EAAO+tB,eACtDvC,EAAMxpB,UAAUC,IAAIzC,EAAO4J,eAAiBpJ,EAAO8tB,gBAAkB9tB,EAAO+tB,mBAGhFj3B,EAAG,OAAQ,MACgC,IAArC0I,EAAOQ,OAAOqsB,WAAWpiB,QAE3Bqc,KAEA3D,IACAoN,IACA/mB,OAGJlS,EAAG,oBAAqB,UACU,IAArB0I,EAAOuO,WAChB/E,MAGJlS,EAAG,kBAAmB,KACpBkS,MAEFlS,EAAG,uBAAwB,KACzBi5B,IACA/mB,MAEFlS,EAAG,UAAW,KACZ2zB,MAEF3zB,EAAG,iBAAkB,KACnB,IAAI,GACFqI,GACEK,EAAO6sB,WACPltB,IACFA,EAAK,EAAkBA,GACvBA,EAAG1H,QAAQ+zB,GAASA,EAAMxpB,UAAUxC,EAAOyK,QAAU,SAAW,OAAOzK,EAAOQ,OAAOqsB,WAAWV,eAGpG70B,EAAG,cAAe,KAChBkS,MAEFlS,EAAG,QAAS,CAACq1B,EAAIjmC,KACf,MAAMy0B,EAAWz0B,EAAEgS,OACbiH,EAAK,EAAkBK,EAAO6sB,WAAWltB,IAC/C,GAAIK,EAAOQ,OAAOqsB,WAAWltB,IAAMK,EAAOQ,OAAOqsB,WAAWL,aAAe7sB,GAAMA,EAAG1X,OAAS,IAAMkzB,EAAS3Y,UAAUmF,SAAS3H,EAAOQ,OAAOqsB,WAAWiB,aAAc,CACpK,GAAI9tB,EAAOohB,aAAephB,EAAOohB,WAAWC,QAAUlG,IAAanb,EAAOohB,WAAWC,QAAUrhB,EAAOohB,WAAWE,QAAUnG,IAAanb,EAAOohB,WAAWE,QAAS,OACnK,MAAMyL,EAAWptB,EAAG,GAAG6C,UAAUmF,SAAS3H,EAAOQ,OAAOqsB,WAAWJ,aAEjErlB,GADe,IAAb2lB,EACG,iBAEA,kBAEPptB,EAAG1H,QAAQ+zB,GAASA,EAAMxpB,UAAUwqB,OAAOhtB,EAAOQ,OAAOqsB,WAAWJ,aACtE,IAEF,MAaM3F,EAAU,KACd9mB,EAAOL,GAAG6C,UAAUC,IAAIzC,EAAOQ,OAAOqsB,WAAW2B,yBACjD,IAAI,GACF7uB,GACEK,EAAO6sB,WACPltB,IACFA,EAAK,EAAkBA,GACvBA,EAAG1H,QAAQ+zB,GAASA,EAAMxpB,UAAUC,IAAIzC,EAAOQ,OAAOqsB,WAAW2B,2BAEnEvD,KAEF1jC,OAAOmG,OAAOsS,EAAO6sB,WAAY,CAC/B9F,OAzBa,KACb/mB,EAAOL,GAAG6C,UAAUrH,OAAO6E,EAAOQ,OAAOqsB,WAAW2B,yBACpD,IAAI,GACF7uB,GACEK,EAAO6sB,WACPltB,IACFA,EAAK,EAAkBA,GACvBA,EAAG1H,QAAQ+zB,GAASA,EAAMxpB,UAAUrH,OAAO6E,EAAOQ,OAAOqsB,WAAW2B,2BAEtErL,IACAoN,IACA/mB,KAeAsd,UACAyJ,SACA/mB,SACA2Z,OACA8H,WAEJ,CCrcA,SAASyF,GAASx4B,GAChB,IAuBIy4B,EACAC,GAxBA,OACF5wB,EAAM,aACN+oB,EAAY,GACZzxB,EAAE,KACF8P,EAAI,OACJ5G,GACEtI,EACJ8H,EAAO0hB,SAAW,CAChBC,SAAS,EACTC,QAAQ,EACRiP,SAAU,GAEZ9H,EAAa,CACXrH,SAAU,CACRjX,SAAS,EACT/L,MAAO,IACPoyB,mBAAmB,EACnBC,sBAAsB,EACtBC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,KAKvB,IAEIC,EAEAC,EACAvV,EACAwV,EACAC,EACAC,EACAC,EACAC,EAVAC,EAAqBlxB,GAAUA,EAAOkhB,SAAWlhB,EAAOkhB,SAAShjB,MAAQ,IACzEizB,EAAuBnxB,GAAUA,EAAOkhB,SAAWlhB,EAAOkhB,SAAShjB,MAAQ,IAE3EkzB,GAAoB,IAAI1zB,MAAO+C,UAQnC,SAAS4wB,EAAgBnrC,GAClBsZ,IAAUA,EAAO2G,WAAc3G,EAAOU,WACvCha,EAAEgS,SAAWsH,EAAOU,YACxBV,EAAOU,UAAU5E,oBAAoB,gBAAiB+1B,GAClDJ,GAAwB/qC,EAAE+4B,QAAU/4B,EAAE+4B,OAAOC,mBAGjDoC,IACF,CACA,MAAMgQ,EAAe,KACnB,GAAI9xB,EAAO2G,YAAc3G,EAAO0hB,SAASC,QAAS,OAC9C3hB,EAAO0hB,SAASE,OAClBwP,GAAY,EACHA,IACTO,EAAuBR,EACvBC,GAAY,GAEd,MAAMP,EAAW7wB,EAAO0hB,SAASE,OAASuP,EAAmBS,EAAoBD,GAAuB,IAAIzzB,MAAO+C,UACnHjB,EAAO0hB,SAASmP,SAAWA,EAC3BzpB,EAAK,mBAAoBypB,EAAUA,EAAWa,GAC9Cd,EAAMtyB,sBAAsB,KAC1BwzB,OAcEC,EAAMC,IACV,GAAIhyB,EAAO2G,YAAc3G,EAAO0hB,SAASC,QAAS,OAClDnjB,qBAAqBoyB,GACrBkB,IACA,IAAIpzB,OAA8B,IAAfszB,EAA6BhyB,EAAOQ,OAAOkhB,SAAShjB,MAAQszB,EAC/EN,EAAqB1xB,EAAOQ,OAAOkhB,SAAShjB,MAC5CizB,EAAuB3xB,EAAOQ,OAAOkhB,SAAShjB,MAC9C,MAAMuzB,EAlBc,MACpB,IAAIC,EAMJ,GAJEA,EADElyB,EAAOwK,SAAWxK,EAAOQ,OAAOgK,QAAQC,QAC1BzK,EAAOqI,OAAO3Z,KAAKkT,GAAWA,EAAQY,UAAUmF,SAAS,wBAEzD3H,EAAOqI,OAAOrI,EAAO4I,aAElCspB,EAEL,OAD0Bn5B,SAASm5B,EAAcxe,aAAa,wBAAyB,KAU7Dye,IACrB58B,OAAOrJ,MAAM+lC,IAAsBA,EAAoB,QAA2B,IAAfD,IACtEtzB,EAAQuzB,EACRP,EAAqBO,EACrBN,EAAuBM,GAEzBd,EAAmBzyB,EACnB,MAAM+B,EAAQT,EAAOQ,OAAOC,MACtB2xB,EAAU,KACTpyB,IAAUA,EAAO2G,YAClB3G,EAAOQ,OAAOkhB,SAASuP,kBACpBjxB,EAAOiR,aAAejR,EAAOQ,OAAO8I,MAAQtJ,EAAOQ,OAAO6I,QAC7DrJ,EAAOyW,UAAUhW,GAAO,GAAM,GAC9B2G,EAAK,aACKpH,EAAOQ,OAAOkhB,SAASsP,kBACjChxB,EAAOwU,QAAQxU,EAAOqI,OAAOpgB,OAAS,EAAGwY,GAAO,GAAM,GACtD2G,EAAK,cAGFpH,EAAOkR,OAASlR,EAAOQ,OAAO8I,MAAQtJ,EAAOQ,OAAO6I,QACvDrJ,EAAOkW,UAAUzV,GAAO,GAAM,GAC9B2G,EAAK,aACKpH,EAAOQ,OAAOkhB,SAASsP,kBACjChxB,EAAOwU,QAAQ,EAAG/T,GAAO,GAAM,GAC/B2G,EAAK,aAGLpH,EAAOQ,OAAOsL,UAChB8lB,GAAoB,IAAI1zB,MAAO+C,UAC/B3C,sBAAsB,KACpByzB,SAgBN,OAZIrzB,EAAQ,GACVN,aAAauyB,GACbA,EAAU51B,WAAW,KACnBq3B,KACC1zB,IAEHJ,sBAAsB,KACpB8zB,MAKG1zB,GAEH2zB,EAAQ,KACZT,GAAoB,IAAI1zB,MAAO+C,UAC/BjB,EAAO0hB,SAASC,SAAU,EAC1BoQ,IACA3qB,EAAK,kBAEDzc,EAAO,KACXqV,EAAO0hB,SAASC,SAAU,EAC1BvjB,aAAauyB,GACbnyB,qBAAqBoyB,GACrBxpB,EAAK,iBAEDkrB,EAAQ,CAAC7d,EAAU8d,KACvB,GAAIvyB,EAAO2G,YAAc3G,EAAO0hB,SAASC,QAAS,OAClDvjB,aAAauyB,GACRlc,IACH+c,GAAsB,GAExB,MAAMY,EAAU,KACdhrB,EAAK,iBACDpH,EAAOQ,OAAOkhB,SAASoP,kBACzB9wB,EAAOU,UAAU7E,iBAAiB,gBAAiBg2B,GAEnD/P,KAIJ,GADA9hB,EAAO0hB,SAASE,QAAS,EACrB2Q,EAMF,OALIhB,IACFJ,EAAmBnxB,EAAOQ,OAAOkhB,SAAShjB,OAE5C6yB,GAAe,OACfa,IAGF,MAAM1zB,EAAQyyB,GAAoBnxB,EAAOQ,OAAOkhB,SAAShjB,MACzDyyB,EAAmBzyB,IAAS,IAAIR,MAAO+C,UAAY2wB,GAC/C5xB,EAAOkR,OAASigB,EAAmB,IAAMnxB,EAAOQ,OAAO8I,OACvD6nB,EAAmB,IAAGA,EAAmB,GAC7CiB,MAEItQ,EAAS,KACT9hB,EAAOkR,OAASigB,EAAmB,IAAMnxB,EAAOQ,OAAO8I,MAAQtJ,EAAO2G,YAAc3G,EAAO0hB,SAASC,UACxGiQ,GAAoB,IAAI1zB,MAAO+C,UAC3BuwB,GACFA,GAAsB,EACtBO,EAAIZ,IAEJY,IAEF/xB,EAAO0hB,SAASE,QAAS,EACzBxa,EAAK,oBAEDorB,EAAqB,KACzB,GAAIxyB,EAAO2G,YAAc3G,EAAO0hB,SAASC,QAAS,OAClD,MAAM5pB,EAAW,IACgB,WAA7BA,EAAS06B,kBACXjB,GAAsB,EACtBc,GAAM,IAEyB,YAA7Bv6B,EAAS06B,iBACX3Q,KAGE4Q,EAAiBhsC,IACC,UAAlBA,EAAEw0B,cACNsW,GAAsB,EACtBC,GAAuB,EACnBzxB,EAAO2U,WAAa3U,EAAO0hB,SAASE,QACxC0Q,GAAM,KAEFK,EAAiBjsC,IACC,UAAlBA,EAAEw0B,cACNuW,GAAuB,EACnBzxB,EAAO0hB,SAASE,QAClBE,MAuBJxqB,EAAG,OAAQ,KACL0I,EAAOQ,OAAOkhB,SAASjX,UApBvBzK,EAAOQ,OAAOkhB,SAASwP,oBACzBlxB,EAAOL,GAAG9D,iBAAiB,eAAgB62B,GAC3C1yB,EAAOL,GAAG9D,iBAAiB,eAAgB82B,IAU5B,IACR92B,iBAAiB,mBAAoB22B,GAU5CH,OAGJ/6B,EAAG,UAAW,KApBR0I,EAAOL,IAA2B,iBAAdK,EAAOL,KAC7BK,EAAOL,GAAG7D,oBAAoB,eAAgB42B,GAC9C1yB,EAAOL,GAAG7D,oBAAoB,eAAgB62B,IAQ/B,IACR72B,oBAAoB,mBAAoB02B,GAY7CxyB,EAAO0hB,SAASC,SAClBh3B,MAGJ2M,EAAG,yBAA0B,MACvB+5B,GAAiBG,IACnB1P,MAGJxqB,EAAG,6BAA8B,KAC1B0I,EAAOQ,OAAOkhB,SAASqP,qBAG1BpmC,IAFA2nC,GAAM,GAAM,KAKhBh7B,EAAG,wBAAyB,CAACq1B,EAAIlsB,EAAOgU,MAClCzU,EAAO2G,WAAc3G,EAAO0hB,SAASC,UACrClN,IAAazU,EAAOQ,OAAOkhB,SAASqP,qBACtCuB,GAAM,GAAM,GAEZ3nC,OAGJ2M,EAAG,kBAAmB,MAChB0I,EAAO2G,WAAc3G,EAAO0hB,SAASC,UACrC3hB,EAAOQ,OAAOkhB,SAASqP,qBACzBpmC,KAGFkxB,GAAY,EACZwV,GAAgB,EAChBG,GAAsB,EACtBF,EAAoBv2B,WAAW,KAC7By2B,GAAsB,EACtBH,GAAgB,EAChBiB,GAAM,IACL,SAELh7B,EAAG,WAAY,KACb,IAAI0I,EAAO2G,WAAc3G,EAAO0hB,SAASC,SAAY9F,EAArD,CAGA,GAFAzd,aAAakzB,GACblzB,aAAauyB,GACT3wB,EAAOQ,OAAOkhB,SAASqP,qBAGzB,OAFAM,GAAgB,OAChBxV,GAAY,GAGVwV,GAAiBrxB,EAAOQ,OAAOsL,SAASgW,IAC5CuP,GAAgB,EAChBxV,GAAY,CAV0D,IAYxEvkB,EAAG,cAAe,MACZ0I,EAAO2G,WAAc3G,EAAO0hB,SAASC,UACzC4P,GAAe,KAEjBhqC,OAAOmG,OAAOsS,EAAO0hB,SAAU,CAC7B2Q,QACA1nC,OACA2nC,QACAxQ,UAEJ,CC3SA,SAAS,GAAa8Q,EAAchxB,GAClC,MAAMixB,EAAc,EAAoBjxB,GAKxC,OAJIixB,IAAgBjxB,IAClBixB,EAAYp2B,MAAMq2B,mBAAqB,SACvCD,EAAYp2B,MAAM,+BAAiC,UAE9Co2B,CACT,CCPA,SAAS,GAAaxD,EAAQztB,EAAS1B,GACrC,MAAM6yB,EAAc,sBAAsB7yB,EAAO,IAAIA,IAAS,KAAKmvB,EAAS,wBAAwBA,IAAW,KACzG2D,EAAkB,EAAoBpxB,GAC5C,IAAIqxB,EAAWD,EAAgB92B,cAAc,IAAI62B,EAAYnwB,MAAM,KAAK+hB,KAAK,QAK7E,OAJKsO,IACHA,EAAW,EAAc,MAAOF,EAAYnwB,MAAM,MAClDowB,EAAgBz3B,OAAO03B,IAElBA,CACT,CCNA,SAASC,GAAgBh7B,GACvB,IAAI,OACF8H,EAAM,aACN+oB,EAAY,GACZzxB,GACEY,EACJ6wB,EAAa,CACXoK,gBAAiB,CACfC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACPnD,MAAO,EACPoD,SAAU,EACVC,cAAc,KClBpB,SAAoBhzB,GAClB,MAAM,OACJ6M,EAAM,OACNrN,EAAM,GACN1I,EAAE,aACF4d,EAAY,cACZ7F,EAAa,gBACbokB,EAAe,YACfC,EAAW,gBACXC,EAAe,gBACfC,GACEpzB,EA+BJ,IAAIqzB,EA9BJv8B,EAAG,aAAc,KACf,GAAI0I,EAAOQ,OAAO6M,SAAWA,EAAQ,OACrCrN,EAAOgoB,WAAW34B,KAAK,GAAG2Q,EAAOQ,OAAOuO,yBAAyB1B,KAC7DqmB,GAAeA,KACjB1zB,EAAOgoB,WAAW34B,KAAK,GAAG2Q,EAAOQ,OAAOuO,4BAE1C,MAAM+kB,EAAwBL,EAAkBA,IAAoB,CAAC,EACrElsC,OAAOmG,OAAOsS,EAAOQ,OAAQszB,GAC7BvsC,OAAOmG,OAAOsS,EAAOqmB,eAAgByN,KAEvCx8B,EAAG,+BAAgC,KAC7B0I,EAAOQ,OAAO6M,SAAWA,GAC7B6H,MAEF5d,EAAG,gBAAiB,CAACq1B,EAAIpsB,KACnBP,EAAOQ,OAAO6M,SAAWA,GAC7BgC,EAAc9O,KAEhBjJ,EAAG,gBAAiB,KAClB,GAAI0I,EAAOQ,OAAO6M,SAAWA,GACzBsmB,EAAiB,CACnB,IAAKC,IAAoBA,IAAkBJ,aAAc,OAEzDxzB,EAAOqI,OAAOpQ,QAAQ2J,IACpBA,EAAQzF,iBAAiB,gHAAgHlE,QAAQg7B,GAAYA,EAAS93B,YAGxKw4B,GACF,IAGFr8B,EAAG,gBAAiB,KACd0I,EAAOQ,OAAO6M,SAAWA,IACxBrN,EAAOqI,OAAOpgB,SACjB4rC,GAAyB,GAE3Bv1B,sBAAsB,KAChBu1B,GAA0B7zB,EAAOqI,QAAUrI,EAAOqI,OAAOpgB,SAC3DitB,IACA2e,GAAyB,OAIjC,CDmCE,CAAW,CACTxmB,OAAQ,YACRrN,SACA1I,KACA4d,aAzEmB,KACnB,MACEta,MAAOm5B,EACPhvB,OAAQivB,EAAY,OACpB3rB,EAAM,gBACNyC,GACE9K,EACEQ,EAASR,EAAOQ,OAAO2yB,gBACvBvpB,EAAe5J,EAAO4J,eACtB6C,EAAYzM,EAAOI,UACnB6zB,EAASrqB,EAA4BmqB,EAAc,EAA1BtnB,EAA2CunB,EAAe,EAA3BvnB,EACxD2mB,EAASxpB,EAAepJ,EAAO4yB,QAAU5yB,EAAO4yB,OAChDhzB,EAAYI,EAAO8yB,MACnB1sC,ET+RV,SAAsBoZ,GACpB,OAAOnY,GACDsZ,KAAK+L,IAAIrlB,GAAK,GAAKmY,EAAO8D,SAAW9D,EAAO8D,QAAQsC,WAAajF,KAAK+L,IAAIrlB,GAAK,IAAO,EACjFA,EAAI,KAENA,CAEX,CStSc,CAAamY,GAEvB,IAAK,IAAI9Y,EAAI,EAAGe,EAASogB,EAAOpgB,OAAQf,EAAIe,EAAQf,GAAK,EAAG,CAC1D,MAAM0a,EAAUyG,EAAOnhB,GACjB8kB,EAAYlB,EAAgB5jB,GAE5BgtC,GAAgBD,EADFryB,EAAQiO,kBACiB7D,EAAY,GAAKA,EACxDmoB,EAA8C,mBAApB3zB,EAAO+yB,SAA0B/yB,EAAO+yB,SAASW,GAAgBA,EAAe1zB,EAAO+yB,SACvH,IAAIa,EAAUxqB,EAAewpB,EAASe,EAAmB,EACrDE,EAAUzqB,EAAe,EAAIwpB,EAASe,EAEtCG,GAAcl0B,EAAYe,KAAK+L,IAAIinB,GACnCd,EAAU7yB,EAAO6yB,QAEE,iBAAZA,IAAkD,IAA1BA,EAAQ33B,QAAQ,OACjD23B,EAAUhwB,WAAW7C,EAAO6yB,SAAW,IAAMrnB,GAE/C,IAAIuoB,EAAa3qB,EAAe,EAAIypB,EAAUc,EAC1CK,EAAa5qB,EAAeypB,EAAUc,EAAmB,EACzDhE,EAAQ,GAAK,EAAI3vB,EAAO2vB,OAAShvB,KAAK+L,IAAIinB,GAG1ChzB,KAAK+L,IAAIsnB,GAAc,OAAOA,EAAa,GAC3CrzB,KAAK+L,IAAIqnB,GAAc,OAAOA,EAAa,GAC3CpzB,KAAK+L,IAAIonB,GAAc,OAAOA,EAAa,GAC3CnzB,KAAK+L,IAAIknB,GAAW,OAAOA,EAAU,GACrCjzB,KAAK+L,IAAImnB,GAAW,OAAOA,EAAU,GACrClzB,KAAK+L,IAAIijB,GAAS,OAAOA,EAAQ,GACrC,MAAMsE,EAAiB,eAAeD,OAAgBD,OAAgBD,iBAA0B1tC,EAAEytC,kBAAwBztC,EAAEwtC,gBAAsBjE,KAIlJ,GAHiB,GAAa3vB,EAAQoB,GAC7BnF,MAAMgQ,UAAYgoB,EAC3B7yB,EAAQnF,MAAMi4B,OAAmD,EAAzCvzB,KAAK+L,IAAI/L,KAAKwzB,MAAMR,IACxC3zB,EAAOgzB,aAAc,CAEvB,IAAIoB,EAAiBhrB,EAAehI,EAAQ1F,cAAc,6BAA+B0F,EAAQ1F,cAAc,4BAC3G24B,EAAgBjrB,EAAehI,EAAQ1F,cAAc,8BAAgC0F,EAAQ1F,cAAc,+BAC1G04B,IACHA,EAAiB,GAAa,YAAahzB,EAASgI,EAAe,OAAS,QAEzEirB,IACHA,EAAgB,GAAa,YAAajzB,EAASgI,EAAe,QAAU,WAE1EgrB,IAAgBA,EAAen4B,MAAMq4B,QAAUX,EAAmB,EAAIA,EAAmB,GACzFU,IAAeA,EAAcp4B,MAAMq4B,SAAWX,EAAmB,GAAKA,EAAmB,EAC/F,CACF,GAgBA9kB,cAdoB9O,IACMP,EAAOqI,OAAOrC,IAAIpE,GAAW,EAAoBA,IACzD3J,QAAQ0H,IACxBA,EAAGlD,MAAM+oB,mBAAqB,GAAGjlB,MACjCZ,EAAGxD,iBAAiB,gHAAgHlE,QAAQg7B,IAC1IA,EAASx2B,MAAM+oB,mBAAqB,GAAGjlB,WAU3CmzB,YAAa,KAAM,EACnBD,gBAAiB,KAAM,CACrB7kB,qBAAqB,KAG3B,CRmwHArnB,OAAOkE,KAAK04B,IAAYlsB,QAAQ88B,IAC9BxtC,OAAOkE,KAAK04B,GAAW4Q,IAAiB98B,QAAQ+8B,IAC9CvM,GAAOrhC,UAAU4tC,GAAe7Q,GAAW4Q,GAAgBC,OAG/DvM,GAAOgD,IAAI,CApwHX,SAAgBvzB,GACd,IAAI,OACF8H,EAAM,GACN1I,EAAE,KACF8P,GACElP,EACJ,MAAM3B,EAAS,IACf,IAAI0+B,EAAW,KACXC,EAAiB,KACrB,MAAMC,EAAgB,KACfn1B,IAAUA,EAAO2G,WAAc3G,EAAO2T,cAC3CvM,EAAK,gBACLA,EAAK,YAsCDguB,EAA2B,KAC1Bp1B,IAAUA,EAAO2G,WAAc3G,EAAO2T,aAC3CvM,EAAK,sBAEP9P,EAAG,OAAQ,KACL0I,EAAOQ,OAAO6iB,qBAAmD,IAA1B9sB,EAAO8+B,eAxC7Cr1B,IAAUA,EAAO2G,WAAc3G,EAAO2T,cAC3CshB,EAAW,IAAII,eAAenN,IAC5BgN,EAAiB3+B,EAAO+H,sBAAsB,KAC5C,MAAM,MACJ1D,EAAK,OACLmK,GACE/E,EACJ,IAAIs1B,EAAW16B,EACXwU,EAAYrK,EAChBmjB,EAAQjwB,QAAQs9B,IACd,IAAI,eACFC,EAAc,YACdC,EAAW,OACX/8B,GACE68B,EACA78B,GAAUA,IAAWsH,EAAOL,KAChC21B,EAAWG,EAAcA,EAAY76B,OAAS46B,EAAe,IAAMA,GAAgBE,WACnFtmB,EAAYqmB,EAAcA,EAAY1wB,QAAUywB,EAAe,IAAMA,GAAgBG,aAEnFL,IAAa16B,GAASwU,IAAcrK,GACtCowB,QAINF,EAASW,QAAQ51B,EAAOL,MAoBxBpJ,EAAOsF,iBAAiB,SAAUs5B,GAClC5+B,EAAOsF,iBAAiB,oBAAqBu5B,MAE/C99B,EAAG,UAAW,KApBR49B,GACF3+B,EAAOiI,qBAAqB02B,GAE1BD,GAAYA,EAASY,WAAa71B,EAAOL,KAC3Cs1B,EAASY,UAAU71B,EAAOL,IAC1Bs1B,EAAW,MAiBb1+B,EAAOuF,oBAAoB,SAAUq5B,GACrC5+B,EAAOuF,oBAAoB,oBAAqBs5B,IAEpD,EAEA,SAAkBl9B,GAChB,IAAI,OACF8H,EAAM,aACN+oB,EAAY,GACZzxB,EAAE,KACF8P,GACElP,EACJ,MAAM49B,EAAY,GACZv/B,EAAS,IACTw/B,EAAS,SAAUr9B,EAAQs9B,QACf,IAAZA,IACFA,EAAU,CAAC,GAEb,MACMf,EAAW,IADI1+B,EAAO0/B,kBAAoB1/B,EAAO2/B,wBACrBC,IAIhC,GAAIn2B,EAAOmZ,oBAAqB,OAChC,GAAyB,IAArBgd,EAAUluC,OAEZ,YADAmf,EAAK,iBAAkB+uB,EAAU,IAGnC,MAAMC,EAAiB,WACrBhvB,EAAK,iBAAkB+uB,EAAU,GACnC,EACI5/B,EAAO+H,sBACT/H,EAAO+H,sBAAsB83B,GAE7B7/B,EAAOwE,WAAWq7B,EAAgB,KAGtCnB,EAASW,QAAQl9B,EAAQ,CACvB29B,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,UAAWt2B,EAAOgI,iBAA2C,IAAtBguB,EAAQM,WAAmCN,GAASM,UAC3FC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAE/ET,EAAUzmC,KAAK4lC,EACjB,EAyBAlM,EAAa,CACXkM,UAAU,EACVuB,gBAAgB,EAChBC,sBAAsB,IAExBn/B,EAAG,OA7BU,KACX,GAAK0I,EAAOQ,OAAOy0B,SAAnB,CACA,GAAIj1B,EAAOQ,OAAOg2B,eAAgB,CAChC,MAAME,EAAmB,EAAe12B,EAAO+qB,QAC/C,IAAK,IAAI7jC,EAAI,EAAGA,EAAIwvC,EAAiBzuC,OAAQf,GAAK,EAChD6uC,EAAOW,EAAiBxvC,GAE5B,CAEA6uC,EAAO/1B,EAAO+qB,OAAQ,CACpBuL,UAAWt2B,EAAOQ,OAAOi2B,uBAI3BV,EAAO/1B,EAAOU,UAAW,CACvB21B,YAAY,GAdqB,IA6BrC/+B,EAAG,UAZa,KACdw+B,EAAU79B,QAAQg9B,IAChBA,EAAS0B,eAEXb,EAAU3uB,OAAO,EAAG2uB,EAAU7tC,SASlC,IU5OO,IAAM2uC,GAAmB,oBAAAA,IAAA,CAkE3B,OAjEDA,EAGcC,sBAAd,WACI,IAAMp+B,EAAa1C,EAAaY,sBAEhC,MAAO,CACH2S,MAAM,EACN1S,aAAc6B,EAAW7B,aACzBC,cAAe4B,EAAW5B,cAC1B6qB,SAAU,CACNhjB,MAAO,IACPqyB,sBAAsB,GAE1BnI,QAAS,CAAC8H,IAElB,EAEAkG,EAGcE,2BAAd,WACI,IAAMr+B,EAAa1C,EAAaY,sBAEhC,MAAO,CACH2S,MAAM,EACN1S,aAAc6B,EAAW1B,qBACzBF,cAAe4B,EAAW3B,sBAC1B8xB,QAAS,GAEjB,EAEAgO,EAGcG,qBAAd,WAGI,MAAO,CACHrV,SAAU,CACNhjB,MAJevJ,EAAeC,oBAK9B27B,sBAAsB,GAE1BznB,MAAM,EACN1S,aAAc,GACdyW,OAAQ,YACRxB,gBAAgB,EAChBhV,cAAe,EACfs8B,gBAAiB,CACbC,OAAQ,EACRE,MAAO,IACPC,SAAU,EACVC,cAAc,EACdH,QAAS,GAEbxG,WAAY,CACRltB,GAAI,qBACJ+a,KAAM,WAEV0G,WAAY,CACRC,OAAQ,sBACRC,OAAQ,uBAEZsH,QAAS,CAACsK,GAAiBvH,GAAYsB,GAAYyD,IAE3D,EAACkG,CAAA,CAlE2B,GAwEnBI,GAAa,oBAAAA,IAAA,CAsHrB,OAnHDA,EAGcxvC,OAAd,SAAqBgQ,EAAkBy/B,EAAsB1/B,GACzD,IAAMyI,EAAS,IAAIyoB,GAAOjxB,EAAUy/B,GAC9BC,EAAa3/B,GAAMC,EAGzB,OADA9O,KAAKyuC,UAAUv/B,IAAIs/B,EAAYl3B,GACxBA,CACX,EAEAg3B,EAGclmC,YAAd,SAA0ByG,GACtB,OAAO7O,KAAKyuC,UAAUvqC,IAAI2K,EAC9B,EAEAy/B,EAGc/L,QAAd,SAAsB1zB,GAClB,IAAMyI,EAAStX,KAAKyuC,UAAUvqC,IAAI2K,GAC9ByI,IACAA,EAAOirB,UACPviC,KAAKyuC,UAAS,OAAQ5/B,GAE9B,EAEAy/B,EAGcI,WAAd,WACI1uC,KAAKyuC,UAAUl/B,QAAQ,SAAC+H,EAAQzI,GAC5ByI,EAAOirB,SACX,GACAviC,KAAKyuC,UAAUE,OACnB,EAEAL,EAGcM,gBAAd,WACI,IAAML,EAASL,GAAoBC,wBACnC,OAAOnuC,KAAKlB,OAAO,aAAcyvC,EAAQ,YAC7C,EAEAD,EAGcO,qBAAd,WACI,IAAMN,EAASL,GAAoBE,6BACnC,OAAOpuC,KAAKlB,OAAO,kBAAmByvC,EAAQ,iBAClD,EAEAD,EAGcQ,eAAd,WACI,IAAMP,EAASL,GAAoBG,uBACnC,OAAOruC,KAAKlB,OAAO,YAAayvC,EAAQ,WAC5C,EAEAD,EAGcS,aAAd,SAA2BlgC,GACvB,IAAMyI,EAAStX,KAAKoI,YAAYyG,GAC5ByI,GACAA,EAAOwJ,QAEf,EAEAwtB,EAGcxiB,QAAd,SAAsBjd,EAAY9B,EAAegL,GAC7C,IAAMT,EAAStX,KAAKoI,YAAYyG,GAC5ByI,GACAA,EAAOwU,QAAQ/e,EAAOgL,EAE9B,EAEAu2B,EAGcU,cAAd,SAA4BngC,GACxB,IAAMyI,EAAStX,KAAKoI,YAAYyG,GAC5ByI,GAAUA,EAAO0hB,UACjB1hB,EAAO0hB,SAAS2Q,OAExB,EAEA2E,EAGcW,aAAd,SAA2BpgC,GACvB,IAAMyI,EAAStX,KAAKoI,YAAYyG,GAC5ByI,GAAUA,EAAO0hB,UACjB1hB,EAAO0hB,SAAS/2B,MAExB,EAEAqsC,EAGcY,gBAAd,WACI,OAAO,IAAIxgC,IAAI1O,KAAKyuC,UACxB,EAEAH,EAGca,YAAd,SAA0BtgC,GACtB,OAAO7O,KAAKyuC,UAAUx/B,IAAIJ,EAC9B,EAACy/B,CAAA,CAtHqB,GAAbA,GACMG,UAAiC,IAAI//B,IA2HjD,ICrMM0gC,GAAgB,oBAAAA,IAAA,CA4DxB,OA5DwBA,EAClBC,gBAAP,SAAuBd,GACnB,IAAMe,EAAYjgC,SAASuE,cAAc,OAazC,OAZA07B,EAAUtwB,UAAYuvB,EAAOvvB,UAEzBuvB,EAAO1/B,KACPygC,EAAUzgC,GAAK0/B,EAAO1/B,IAGtB0/B,EAAOlyB,SACPizB,EAAUv7B,MAAMsI,OAAkC,iBAAlBkyB,EAAOlyB,OAC9BkyB,EAAOlyB,OAAM,KAChBkyB,EAAOlyB,QAGVizB,CACX,EAACF,EAEMG,sBAAP,SAA6BvwB,GACzB,IAAM1H,EAASjI,SAASuE,cAAc,OAEtC,OADA0D,EAAO0H,UAAYA,EACZ1H,CACX,EAAC83B,EAEMI,oBAAP,SAA2B3gC,GACvB,IAAM4gC,EAAUpgC,SAASuE,cAAc,OAKvC,OAJA67B,EAAQzwB,UAAY,iBAChBnQ,IACA4gC,EAAQ5gC,GAAKA,GAEV4gC,CACX,EAACL,EAEMM,kBAAP,SAAyB1wB,EAAmBlE,GACxC,IAAM6I,EAAQtU,SAASuE,cAAc,OAGrC,OAFA+P,EAAM3E,UAAYA,EAClB2E,EAAM7I,UAAYA,EACX6I,CACX,EAACyrB,EAEMO,uBAAP,SAA8B3wB,GAC1B,IAAM0Z,EAAarpB,SAASuE,cAAc,OAE1C,OADA8kB,EAAW1Z,UAAYA,EAChB0Z,CACX,EAAC0W,EAEMQ,aAAP,SAAoB/gC,EAAYuB,EAAgBy/B,EAAmBp2B,GAC/D,MAAO,eAAe5K,EAAE,aAAauB,EAAM,iEACnBy/B,EAAS,uDACKp2B,EAAI,mCAE9C,EAAC21B,EAEMU,aAAP,SAAoBjhC,EAAYmQ,EAAmB7R,QAAW,IAAXA,IAAAA,EAAc,IAC7D,IAAM4iC,EAAS1gC,SAASuE,cAAc,UAKtC,OAJAm8B,EAAOlhC,GAAKA,EACZkhC,EAAO/tC,KAAO,cACd+tC,EAAO/wB,UAAYA,EACnB+wB,EAAO5iC,IAAMA,EACN4iC,CACX,EAACX,CAAA,CA5DwB,GAkEhBY,GAAiB,oBAAAA,IAAA,CAyCzB,OAzCyBA,EACnBlxC,OAAP,WACI,IAAMiR,EAAa1C,EAAaY,sBAE5BgiC,EAA2B,GADX7gC,EAAEvB,QAAQqE,SAAW,GACN,GAE7Bg+B,EAAkBd,GAAiBC,gBAAgB,CACrDrwB,UAAW5V,EAAUyC,YAAYV,oBACjC0D,GAAIzF,EAAU6B,cAAcE,sBAG5B4E,EAAWzC,WACX4iC,EAAgBn8B,MAAM7B,MAAQ+9B,EAAa,KAC3CC,EAAgBn8B,MAAMgP,YAA4B,KAAbktB,EAAsB,MAG/D,IAAM34B,EAAS83B,GAAiBG,sBAAsBnmC,EAAUyC,YAAYE,WACtE0jC,EAAUL,GAAiBI,sBAGd/iC,EAAeQ,oBACvBsC,QAAQ,SAAA4gC,GACf,IAAMxsB,EAAQyrB,GAAiBM,kBAC3B,eAAc,wCACyBS,EAAK/iC,KAAI,YAAW+iC,EAAKhjC,IAAG,QAEvEsiC,EAAQW,YAAYzsB,EACxB,GAGA,IAAM0sB,EAAajB,GAAiBO,uBAAuB,sBACrDW,EAAalB,GAAiBO,uBAAuB,sBACrDxL,EAAaiL,GAAiBO,uBAAuB,qBAQ3D,OANAr4B,EAAO84B,YAAYX,GACnBn4B,EAAO84B,YAAYC,GACnB/4B,EAAO84B,YAAYE,GACnBh5B,EAAO84B,YAAYjM,GACnB+L,EAAgBE,YAAY94B,GAErB44B,CACX,EAACF,CAAA,CAzCyB,GA+CjBO,GAAkB,oBAAAA,IAAA,CA6D1B,OA7D0BA,EACpBzxC,OAAP,WACI,IAAMiR,EAAa1C,EAAaY,sBAC1BuiC,EAAUphC,EAAE,YAEZ8gC,EAAkBd,GAAiBC,gBAAgB,CACrDrwB,UAAW5V,EAAUyC,YAAYX,qBACjC2D,GAAIzF,EAAU6B,cAAcC,uBAG1BoM,EAAS83B,GAAiBG,sBAAsBnmC,EAAUyC,YAAYC,YACtE2kC,EAAwBrB,GAAiBC,gBAAgB,CAC3DrwB,UAAW,0BAGfkxB,EAAgBE,YAAYK,GAC5BA,EAAsBL,YAAY94B,GAElC,IAAMm4B,EAAUL,GAAiBI,oBAAoBpmC,EAAU6B,cAAcC,qBAAqB2X,QAAQ,YAAa,YACvHvL,EAAO84B,YAAYX,GAGnB,IAAK,IAAIjxC,EAAI,EAAGA,EAAIgyC,EAAQjxC,OAAQf,IAAK,CACrC,IAAMob,EAAM42B,EAAQhyC,GACdkyC,EAASthC,EAAEwK,GAAK5T,KAAK,KAAKmK,KAAK,SAAW,GAC1CwgC,EAAgBvhC,EAAEwK,GAAKpJ,IAAI,eAAiB,GAC5CgzB,EAAUp0B,EAAEwK,GAAK5T,KAAK,iBAAiByT,QAAU,GACjDm3B,EAAexhC,EAAEwK,GAAK5T,KAAK,iBAAiBwK,IAAI,UAAY,GAE5DqgC,EAAe,YAAYH,EAAM,oCACrB3gC,EAAWzC,SAAWlE,EAAUyC,YAAYW,8BAAgCpD,EAAUyC,YAAYU,wBAAsB,6CAC7GokC,EAAa,8JACkBC,EAAY,KAAKpN,EAAO,mDAI9E7f,EAAQyrB,GAAiBM,kBAAkBtmC,EAAUyC,YAAYS,iBAAkBukC,GACzFpB,EAAQW,YAAYzsB,EACxB,CAEA,OAAOusB,CACX,EAACK,EAEMO,eAAP,SAAsBxB,GAClB,IAAMyB,EAAmBzB,EAAU97B,cAAc,0BAC7Cu9B,IACA3hC,EAAE2hC,GAAkBpgB,QAAQ,+EAC5BvhB,EAAE2hC,GAAkBl+B,OAAO,06CAcnC,EAAC09B,CAAA,CA7D0B,GAmElBS,GAAuB,oBAAAA,IAAA,CAuC/B,OAvC+BA,EACzBlyC,OAAP,SAAcmyC,GACV,IAAMC,EAAgB9B,GAAiBC,gBAAgB,CACnDrwB,UAAW,wBACXnQ,GAAIzF,EAAU6B,cAAcG,0BAEhC8lC,EAAcp2B,UAAY,4DAE1B,IAAMxD,EAAS83B,GAAiBG,sBAAsBnmC,EAAUyC,YAAYG,iBACtEyjC,EAAUL,GAAiBI,sBAEjCyB,EAAa1hC,QAAQ,SAAAlK,GACjB,IAAM8rC,EAAmB9gC,SAAShL,EAAK+rC,YAAc,OAG/CP,EAAe,8FAFexrC,EAAKgsC,MAEpB,ieAKGF,EAAgB,4GAMlCxtB,EAAQyrB,GAAiBM,kBAAkBtmC,EAAUyC,YAAYS,iBAAkBukC,GACzFpB,EAAQW,YAAYzsB,EACxB,GAEArM,EAAO84B,YAAYX,GAEnB,IAAMH,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW,4BAKf,OAHAswB,EAAUc,YAAYc,GACtB5B,EAAUc,YAAY94B,GAEfg4B,CACX,EAAC0B,CAAA,CAvC+B,GA6CvBM,GAAyB,oBAAAA,IAAA,CA4CjC,OA5CiCA,EAC3BxyC,OAAP,SAAcyyC,GACV,IAAMjC,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW5V,EAAUyC,YAAYR,uBACjCwD,GAAIzF,EAAU6B,cAAcI,yBAG5BmmC,EAAuB,GACvB3hC,EAAezG,EAAUc,qBAE7BqnC,EAAyBhiC,QAAQ,SAAAlK,GAC7BwK,IACA2hC,GAAwBpC,GAAiBQ,aAAa,sBAC5B//B,EACtBA,EAAaqG,WACb7Q,EAAKosC,OACLpsC,EAAKrD,OAEb,GAEA,IAAM0vC,EAActC,GAAiBC,gBAAgB,CACjDrwB,UAAW,gBAGT2yB,EAAgBviC,EAAE,qBAAqB8C,SAAW,EAClD0/B,EAAiB,iBACjBxC,GAAiBQ,aAAa,uBAAwB,IAAK,aAAc,MAAK,iBAC9ER,GAAiBQ,aAAa,uBAAwB,IAAK,gBAAiB,MAAK,iBACjFR,GAAiBQ,aAAa,uBAAwB,IAAK,cAAe,MAAK,iBAC/ER,GAAiBQ,aAAa,uBAAwB,IAAK,eAAgB,MAAK,aActF,OAXA8B,EAAY52B,UAAY,2DACwB62B,EAAa,2EAE/CC,EAAc,yBACdJ,EAAoB,kCACXpoC,EAAU6B,cAAcQ,2BAA0B,8HAKzE6jC,EAAUc,YAAYsB,GACfpC,CACX,EAACgC,CAAA,CA5CiC,GAkDzBO,GAAmB,oBAAAA,IAAA,CAkH3B,OAlH2BA,EACrBC,qBAAP,WACI,IAAM/hC,EAAa1C,EAAaY,sBAC1BqhC,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW5V,EAAUyC,YAAYI,gBACjCoQ,OAAQjN,EAAE,uBAAuBoB,IAAI,WAAa,KA0ChDu/B,EAASX,GAAiBU,aAC5B1mC,EAAU6B,cAAcM,aACxB,eAkBJ,OAfA+jC,EAAUx0B,UAAY,0BACP1R,EAAU6B,cAAcS,oBAAmB,kFAAiFqE,EAAWzC,SAAW,GAAK,GADhJ,orBAKHlE,EAAU6B,cAAcU,iBALrB,msBAQHvC,EAAU6B,cAAcW,iBARrB,mrBActB0jC,EAAUc,YAAYL,GACfT,CACX,EAACuC,EAEME,qBAAP,WACI,IAAMzC,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW5V,EAAUyC,YAAYK,gBACjCmQ,OAAQjN,EAAE,uBAAuBoB,IAAI,WAAa,KAGtD,OADA8+B,EAAU0C,UAAYjsC,IAAAA,WAAeksC,MAAM,+CACpC3C,CACX,EAACuC,EAEMK,0BAAP,WACI,IAAM5C,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW5V,EAAUyC,YAAYM,qBACjCkQ,OAAQjN,EAAE,uBAAuBoB,IAAI,WAAa,KAGtD,OADA8+B,EAAU0C,UAAYjsC,IAAAA,WAAeksC,MAAM,+CACpC3C,CACX,EAACuC,EAEMM,mCAAP,WACI,IAAM7C,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW5V,EAAUyC,YAAYO,+BACjCiQ,OAAQjN,EAAE,uBAAuBoB,IAAI,WAAa,KAGhDu/B,EAASX,GAAiBU,aAC5B1mC,EAAU6B,cAAcO,qBACxB,sBAIJ,OADA8jC,EAAUc,YAAYL,GACfT,CACX,EAACuC,EAEMO,iBAAP,WACI,IAAM9C,EAAYF,GAAiBC,gBAAgB,CAC/CrwB,UAAW,sBACXnQ,GAAIzF,EAAU6B,cAAcK,cAOhC,OAJAgkC,EAAUv7B,MAAMs+B,QAAU,eAC1B/C,EAAUv7B,MAAMmP,UAAY,MAC5BosB,EAAUx0B,UAAY,6GAEfw0B,CACX,EAACuC,CAAA,CAlH2B,GCrQnBS,GAAa,WAQtB,SAAAA,IAAsB,KALd3jC,iBAAW,OACX4jC,kBAAY,OACZC,cAAQ,OACRC,eAAyB,EAG7BzyC,KAAK2O,YAAc3G,EAAYI,cAC/BpI,KAAKuyC,aAAe/jC,EAAapG,cACjCpI,KAAKwyC,SAAW,CACZE,YAAa,QACbC,eAAgB,EAChBrlC,SAAUD,EAAaC,WACvBmlC,eAAe,EAEvB,CAACH,EAEalqC,YAAd,WAII,OAHKkqC,EAAcjqC,WACfiqC,EAAcjqC,SAAW,IAAIiqC,GAE1BA,EAAcjqC,QACzB,EAEA,IAAA/C,EAAAgtC,EAAA5zC,UAqRoB,OArRpB4G,EAGastC,WAAU,eAAAC,EAAAtuC,EAAAzC,IAAAA,KAAvB,SAAA6D,IAAA,IAAArD,EAAA,OAAAR,IAAAA,KAAA,SAAAgE,GAAA,cAAAA,EAAAvD,KAAAuD,EAAAtE,MAAA,WACQxB,KAAKyyC,cAAe,CAAF3sC,EAAAtE,KAAA,eAAAsE,EAAA5D,OAAA,iBAAA4D,EAAAvD,KAAA,EAKlBvC,KAAK8yC,uBACL9yC,KAAKyyC,eAAgB,EACrBzyC,KAAKwyC,SAASC,eAAgB,EAAK3sC,EAAAtE,KAAA,eAEyB,MAFzBsE,EAAAvD,KAAA,EAAAD,EAAAwD,EAAA,SAEnCK,QAAQC,MAAM,sCAAqC9D,GAASA,EAAA,wBAAAwD,EAAA7D,OAAA,EAAA0D,EAAA,iBAV7C,OAatB,WAbsB,OAAAktC,EAAApuC,MAAC,KAADD,UAAA,KAAAc,EAehBmtC,cAAP,WACI,OAAOzyC,KAAKyyC,aAChB,EAEAntC,EAGQwtC,qBAAR,WAAqC,IAAA7rC,EAAA,MACjC6L,EAAAA,EAAAA,QAAOigC,IAAAA,UAAyB,OAAQ,SAACC,GAGnB,SAFAjtC,IAAAA,QAAY7B,IAAI,cAG9B+C,EAAKgsC,sBAAsBD,EAEnC,EACJ,EAEA1tC,EAGQ2tC,sBAAR,SAA8BC,GAAwB,IAAApjC,EAAA,KAClD9P,KAAKuyC,aAAahgC,yBAElB,IAAM4gC,EAAOC,YAAY,WACjBF,EAAKG,MACLC,cAAcH,GACdrjC,EAAKyjC,yBAAyBL,GAEtC,EAAG9pC,EAAUC,WACjB,EAEA/D,EAGciuC,yBAAwB,eAAAC,EAAAjvC,EAAAzC,IAAAA,KAAtC,SAAA8G,EAAuCsqC,GAAgB,IAAAO,EAAAC,EAAA,OAAA5xC,IAAAA,KAAA,SAAAgH,GAAA,cAAAA,EAAAvG,KAAAuG,EAAAtH,MAAA,UAAAsH,EAAAvG,KAAA,GAG3C8M,SAASkB,eAAenH,EAAU6B,cAAcE,qBAAsB,CAAFrC,EAAAtH,KAAA,eAAAsH,EAAA5G,OAAA,iBASxE,OAJMuxC,EAAWzD,GAAkBlxC,SACnCsQ,EAAEhG,EAAUgB,UAAUI,mBAAmBmmB,QAAQ8iB,GACjDnF,GAAcQ,iBAEdhmC,EAAAtH,KAAA,EACMxB,KAAK2O,YAAYjG,cAAa,OAGpC1I,KAAK2zC,2BAA2B7qC,EAAAtH,KAAA,eAAAsH,EAAAvG,KAAA,EAAAmxC,EAAA5qC,EAAA,SAGhC3C,QAAQC,MAAM,sCAAqCstC,GAAS,wBAAA5qC,EAAA7G,OAAA,EAAA2G,EAAA,iBAnB9B,OAqBrC,SArBqCvC,GAAA,OAAAmtC,EAAA/uC,MAAC,KAADD,UAAA,KAuBtCc,EAGQquC,yBAAR,WAAyC,IAAAvhC,EAAA,KAC/BwhC,EAAgBR,YAAY,WAC1BhhC,EAAKzD,YAAYzF,oBACjBoqC,cAAcM,GACdxhC,EAAKyhC,oCAEb,EAAGzqC,EAAUG,oBACjB,EAEAjE,EAGQuuC,kCAAR,WACI,IACI,GAAIxkC,SAASkB,eAAenH,EAAU6B,cAAcC,sBAChD,OAGJlL,KAAK8zC,uBACL9zC,KAAK+zC,mBACL/zC,KAAKg0C,yBACLh0C,KAAKi0C,qBACLj0C,KAAKk0C,4BAET,CAAE,MAAO9tC,GACLD,QAAQC,MAAM,gDAAiDA,EACnE,CACJ,EAEAd,EAGQwuC,qBAAR,WACI,IAAMK,EAAY5D,GAAmBzxC,SACrCsQ,EAAEhG,EAAUgB,UAAUK,mBAAmBkmB,QAAQwjB,GAEjD5D,GAAmBO,eAAeqD,GAClC7F,GAAcM,kBAGd5uC,KAAKo0C,qBAAqBD,GAG1B/kC,EAAEhG,EAAUgB,UAAUM,WAAW+H,SACjCzS,KAAKq0C,mBACT,EAEA/uC,EAGQ8uC,qBAAR,SAA6BE,GACzB,IAAMrD,EAAejxC,KAAK2O,YAAYpG,qBAAqB/C,UAC3D,GAAIyrC,GAAgBA,EAAa1xC,OAAS,EAAG,CACzC,IAAMg1C,EAAiBvD,GAAwBlyC,OAAOmyC,GACtD7hC,EAAEklC,GAAiBzhC,OAAO0hC,GAC1BjG,GAAcO,sBAClB,CACJ,EAEAvpC,EAGQyuC,iBAAR,WACI,IAAMS,EAAkBplC,EAAEhG,EAAUgB,UAAUK,mBAGxCgqC,EAAiB5C,GAAoBC,uBACrC4C,EAAiB7C,GAAoBE,uBACrC4C,EAA+B9C,GAAoBM,qCACnDyC,EAAsB/C,GAAoBK,4BAGhDsC,EAAgB7jB,QAAQ8jB,GACxBD,EAAgB7jB,QAAQ+jB,GACxBF,EAAgB7jB,QAAQgkB,GACxBH,EAAgB7jB,QAAQikB,EAC5B,EAEAtvC,EAGQ0uC,uBAAR,WACI,IAAMzC,EAA2BvxC,KAAK2O,YAAYnG,iCAAiChD,WAAa,GAC1FqvC,EAAmBvD,GAA0BxyC,OAAOyyC,GAE1DniC,EAAEhG,EAAUgB,UAAUK,mBAAmBkmB,QAAQkkB,EACrD,EAEAvvC,EAGQ2uC,mBAAR,WACI,IAAM1C,EAA2BvxC,KAAK2O,YAAYnG,iCAAiChD,WAAa,GAChGsvC,EAAgE90C,KAAK+0C,wBAAwBxD,GAArF5hC,EAAuBmlC,EAAvBnlC,wBAAyBC,EAAYklC,EAAZllC,aAAcC,EAAYilC,EAAZjlC,aAE/C7P,KAAKuyC,aAAa7iC,sBAAsBC,EAAyBC,EAAcC,GAC/E7P,KAAKuyC,aAAapgC,sBACtB,EAEA7M,EAGQyvC,wBAAR,SAAgCxD,GAC5B,IAAMxhC,EAAa1C,EAAaY,sBAC1B0B,EAAmD,CAAC,EACpDC,EAA6B,CAAC,EAChCC,EAAezG,EAAUc,qBAG7BqnC,EAAyBhiC,QAAQ,SAAAlK,GAC7BwK,IACAF,EAAwBE,GAAgB,CAAEiC,IAAKzM,EAAKyM,MACxD,GAGA,IAAIkjC,EAAgB,EACpBplC,EAAa,GAAK,EAElB,IAAK,IAAIpR,EAAI,EAAGA,GAAKqR,EAAcrR,IAAK,CACpC,IACMy2C,EADgB7lC,EAAE,uBAAuB5Q,GACfwT,aAEtB,IAANxT,GAAiB,IAANA,IAIL,IAANA,GAAWy2C,GACsB7lC,EAAE,IAAIhG,EAAU6B,cAAcQ,4BACtCyG,MAAM+iC,GAG/BA,IACArlC,EAAapR,EAAI,GAAKy2C,EAAYD,EAAgBjlC,EAAWxB,aAC7DymC,GAAiBC,GAEzB,CAEA,MAAO,CAAEtlC,wBAAAA,EAAyBC,aAAAA,EAAcC,aAAAA,EACpD,EAEAvK,EAGQ4uC,2BAAR,WACIl0C,KAAKuyC,aAAa7/B,2BAEb3M,IAAAA,QAAYmvC,MACbl1C,KAAKm1C,eAEb,EAEA7vC,EAGQ6vC,cAAR,WACI,IAAMC,EAAavD,GAAoBO,mBACvChjC,EAAEhG,EAAUgB,UAAUY,kBAAkB2lB,QAAQykB,EACpD,EAEA9vC,EAGQ+uC,kBAAR,WACQr0C,KAAKwyC,SAASllC,WACd8B,EAAEhG,EAAUgB,UAAUC,KAAKmG,IAAI,aAAc,UAC7CpB,EAAEhG,EAAUgB,UAAUG,aAAaiG,IAAI,CACnC,aAAc,OACd,WAAc,KAG1B,EAEAlL,EAGO+vC,YAAP,WACI,OAAAtwC,EAAA,GAAY/E,KAAKwyC,SACrB,EAEAltC,EAGOgwC,eAAP,SAAsBnU,GAClBnhC,KAAKwyC,SAAQztC,EAAA,GAAQ/E,KAAKwyC,SAAarR,EAC3C,EAEA77B,EAGOiwC,QAAP,WACIv1C,KAAKuyC,aAAa9iC,SAClB6+B,GAAcI,aACd1uC,KAAKyyC,eAAgB,EACrBzyC,KAAKwyC,SAASC,eAAgB,CAClC,EAEAntC,EAGakwC,QAAO,eAAAC,EAAAlxC,EAAAzC,IAAAA,KAApB,SAAA4zC,IAAA,OAAA5zC,IAAAA,KAAA,SAAA6zC,GAAA,cAAAA,EAAApzC,KAAAozC,EAAAn0C,MAAA,OACmB,OAAfxB,KAAKu1C,UAAUI,EAAAn0C,KAAA,EACTxB,KAAK4yC,aAAY,wBAAA+C,EAAA1zC,OAAA,EAAAyzC,EAAA,SAFP,OAGnB,WAHmB,OAAAD,EAAAhxC,MAAC,KAADD,UAAA,KAAA8tC,CAAA,CA/SE,GAAbA,GACMjqC,cAAQ,EChB3BtC,IAAAA,aAAiBgU,IAAI,gCAA+BxV,EAAAzC,IAAAA,KAAE,SAAA6D,IAAA,IAAAiwC,EAAAtzC,EAAA,OAAAR,IAAAA,KAAA,SAAAgE,GAAA,cAAAA,EAAAvD,KAAAuD,EAAAtE,MAAA,OAEG,OAFHsE,EAAAvD,KAAA,EAExCqzC,EAAgBtD,GAAclqC,cAAatC,EAAAtE,KAAA,EAC3Co0C,EAAchD,aAAY,OAEhCzsC,QAAQ0vC,IAAI,yDAAyD/vC,EAAAtE,KAAA,eAAAsE,EAAAvD,KAAA,EAAAD,EAAAwD,EAAA,SAErEK,QAAQC,MAAM,qDAAoD9D,GAAS,wBAAAwD,EAAA7D,OAAA,EAAA0D,EAAA,iB", "sources": ["webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regenerator.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorAsyncGen.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorKeys.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/OverloadYield.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/regenerator/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorAsyncIterator.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorAsync.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorRuntime.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/typeof.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorDefine.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/regeneratorValues.js", "webpack://@justoverclock/header-slideshow/webpack/bootstrap", "webpack://@justoverclock/header-slideshow/webpack/runtime/compat get default export", "webpack://@justoverclock/header-slideshow/webpack/runtime/define property getters", "webpack://@justoverclock/header-slideshow/webpack/runtime/hasOwnProperty shorthand", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['forum/app']\"", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/extends.js", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['common/extend']\"", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['forum/components/HeaderPrimary']\"", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@justoverclock/header-slideshow/./src/forum/services/data-service.ts", "webpack://@justoverclock/header-slideshow/./src/forum/config/app-config.ts", "webpack://@justoverclock/header-slideshow/./src/forum/services/event-manager.ts", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/ssr-window.esm.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/utils.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/swiper-core.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/create-element-if-not-defined.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/navigation.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/classes-to-selector.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/pagination.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/autoplay.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/effect-target.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/create-shadow.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/effect-coverflow.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/effect-init.mjs", "webpack://@justoverclock/header-slideshow/./src/forum/services/swiper-manager.ts", "webpack://@justoverclock/header-slideshow/./src/forum/components.ts", "webpack://@justoverclock/header-slideshow/./src/forum/controllers/app-controller.ts", "webpack://@justoverclock/header-slideshow/./src/forum/index.ts"], "sourcesContent": ["var regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction _regenerator() {\n  /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */\n  var e,\n    t,\n    r = \"function\" == typeof Symbol ? Symbol : {},\n    n = r.iterator || \"@@iterator\",\n    o = r.toStringTag || \"@@toStringTag\";\n  function i(r, n, o, i) {\n    var c = n && n.prototype instanceof Generator ? n : Generator,\n      u = Object.create(c.prototype);\n    return regeneratorDefine(u, \"_invoke\", function (r, n, o) {\n      var i,\n        c,\n        u,\n        f = 0,\n        p = o || [],\n        y = !1,\n        G = {\n          p: 0,\n          n: 0,\n          v: e,\n          a: d,\n          f: d.bind(e, 4),\n          d: function d(t, r) {\n            return i = t, c = 0, u = e, G.n = r, a;\n          }\n        };\n      function d(r, n) {\n        for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) {\n          var o,\n            i = p[t],\n            d = G.p,\n            l = i[2];\n          r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0));\n        }\n        if (o || r > 1) return a;\n        throw y = !0, n;\n      }\n      return function (o, p, l) {\n        if (f > 1) throw TypeError(\"Generator is already running\");\n        for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) {\n          i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u);\n          try {\n            if (f = 2, i) {\n              if (c || (o = \"next\"), t = i[o]) {\n                if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\");\n                if (!t.done) return t;\n                u = t.value, c < 2 && (c = 0);\n              } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1);\n              i = e;\n            } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break;\n          } catch (t) {\n            i = e, c = 1, u = t;\n          } finally {\n            f = 1;\n          }\n        }\n        return {\n          value: t,\n          done: y\n        };\n      };\n    }(r, o, i), !0), u;\n  }\n  var a = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  t = Object.getPrototypeOf;\n  var c = [][n] ? t(t([][n]())) : (regeneratorDefine(t = {}, n, function () {\n      return this;\n    }), t),\n    u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c);\n  function f(e) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, regeneratorDefine(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e;\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, regeneratorDefine(u, \"constructor\", GeneratorFunctionPrototype), regeneratorDefine(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", regeneratorDefine(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), regeneratorDefine(u), regeneratorDefine(u, o, \"Generator\"), regeneratorDefine(u, n, function () {\n    return this;\n  }), regeneratorDefine(u, \"toString\", function () {\n    return \"[object Generator]\";\n  }), (module.exports = _regenerator = function _regenerator() {\n    return {\n      w: i,\n      m: f\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regenerator = require(\"./regenerator.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nfunction _regeneratorAsyncGen(r, e, t, o, n) {\n  return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);\n}\nmodule.exports = _regeneratorAsyncGen, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _regeneratorKeys(e) {\n  var n = Object(e),\n    r = [];\n  for (var t in n) r.unshift(t);\n  return function e() {\n    for (; r.length;) if ((t = r.pop()) in n) return e.value = t, e.done = !1, e;\n    return e.done = !0, e;\n  };\n}\nmodule.exports = _regeneratorKeys, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "// TODO(Babel 8): Remove this file.\n\nvar runtime = require(\"../helpers/regeneratorRuntime\")();\nmodule.exports = runtime;\n\n// Copied from https://github.com/facebook/regenerator/blob/main/packages/runtime/runtime.js#L736=\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "var OverloadYield = require(\"./OverloadYield.js\");\nvar regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction AsyncIterator(t, e) {\n  function n(r, o, i, f) {\n    try {\n      var c = t[r](o),\n        u = c.value;\n      return u instanceof OverloadYield ? e.resolve(u.v).then(function (t) {\n        n(\"next\", t, i, f);\n      }, function (t) {\n        n(\"throw\", t, i, f);\n      }) : e.resolve(u).then(function (t) {\n        c.value = t, i(c);\n      }, function (t) {\n        return n(\"throw\", t, i, f);\n      });\n    } catch (t) {\n      f(t);\n    }\n  }\n  var r;\n  this.next || (regeneratorDefine(AsyncIterator.prototype), regeneratorDefine(AsyncIterator.prototype, \"function\" == typeof Symbol && Symbol.asyncIterator || \"@asyncIterator\", function () {\n    return this;\n  })), regeneratorDefine(this, \"_invoke\", function (t, o, i) {\n    function f() {\n      return new e(function (e, r) {\n        n(t, i, e, r);\n      });\n    }\n    return r = r ? r.then(f, f) : f();\n  }, !0);\n}\nmodule.exports = AsyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nfunction _regeneratorAsync(n, e, r, t, o) {\n  var a = regeneratorAsyncGen(n, e, r, t, o);\n  return a.next().then(function (n) {\n    return n.done ? n.value : a.next();\n  });\n}\nmodule.exports = _regeneratorAsync, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nvar regenerator = require(\"./regenerator.js\");\nvar regeneratorAsync = require(\"./regeneratorAsync.js\");\nvar regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nvar regeneratorKeys = require(\"./regeneratorKeys.js\");\nvar regeneratorValues = require(\"./regeneratorValues.js\");\nfunction _regeneratorRuntime() {\n  \"use strict\";\n\n  var r = regenerator(),\n    e = r.m(_regeneratorRuntime),\n    t = (Object.getPrototypeOf ? Object.getPrototypeOf(e) : e.__proto__).constructor;\n  function n(r) {\n    var e = \"function\" == typeof r && r.constructor;\n    return !!e && (e === t || \"GeneratorFunction\" === (e.displayName || e.name));\n  }\n  var o = {\n    \"throw\": 1,\n    \"return\": 2,\n    \"break\": 3,\n    \"continue\": 3\n  };\n  function a(r) {\n    var e, t;\n    return function (n) {\n      e || (e = {\n        stop: function stop() {\n          return t(n.a, 2);\n        },\n        \"catch\": function _catch() {\n          return n.v;\n        },\n        abrupt: function abrupt(r, e) {\n          return t(n.a, o[r], e);\n        },\n        delegateYield: function delegateYield(r, o, a) {\n          return e.resultName = o, t(n.d, regeneratorValues(r), a);\n        },\n        finish: function finish(r) {\n          return t(n.f, r);\n        }\n      }, t = function t(r, _t, o) {\n        n.p = e.prev, n.n = e.next;\n        try {\n          return r(_t, o);\n        } finally {\n          e.next = n.n;\n        }\n      }), e.resultName && (e[e.resultName] = n.v, e.resultName = void 0), e.sent = n.v, e.next = n.n;\n      try {\n        return r.call(this, e);\n      } finally {\n        n.p = e.prev, n.n = e.next;\n      }\n    };\n  }\n  return (module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return {\n      wrap: function wrap(e, t, n, o) {\n        return r.w(a(e), t, n, o && o.reverse());\n      },\n      isGeneratorFunction: n,\n      mark: r.m,\n      awrap: function awrap(r, e) {\n        return new OverloadYield(r, e);\n      },\n      AsyncIterator: regeneratorAsyncIterator,\n      async: function async(r, e, t, o, u) {\n        return (n(e) ? regeneratorAsyncGen : regeneratorAsync)(a(r), e, t, o, u);\n      },\n      keys: regeneratorKeys,\n      values: regeneratorValues\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _regeneratorDefine(e, r, n, t) {\n  var i = Object.defineProperty;\n  try {\n    i({}, \"\", {});\n  } catch (e) {\n    i = 0;\n  }\n  module.exports = _regeneratorDefine = function regeneratorDefine(e, r, n, t) {\n    function o(r, n) {\n      _regeneratorDefine(e, r, function (e) {\n        return this._invoke(r, n, e);\n      });\n    }\n    r ? i ? i(e, r, {\n      value: n,\n      enumerable: !t,\n      configurable: !t,\n      writable: !t\n    }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2));\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _regeneratorDefine(e, r, n, t);\n}\nmodule.exports = _regeneratorDefine, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorValues(e) {\n  if (null != e) {\n    var t = e[\"function\" == typeof Symbol && Symbol.iterator || \"@@iterator\"],\n      r = 0;\n    if (t) return t.call(e);\n    if (\"function\" == typeof e.next) return e;\n    if (!isNaN(e.length)) return {\n      next: function next() {\n        return e && r >= e.length && (e = void 0), {\n          value: e && e[r++],\n          done: !e\n        };\n      }\n    };\n  }\n  throw new TypeError(_typeof(e) + \" is not iterable\");\n}\nmodule.exports = _regeneratorValues, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nexport { _asyncToGenerator as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/HeaderPrimary'];", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "import app from 'flarum/forum/app';\nimport { TronscanData, ButtonsCustomizationData, LinksQueueData } from '../types';\n\n/**\n * Base interface for data services\n */\ninterface DataService<T> {\n    load(): Promise<T[]> | undefined;\n    isLoading(): boolean;\n    getData(): T[] | null;\n    parseResults(results: any): T[];\n}\n\n/**\n * Abstract base class for data services\n */\nabstract class BaseDataService<T> implements DataService<T> {\n    protected loading: boolean = false;\n    protected data: T[] | null = null;\n\n    public isLoading(): boolean {\n        return this.loading;\n    }\n\n    public getData(): T[] | null {\n        return this.data;\n    }\n\n    public abstract load(): Promise<T[]> | undefined;\n    public abstract parseResults(results: any): T[];\n\n    protected async fetchData(endpoint: string): Promise<T[]> {\n        if (this.loading) {\n            return Promise.resolve([]);\n        }\n\n        this.loading = true;\n        \n        try {\n            const results = await app.store.find(endpoint);\n            this.data = this.parseResults(results);\n            return this.data;\n        } catch (error) {\n            console.error(`Error loading data from ${endpoint}:`, error);\n            this.data = [];\n            return this.data;\n        } finally {\n            this.loading = false;\n        }\n    }\n}\n\n/**\n * Service for managing Tronscan data\n */\nexport class TronscanService extends BaseDataService<TronscanData> {\n    public load(): Promise<TronscanData[]> | undefined {\n        return this.fetchData(\"syncTronscanList\");\n    }\n\n    public parseResults(results: any): TronscanData[] {\n        const list: TronscanData[] = [];\n        if (results) {\n            [].push.apply(list, results);\n        }\n        return list;\n    }\n}\n\n/**\n * Service for managing buttons customization data\n */\nexport class ButtonsCustomizationService extends BaseDataService<ButtonsCustomizationData> {\n    public load(): Promise<ButtonsCustomizationData[]> | undefined {\n        return this.fetchData(\"buttonsCustomizationList\");\n    }\n\n    public parseResults(results: any): ButtonsCustomizationData[] {\n        const list: ButtonsCustomizationData[] = [];\n        if (results) {\n            [].push.apply(list, results);\n        }\n        return list;\n    }\n}\n\n/**\n * Service for managing links queue data\n */\nexport class LinksQueueService extends BaseDataService<LinksQueueData> {\n    private pointer: number = 0;\n\n    public load(): Promise<LinksQueueData[]> | undefined {\n        return this.fetchData(\"linksQueueList\");\n    }\n\n    public parseResults(results: any): LinksQueueData[] {\n        const list: LinksQueueData[] = [];\n        if (results) {\n            [].push.apply(list, results);\n        }\n        return list;\n    }\n\n    public getPointer(): number {\n        return this.pointer;\n    }\n\n    public setPointer(value: number): void {\n        this.pointer = value;\n    }\n\n    public incrementPointer(): void {\n        this.pointer++;\n    }\n\n    public decrementPointer(): void {\n        this.pointer--;\n    }\n\n    public getCurrentItem(): LinksQueueData | null {\n        if (this.data && this.data[this.pointer]) {\n            return this.data[this.pointer];\n        }\n        return null;\n    }\n\n    public hasNext(): boolean {\n        return this.data !== null && this.data[this.pointer + 1] !== undefined;\n    }\n\n    public hasPrevious(): boolean {\n        return this.data !== null && this.data[this.pointer - 1] !== undefined;\n    }\n}\n\n/**\n * Centralized data manager for all services\n */\nexport class DataManager {\n    private static instance: DataManager;\n    \n    private tronscanService: TronscanService;\n    private buttonsCustomizationService: ButtonsCustomizationService;\n    private linksQueueService: LinksQueueService;\n\n    private constructor() {\n        this.tronscanService = new TronscanService();\n        this.buttonsCustomizationService = new ButtonsCustomizationService();\n        this.linksQueueService = new LinksQueueService();\n    }\n\n    public static getInstance(): DataManager {\n        if (!DataManager.instance) {\n            DataManager.instance = new DataManager();\n        }\n        return DataManager.instance;\n    }\n\n    public getTronscanService(): TronscanService {\n        return this.tronscanService;\n    }\n\n    public getButtonsCustomizationService(): ButtonsCustomizationService {\n        return this.buttonsCustomizationService;\n    }\n\n    public getLinksQueueService(): LinksQueueService {\n        return this.linksQueueService;\n    }\n\n    /**\n     * Load all data services\n     */\n    public async loadAllData(): Promise<void> {\n        const promises = [\n            this.tronscanService.load(),\n            this.buttonsCustomizationService.load(),\n            this.linksQueueService.load()\n        ].filter(promise => promise !== undefined) as Promise<any>[];\n\n        await Promise.all(promises);\n    }\n\n    /**\n     * Check if all data is loaded\n     */\n    public isAllDataLoaded(): boolean {\n        return this.tronscanService.getData() !== null &&\n               this.buttonsCustomizationService.getData() !== null &&\n               this.linksQueueService.getData() !== null;\n    }\n\n    /**\n     * Check if any data is loading\n     */\n    public isAnyDataLoading(): boolean {\n        return this.tronscanService.isLoading() ||\n               this.buttonsCustomizationService.isLoading() ||\n               this.linksQueueService.isLoading();\n    }\n}\n", "import app from 'flarum/forum/app';\n\n/**\n * Application configuration constants\n */\nexport class AppConfig {\n    // Timing constants\n    public static readonly CHECK_TIME = 10;\n    public static readonly DEFAULT_TRANSITION_TIME = 5000;\n    public static readonly DATA_CHECK_INTERVAL = 100;\n    public static readonly ZHIBO_REFRESH_DELAY = 100;\n\n    // UI constants\n    public static readonly MOBILE_SPACE_BETWEEN = 90;\n    public static readonly DESKTOP_SPACE_BETWEEN = 10;\n    public static readonly MOBILE_SLIDES_PER_VIEW = 2;\n    public static readonly DESKTOP_SLIDES_PER_VIEW = 7;\n    public static readonly TRONSCAN_MOBILE_SLIDES = 4;\n    public static readonly TRONSCAN_DESKTOP_SLIDES = 7;\n    public static readonly TRONSCAN_MOBILE_SPACE = 80;\n\n    // Extension settings keys\n    public static readonly EXTENSION_PREFIX = 'wusong8899-client1-header-adv';\n    public static readonly TRANSLATION_PREFIX = 'wusong8899-client1';\n\n    // Button configuration\n    public static readonly DEFAULT_BUTTON_COUNT = 3;\n    public static readonly MAX_LINK_IMAGE_PAIRS = 30;\n\n    // CSS selectors\n    public static readonly SELECTORS = {\n        APP: '#app',\n        APP_NAVIGATION: '#app-navigation',\n        APP_CONTENT: '.App-content',\n        CONTENT_CONTAINER: '#content .container',\n        TAGS_PAGE_CONTENT: '.TagsPage-content',\n        TAG_TILES: '.TagTiles',\n        TAG_TILE: '.TagTile',\n        ITEM_NEW_DISCUSSION: '.item-newDiscussion',\n        ITEM_NAV: '.item-nav',\n        ITEM_MONEY_LEADERBOARD: '.item-MoneyLeaderboard',\n        ITEM_FORUM_CHECKIN: '.item-forum-checkin',\n        APP_BACK_CONTROL: '.App-backControl'\n    } as const;\n\n    // Container IDs\n    public static readonly CONTAINER_IDS = {\n        SWIPER_TAG_CONTAINER: 'swiperTagContainer',\n        SWIPER_AD_CONTAINER: 'swiperAdContainer',\n        TRONSCAN_TEXT_CONTAINER: 'TronscanTextContainer',\n        SELECT_TITLE_CONTAINER: 'selectTitleContainer',\n        HEADER_ICON: 'wusong8899Client1HeaderIcon',\n        ZHIBO_IFRAME: 'zhiboIframe',\n        CUSTOM_BUTTON_IFRAME: 'customButtonIframe',\n        BUTTON_SELECTED_BACKGROUND: 'buttonSelectedBackground',\n        LINKS_QUEUE_REFRESH: 'linksQueueRefresh',\n        LINKS_QUEUE_PREV: 'linksQueuePrev',\n        LINKS_QUEUE_NEXT: 'linksQueueNext'\n    } as const;\n\n    // CSS classes\n    public static readonly CSS_CLASSES = {\n        SWIPER_TAG_CONTAINER: 'swiperTagContainer',\n        SWIPER_AD_CONTAINER: 'swiperAdContainer',\n        TAG_SWIPER: 'swiper tagSwiper',\n        AD_SWIPER: 'swiper adSwiper',\n        TRONSCAN_SWIPER: 'swiper tronscanSwiper',\n        ZHIBO_CONTAINER: 'zhiboContainer',\n        YOUXI_CONTAINER: 'youxiContainer',\n        SHANGCHENG_CONTAINER: 'shangchengContainer',\n        BUTTON_CUSTOMIZATION_CONTAINER: 'buttonCustomizationContainer',\n        SELECT_TITLE_CONTAINER: 'selectTitleContainer',\n        U_BTN: 'u-btn',\n        SWIPER_SLIDE_TAG: 'swiper-slide swiper-slide-tag',\n        SWIPER_SLIDE_TAG_INNER: 'swiper-slide-tag-inner',\n        SWIPER_SLIDE_TAG_INNER_MOBILE: 'swiper-slide-tag-inner-mobile'\n    } as const;\n}\n\n/**\n * Settings configuration manager\n */\nexport class SettingsConfig {\n    /**\n     * Get transition time from forum settings\n     */\n    public static getTransitionTime(): number {\n        const transitionTime = app.forum.attribute('Client1HeaderAdvTransitionTime');\n        return transitionTime ? Number(transitionTime) : AppConfig.DEFAULT_TRANSITION_TIME;\n    }\n\n    /**\n     * Get image source for a specific index\n     */\n    public static getImageSrc(index: number): string | null {\n        return app.forum.attribute(`Client1HeaderAdvImage${index}`) || null;\n    }\n\n    /**\n     * Get link for a specific index\n     */\n    public static getImageLink(index: number): string | null {\n        return app.forum.attribute(`Client1HeaderAdvLink${index}`) || null;\n    }\n\n    /**\n     * Get all configured image/link pairs\n     */\n    public static getImageLinkPairs(): Array<{ src: string; link: string; index: number }> {\n        const pairs: Array<{ src: string; link: string; index: number }> = [];\n        \n        for (let i = 1; i <= AppConfig.MAX_LINK_IMAGE_PAIRS; i++) {\n            const src = this.getImageSrc(i);\n            const link = this.getImageLink(i);\n            \n            if (src) {\n                pairs.push({\n                    src,\n                    link: link || '#',\n                    index: i\n                });\n            }\n        }\n        \n        return pairs;\n    }\n}\n\n/**\n * Device detection and responsive configuration\n */\nexport class DeviceConfig {\n    private static _isMobile: boolean | null = null;\n\n    /**\n     * Check if the current device is mobile\n     */\n    public static isMobile(): boolean {\n        if (this._isMobile === null) {\n            this._isMobile = this.mobileCheck();\n        }\n        return this._isMobile;\n    }\n\n    /**\n     * Mobile device detection\n     */\n    private static mobileCheck(): boolean {\n        let check = false;\n        const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;\n        \n        if (userAgent) {\n            const mobileRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;\n            const mobileRegex2 = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i;\n            \n            check = mobileRegex.test(userAgent) || mobileRegex2.test(userAgent.substr(0, 4));\n        }\n        \n        return check;\n    }\n\n    /**\n     * Get responsive configuration based on device type\n     */\n    public static getResponsiveConfig() {\n        const isMobile = this.isMobile();\n        \n        return {\n            isMobile,\n            spaceBetween: isMobile ? AppConfig.MOBILE_SPACE_BETWEEN : AppConfig.DESKTOP_SPACE_BETWEEN,\n            slidesPerView: isMobile ? AppConfig.MOBILE_SLIDES_PER_VIEW : AppConfig.DESKTOP_SLIDES_PER_VIEW,\n            tronscanSlidesPerView: isMobile ? AppConfig.TRONSCAN_MOBILE_SLIDES : AppConfig.TRONSCAN_DESKTOP_SLIDES,\n            tronscanSpaceBetween: isMobile ? AppConfig.TRONSCAN_MOBILE_SPACE : AppConfig.DESKTOP_SPACE_BETWEEN,\n            eventType: isMobile ? 'touchend' : 'click',\n            leftModifier: isMobile ? 3 : 0\n        };\n    }\n}\n", "import { ButtonsCustomizationMap, LeftValueMap } from '../types';\nimport { AppConfig, DeviceConfig } from '../config/app-config';\nimport { DataManager } from './data-service';\n\n/**\n * Event handler interface\n */\ninterface EventHandler {\n    selector: string;\n    event: string;\n    handler: (event: Event) => void;\n}\n\n/**\n * Event manager for centralized event handling\n */\nexport class EventManager {\n    private static instance: EventManager;\n    private handlers: Map<string, EventHandler[]> = new Map();\n    private dataManager: DataManager;\n\n    private constructor() {\n        this.dataManager = DataManager.getInstance();\n    }\n\n    public static getInstance(): EventManager {\n        if (!EventManager.instance) {\n            EventManager.instance = new EventManager();\n        }\n        return EventManager.instance;\n    }\n\n    /**\n     * Register an event handler\n     */\n    public on(id: string, selector: string, event: string, handler: (event: Event) => void): void {\n        if (!this.handlers.has(id)) {\n            this.handlers.set(id, []);\n        }\n\n        const eventHandler: EventHandler = { selector, event, handler };\n        this.handlers.get(id)!.push(eventHandler);\n\n        // Attach the event listener\n        $(document).on(event, selector, handler);\n    }\n\n    /**\n     * Remove event handlers by ID\n     */\n    public off(id: string): void {\n        const handlers = this.handlers.get(id);\n        if (handlers) {\n            handlers.forEach(({ selector, event, handler }) => {\n                $(document).off(event, selector, handler);\n            });\n            this.handlers.delete(id);\n        }\n    }\n\n    /**\n     * Remove all event handlers\n     */\n    public offAll(): void {\n        this.handlers.forEach((handlers, id) => {\n            this.off(id);\n        });\n    }\n\n    /**\n     * Setup button navigation events\n     */\n    public setupButtonNavigation(\n        buttonsCustomizationMap: ButtonsCustomizationMap,\n        leftValueMap: LeftValueMap,\n        totalButtons: number\n    ): void {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        const linksQueueService = this.dataManager.getLinksQueueService();\n\n        this.on('buttonNavigation', '.u-btn', responsive.eventType, (event) => {\n            const target = event.currentTarget as HTMLElement;\n            const numberStr = $(target).attr('number');\n            const number = numberStr ? parseInt(numberStr) : 0;\n            const zhiboIframe = document.getElementById(AppConfig.CONTAINER_IDS.ZHIBO_IFRAME) as HTMLIFrameElement | null;\n\n            $(\".App\").css(\"min-height\", \"100vh\");\n\n            this.handleButtonClick(number, buttonsCustomizationMap, zhiboIframe, linksQueueService);\n            this.updateButtonSelection(target, leftValueMap, number);\n        });\n    }\n\n    /**\n     * Handle button click logic\n     */\n    private handleButtonClick(\n        number: number,\n        buttonsCustomizationMap: ButtonsCustomizationMap,\n        zhiboIframe: HTMLIFrameElement | null,\n        linksQueueService: any\n    ): void {\n        // Hide all containers first\n        this.hideAllContainers();\n\n        switch (number) {\n            case 0: // Forum\n                this.showForumView(zhiboIframe);\n                break;\n            case 1: // Zhibo\n                this.showZhiboView(zhiboIframe, linksQueueService);\n                break;\n            case 2: // Youxi\n                this.showYouxiView(zhiboIframe);\n                break;\n            case 3: // Shangcheng\n                this.showShangchengView(zhiboIframe);\n                break;\n            default: // Custom buttons\n                this.showCustomButtonView(number, buttonsCustomizationMap);\n                break;\n        }\n    }\n\n    /**\n     * Hide all containers\n     */\n    private hideAllContainers(): void {\n        $(`.${AppConfig.CSS_CLASSES.SWIPER_TAG_CONTAINER}`).css(\"display\", \"none\");\n        $(`.${AppConfig.CSS_CLASSES.ZHIBO_CONTAINER}`).css(\"display\", \"none\");\n        $(`.${AppConfig.CSS_CLASSES.YOUXI_CONTAINER}`).css(\"display\", \"none\");\n        $(`.${AppConfig.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER}`).css(\"display\", \"none\");\n        $(`.${AppConfig.CSS_CLASSES.SHANGCHENG_CONTAINER}`).css(\"display\", \"none\");\n    }\n\n    /**\n     * Show forum view\n     */\n    private showForumView(zhiboIframe: HTMLIFrameElement | null): void {\n        $(`.${AppConfig.CSS_CLASSES.SWIPER_TAG_CONTAINER}`).css(\"display\", \"\");\n        $(\".App\").css(\"min-height\", \"50vh\");\n        if (zhiboIframe) {\n            zhiboIframe.src = \"\";\n        }\n    }\n\n    /**\n     * Show zhibo view\n     */\n    private showZhiboView(zhiboIframe: HTMLIFrameElement | null, linksQueueService: any): void {\n        $(`.${AppConfig.CSS_CLASSES.ZHIBO_CONTAINER}`).css(\"display\", \"inline-block\");\n\n        const appNavHeight = $(\"#app-navigation\").outerHeight() || 0;\n        const selectTitleHeight = $(\".selectTitleContainer\").outerHeight() || 0;\n        const linksQueueHeight = $(\"#linksQueuePrev\").outerHeight() || 0;\n        const iframeHeight = window.innerHeight - appNavHeight - selectTitleHeight - linksQueueHeight;\n\n        if (zhiboIframe) {\n            $(\"#zhiboIframe\").css(\"height\", iframeHeight + \"px\");\n            const currentItem = linksQueueService.getCurrentItem();\n            if (currentItem) {\n                const linksQueueURL = currentItem.attribute(\"links\");\n                if (zhiboIframe.src !== linksQueueURL) {\n                    zhiboIframe.src = linksQueueURL;\n                }\n            }\n        }\n    }\n\n    /**\n     * Show youxi view\n     */\n    private showYouxiView(zhiboIframe: HTMLIFrameElement | null): void {\n        $(`.${AppConfig.CSS_CLASSES.YOUXI_CONTAINER}`).css(\"display\", \"flex\");\n        if (zhiboIframe) {\n            zhiboIframe.src = \"\";\n        }\n    }\n\n    /**\n     * Show shangcheng view\n     */\n    private showShangchengView(zhiboIframe: HTMLIFrameElement | null): void {\n        $(`.${AppConfig.CSS_CLASSES.SHANGCHENG_CONTAINER}`).css(\"display\", \"flex\");\n        if (zhiboIframe) {\n            zhiboIframe.src = \"\";\n        }\n    }\n\n    /**\n     * Show custom button view\n     */\n    private showCustomButtonView(number: number, buttonsCustomizationMap: ButtonsCustomizationMap): void {\n        const customButtonData = buttonsCustomizationMap[number];\n\n        if (customButtonData) {\n            const appNavHeight = $(\"#app-navigation\").outerHeight() || 0;\n            const selectTitleHeight = $(\".selectTitleContainer\").outerHeight() || 0;\n            const linksQueueHeight = $(\"#linksQueuePrev\").outerHeight() || 0;\n            let iframeHeight = window.innerHeight - appNavHeight - selectTitleHeight - linksQueueHeight;\n            let paddingBottom = 0;\n            let scrolling: \"yes\" | \"no\" = \"yes\";\n            let containerHeight: string | number = $(\".swiperTagContainer\").css(\"height\") || '0px';\n\n            // Custom height configurations for specific buttons\n            if (number == 5) {\n                iframeHeight = 550;\n                containerHeight = 550;\n            } else if (number == 6) {\n                iframeHeight = containerHeight = 380;\n                scrolling = \"no\";\n            } else if (number == 7 || number == 8) {\n                paddingBottom = 20;\n            }\n\n            $(`.${AppConfig.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER}`).css({\n                \"padding-bottom\": paddingBottom + \"px\",\n                \"height\": containerHeight + \"px\",\n                \"display\": \"inline-block\"\n            });\n\n            $(`#${AppConfig.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME}`).css({\n                \"padding-bottom\": paddingBottom + \"px\",\n                \"height\": iframeHeight + \"px\"\n            }).attr(\"scrolling\", scrolling);\n\n            const customButtonIframe = document.getElementById(AppConfig.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME) as HTMLIFrameElement | null;\n            if (customButtonIframe) {\n                customButtonIframe.src = customButtonData.url;\n            }\n        }\n    }\n\n    /**\n     * Update button selection visual feedback\n     */\n    private updateButtonSelection(target: HTMLElement, leftValueMap: LeftValueMap, number: number): void {\n        const selectedButtonWidth = $(target).outerWidth();\n        const buttonSelectedBackground = $(`#${AppConfig.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND}`);\n\n        if (buttonSelectedBackground.length && selectedButtonWidth) {\n            buttonSelectedBackground.width(selectedButtonWidth);\n        }\n\n        if (leftValueMap[number] !== undefined) {\n            buttonSelectedBackground.css(\"left\", leftValueMap[number]);\n        }\n    }\n\n    /**\n     * Setup zhibo navigation events\n     */\n    public setupZhiboNavigation(): void {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        const linksQueueService = this.dataManager.getLinksQueueService();\n        const zhiboIframe = document.getElementById(AppConfig.CONTAINER_IDS.ZHIBO_IFRAME) as HTMLIFrameElement | null;\n\n        // Refresh button\n        this.on('zhiboRefresh', `#${AppConfig.CONTAINER_IDS.LINKS_QUEUE_REFRESH}`, responsive.eventType, () => {\n            if (zhiboIframe) {\n                zhiboIframe.src = \"\";\n                setTimeout(() => {\n                    const currentItem = linksQueueService.getCurrentItem();\n                    if (currentItem && zhiboIframe) {\n                        zhiboIframe.src = currentItem.attribute(\"links\");\n                    }\n                }, AppConfig.ZHIBO_REFRESH_DELAY);\n            }\n        });\n\n        // Previous button\n        this.on('zhiboPrev', `#${AppConfig.CONTAINER_IDS.LINKS_QUEUE_PREV}`, responsive.eventType, () => {\n            linksQueueService.decrementPointer();\n            const currentItem = linksQueueService.getCurrentItem();\n            \n            if (currentItem && zhiboIframe) {\n                zhiboIframe.src = currentItem.attribute(\"links\");\n            }\n\n            this.updateZhiboButtonStates(linksQueueService);\n        });\n\n        // Next button\n        this.on('zhiboNext', `#${AppConfig.CONTAINER_IDS.LINKS_QUEUE_NEXT}`, responsive.eventType, () => {\n            linksQueueService.incrementPointer();\n            const currentItem = linksQueueService.getCurrentItem();\n            \n            if (currentItem && zhiboIframe) {\n                zhiboIframe.src = currentItem.attribute(\"links\");\n            }\n\n            this.updateZhiboButtonStates(linksQueueService);\n        });\n    }\n\n    /**\n     * Update zhibo button states based on navigation\n     */\n    private updateZhiboButtonStates(linksQueueService: any): void {\n        $(\"#nextZhiBoButton\").css(\"color\", linksQueueService.hasNext() ? \"\" : \"#666\");\n        $(\"#prevZhiBoButton\").css(\"color\", linksQueueService.hasPrevious() ? \"\" : \"#666\");\n    }\n\n    /**\n     * Setup mobile-specific UI adjustments\n     */\n    public setupMobileAdjustments(): void {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        \n        if (responsive.isMobile) {\n            $(\".item-newDiscussion\").find(\"span.Button-label\").html(\"<div class='buttonRegister'>登录</div>\");\n            $(\".item-newDiscussion\").find(\"span.Button-label\").css({\n                \"display\": \"block\",\n                \"font-size\": \"14px\",\n                \"word-spacing\": \"-1px\"\n            });\n        }\n\n        $(\".item-newDiscussion\").find(\"i\").css(\"display\", \"none\");\n        $(\".item-nav\").remove();\n        $(\".TagTiles\").css(\"display\", \"none\");\n    }\n\n    /**\n     * Setup leaderboard positioning\n     */\n    public setupLeaderboardPosition(): void {\n        $(\".item-MoneyLeaderboard\").addClass(\"App-primaryControl\");\n        $(\".item-forum-checkin\").parent().append($(\".item-MoneyLeaderboard\"));\n        $(\".item-MoneyLeaderboard\").css(\"right\", \"75px\");\n    }\n}\n", "/**\n * SSR Window 5.0.1\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: June 27, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n", "import { e as elementChildren, c as createElement } from './utils.mjs';\n\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n\nexport { createElementIfNotDefined as c };\n", "import { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray } from '../shared/utils.mjs';\n\nfunction Navigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null\n  };\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.querySelector(el) || swiper.hostEl.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && res && res.length > 1 && swiper.el.querySelectorAll(el).length === 1) {\n        res = swiper.el.querySelector(el);\n      } else if (res && res.length === 1) {\n        res = res[0];\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n    nextEl.forEach(el => initButton(el, 'next'));\n    prevEl.forEach(el => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach(el => destroyButton(el, 'next'));\n    prevEl.forEach(el => destroyButton(el, 'prev'));\n  }\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (swiper.enabled) {\n      update();\n      return;\n    }\n    [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.add(swiper.params.navigation.lockClass));\n  });\n  on('click', (_s, e) => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    let targetIsButton = prevEl.includes(targetEl) || nextEl.includes(targetEl);\n    if (swiper.isElement && !targetIsButton) {\n      const path = e.path || e.composedPath && e.composedPath();\n      if (path) {\n        targetIsButton = path.find(pathEl => nextEl.includes(pathEl) || prevEl.includes(pathEl));\n      }\n    }\n    if (swiper.params.navigation.hideOnClick && !targetIsButton) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Navigation as default };\n", "function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return `.${classes.trim().replace(/([\\.:!+\\/()[\\]])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}\n\nexport { classesToSelector as c };\n", "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n", "import { g as getSlideTransformEl } from './utils.mjs';\n\nfunction effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}\n\nexport { effectTarget as e };\n", "import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\n\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n\nexport { createShadow as c };\n", "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectCoverflow(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true\n    }\n  });\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    const r = getRotateFix(swiper);\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${r(rotateX)}deg) rotateY(${r(rotateY)}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow('coverflow', slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow('coverflow', slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl) shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl) shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}\n\nexport { EffectCoverflow as default };\n", "function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate _virtualUpdated', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach(slideEl => {\n        slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}\n\nexport { effectInit as e };\n", "import Swiper from 'swiper';\nimport { EffectCoverflow, Navigation, Pagination, Autoplay } from 'swiper/modules';\nimport { SwiperConfig } from '../types';\nimport { AppConfig, DeviceConfig, SettingsConfig } from '../config/app-config';\n\n/**\n * Swiper configuration factory\n */\nexport class SwiperConfigFactory {\n    /**\n     * Create configuration for tag swiper\n     */\n    public static createTagSwiperConfig(): SwiperConfig {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        \n        return {\n            loop: true,\n            spaceBetween: responsive.spaceBetween,\n            slidesPerView: responsive.slidesPerView,\n            autoplay: {\n                delay: 3000,\n                disableOnInteraction: false,\n            },\n            modules: [Autoplay]\n        };\n    }\n\n    /**\n     * Create configuration for Tronscan swiper\n     */\n    public static createTronscanSwiperConfig(): SwiperConfig {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        \n        return {\n            loop: true,\n            spaceBetween: responsive.tronscanSpaceBetween,\n            slidesPerView: responsive.tronscanSlidesPerView,\n            modules: []\n        };\n    }\n\n    /**\n     * Create configuration for advertisement swiper\n     */\n    public static createAdSwiperConfig(): SwiperConfig {\n        const transitionTime = SettingsConfig.getTransitionTime();\n        \n        return {\n            autoplay: {\n                delay: transitionTime,\n                disableOnInteraction: false,\n            },\n            loop: true,\n            spaceBetween: 30,\n            effect: \"coverflow\",\n            centeredSlides: true,\n            slidesPerView: 2,\n            coverflowEffect: {\n                rotate: 0,\n                depth: 100,\n                modifier: 1,\n                slideShadows: true,\n                stretch: 0\n            },\n            pagination: {\n                el: '.swiper-pagination',\n                type: 'bullets',\n            },\n            navigation: {\n                nextEl: '.swiper-button-next',\n                prevEl: '.swiper-button-prev',\n            },\n            modules: [EffectCoverflow, Navigation, Pagination, Autoplay]\n        };\n    }\n}\n\n/**\n * Swiper instance manager\n */\nexport class SwiperManager {\n    private static instances: Map<string, Swiper> = new Map();\n\n    /**\n     * Create and register a new Swiper instance\n     */\n    public static create(selector: string, config: SwiperConfig, id?: string): Swiper {\n        const swiper = new Swiper(selector, config);\n        const instanceId = id || selector;\n        \n        this.instances.set(instanceId, swiper);\n        return swiper;\n    }\n\n    /**\n     * Get a Swiper instance by ID\n     */\n    public static getInstance(id: string): Swiper | undefined {\n        return this.instances.get(id);\n    }\n\n    /**\n     * Destroy a Swiper instance\n     */\n    public static destroy(id: string): void {\n        const swiper = this.instances.get(id);\n        if (swiper) {\n            swiper.destroy();\n            this.instances.delete(id);\n        }\n    }\n\n    /**\n     * Destroy all Swiper instances\n     */\n    public static destroyAll(): void {\n        this.instances.forEach((swiper, id) => {\n            swiper.destroy();\n        });\n        this.instances.clear();\n    }\n\n    /**\n     * Create tag swiper\n     */\n    public static createTagSwiper(): Swiper {\n        const config = SwiperConfigFactory.createTagSwiperConfig();\n        return this.create('.tagSwiper', config, 'tagSwiper');\n    }\n\n    /**\n     * Create Tronscan swiper\n     */\n    public static createTronscanSwiper(): Swiper {\n        const config = SwiperConfigFactory.createTronscanSwiperConfig();\n        return this.create('.tronscanSwiper', config, 'tronscanSwiper');\n    }\n\n    /**\n     * Create advertisement swiper\n     */\n    public static createAdSwiper(): Swiper {\n        const config = SwiperConfigFactory.createAdSwiperConfig();\n        return this.create('.adSwiper', config, 'adSwiper');\n    }\n\n    /**\n     * Update swiper when data changes\n     */\n    public static updateSwiper(id: string): void {\n        const swiper = this.getInstance(id);\n        if (swiper) {\n            swiper.update();\n        }\n    }\n\n    /**\n     * Slide to specific index\n     */\n    public static slideTo(id: string, index: number, speed?: number): void {\n        const swiper = this.getInstance(id);\n        if (swiper) {\n            swiper.slideTo(index, speed);\n        }\n    }\n\n    /**\n     * Start autoplay\n     */\n    public static startAutoplay(id: string): void {\n        const swiper = this.getInstance(id);\n        if (swiper && swiper.autoplay) {\n            swiper.autoplay.start();\n        }\n    }\n\n    /**\n     * Stop autoplay\n     */\n    public static stopAutoplay(id: string): void {\n        const swiper = this.getInstance(id);\n        if (swiper && swiper.autoplay) {\n            swiper.autoplay.stop();\n        }\n    }\n\n    /**\n     * Get all active swiper instances\n     */\n    public static getAllInstances(): Map<string, Swiper> {\n        return new Map(this.instances);\n    }\n\n    /**\n     * Check if a swiper instance exists\n     */\n    public static hasInstance(id: string): boolean {\n        return this.instances.has(id);\n    }\n}\n\n/**\n * Swiper utilities for common operations\n */\nexport class SwiperUtils {\n    /**\n     * Wait for DOM element to be available\n     */\n    public static waitForElement(selector: string, timeout: number = 5000): Promise<Element> {\n        return new Promise((resolve, reject) => {\n            const element = document.querySelector(selector);\n            if (element) {\n                resolve(element);\n                return;\n            }\n\n            const observer = new MutationObserver((mutations, obs) => {\n                const element = document.querySelector(selector);\n                if (element) {\n                    obs.disconnect();\n                    resolve(element);\n                }\n            });\n\n            observer.observe(document.body, {\n                childList: true,\n                subtree: true\n            });\n\n            setTimeout(() => {\n                observer.disconnect();\n                reject(new Error(`Element ${selector} not found within ${timeout}ms`));\n            }, timeout);\n        });\n    }\n\n    /**\n     * Create swiper with retry mechanism\n     */\n    public static async createWithRetry(\n        selector: string, \n        config: SwiperConfig, \n        maxRetries: number = 3,\n        retryDelay: number = 100\n    ): Promise<Swiper> {\n        for (let i = 0; i < maxRetries; i++) {\n            try {\n                await this.waitForElement(selector, 1000);\n                return SwiperManager.create(selector, config);\n            } catch (error) {\n                if (i === maxRetries - 1) {\n                    throw error;\n                }\n                await new Promise(resolve => setTimeout(resolve, retryDelay));\n            }\n        }\n        throw new Error(`Failed to create swiper after ${maxRetries} retries`);\n    }\n\n    /**\n     * Safely destroy swiper\n     */\n    public static safeDestroy(id: string): void {\n        try {\n            SwiperManager.destroy(id);\n        } catch (error) {\n            console.warn(`Error destroying swiper ${id}:`, error);\n        }\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { ContainerConfig, TronscanData, ButtonsCustomizationData } from './types';\nimport { AppConfig, DeviceConfig, SettingsConfig } from './config/app-config';\n\n/**\n * Base component factory for creating DOM elements\n */\nexport class ComponentFactory {\n    static createContainer(config: ContainerConfig): HTMLDivElement {\n        const container = document.createElement(\"div\");\n        container.className = config.className;\n\n        if (config.id) {\n            container.id = config.id;\n        }\n\n        if (config.height) {\n            container.style.height = typeof config.height === 'number'\n                ? `${config.height}px`\n                : config.height;\n        }\n\n        return container;\n    }\n\n    static createSwiperContainer(className: string): HTMLDivElement {\n        const swiper = document.createElement(\"div\");\n        swiper.className = className;\n        return swiper;\n    }\n\n    static createSwiperWrapper(id?: string): HTMLDivElement {\n        const wrapper = document.createElement(\"div\");\n        wrapper.className = \"swiper-wrapper\";\n        if (id) {\n            wrapper.id = id;\n        }\n        return wrapper;\n    }\n\n    static createSwiperSlide(className: string, innerHTML: string): HTMLDivElement {\n        const slide = document.createElement(\"div\");\n        slide.className = className;\n        slide.innerHTML = innerHTML;\n        return slide;\n    }\n\n    static createSwiperNavigation(className: string): HTMLDivElement {\n        const navigation = document.createElement(\"div\");\n        navigation.className = className;\n        return navigation;\n    }\n\n    static createButton(id: string, number: string, iconClass: string, text: string): string {\n        return `<button id=\"${id}\" number=\"${number}\" type=\"button\" class=\"u-btn\">\n                    <i class=\"${iconClass}\"></i>\n                    <div class=\"u-btn-text\">${text}</div>\n                </button>`;\n    }\n\n    static createIframe(id: string, className: string, src: string = ''): HTMLIFrameElement {\n        const iframe = document.createElement(\"iframe\");\n        iframe.id = id;\n        iframe.name = \"contentOnly\";\n        iframe.className = className;\n        iframe.src = src;\n        return iframe;\n    }\n}\n\n/**\n * Advertisement swiper component\n */\nexport class AdSwiperComponent {\n    static create(): HTMLDivElement {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        const screenWidth = $(window).width() || 0;\n        let styleWidth = screenWidth * 2 - 50;\n\n        const swiperContainer = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.SWIPER_AD_CONTAINER,\n            id: AppConfig.CONTAINER_IDS.SWIPER_AD_CONTAINER\n        });\n\n        if (responsive.isMobile) {\n            swiperContainer.style.width = styleWidth + \"px\";\n            swiperContainer.style.marginLeft = -(styleWidth * 0.254) + \"px\";\n        }\n\n        const swiper = ComponentFactory.createSwiperContainer(AppConfig.CSS_CLASSES.AD_SWIPER);\n        const wrapper = ComponentFactory.createSwiperWrapper();\n\n        // Add slides from configuration\n        const imagePairs = SettingsConfig.getImageLinkPairs();\n        imagePairs.forEach(pair => {\n            const slide = ComponentFactory.createSwiperSlide(\n                \"swiper-slide\",\n                `<img onclick='window.location.href=\"${pair.link}\"' src='${pair.src}' />`\n            );\n            wrapper.appendChild(slide);\n        });\n\n        // Add navigation\n        const nextButton = ComponentFactory.createSwiperNavigation(\"swiper-button-next\");\n        const prevButton = ComponentFactory.createSwiperNavigation(\"swiper-button-prev\");\n        const pagination = ComponentFactory.createSwiperNavigation(\"swiper-pagination\");\n\n        swiper.appendChild(wrapper);\n        swiper.appendChild(nextButton);\n        swiper.appendChild(prevButton);\n        swiper.appendChild(pagination);\n        swiperContainer.appendChild(swiper);\n\n        return swiperContainer;\n    }\n}\n\n/**\n * Tag swiper component\n */\nexport class TagSwiperComponent {\n    static create(): HTMLDivElement {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        const tagTile = $(\".TagTile\");\n\n        const swiperContainer = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.SWIPER_TAG_CONTAINER,\n            id: AppConfig.CONTAINER_IDS.SWIPER_TAG_CONTAINER\n        });\n\n        const swiper = ComponentFactory.createSwiperContainer(AppConfig.CSS_CLASSES.TAG_SWIPER);\n        const tagTextOuterContainer = ComponentFactory.createContainer({\n            className: \"TagTextOuterContainer\"\n        });\n\n        swiperContainer.appendChild(tagTextOuterContainer);\n        tagTextOuterContainer.appendChild(swiper);\n\n        const wrapper = ComponentFactory.createSwiperWrapper(AppConfig.CONTAINER_IDS.SWIPER_TAG_CONTAINER.replace('Container', 'Wrapper'));\n        swiper.appendChild(wrapper);\n\n        // Create slides from existing tag tiles\n        for (let i = 0; i < tagTile.length; i++) {\n            const tag = tagTile[i];\n            const tagURL = $(tag).find(\"a\").attr(\"href\") || '';\n            const tagBackground = $(tag).css(\"background\") || '';\n            const tagName = $(tag).find(\".TagTile-name\").text() || '';\n            const tagNameColor = $(tag).find(\".TagTile-name\").css(\"color\") || '';\n\n            const slideContent = `<a href='${tagURL}'>\n                <div class='${responsive.isMobile ? AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG_INNER_MOBILE : AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG_INNER}'\n                     style='background:${tagBackground};background-size: cover;background-position: center;background-repeat: no-repeat;'>\n                    <div style='font-weight:bold;font-size:14px;color:${tagNameColor}'>${tagName}</div>\n                </div>\n            </a>`;\n\n            const slide = ComponentFactory.createSwiperSlide(AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG, slideContent);\n            wrapper.appendChild(slide);\n        }\n\n        return swiperContainer;\n    }\n\n    static addTextContent(container: HTMLDivElement): void {\n        const tagTextContainer = container.querySelector('.TagTextOuterContainer');\n        if (tagTextContainer) {\n            $(tagTextContainer).prepend(\"<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>\");\n            $(tagTextContainer).append(`\n                <div style=\"text-align:center;padding-top: 10px;\">\n                    <button class=\"Button Button--primary\" type=\"button\" style=\"font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;\">\n                        <div style=\"margin-top: 5px;\" class=\"Button-label\">\n                            <img onClick=\"window.open('https://kick.com/wangming886', '_blank')\" style=\"width: 32px;\" src=\"https://mutluresim.com/images/2023/04/10/KcgSG.png\">\n                            <img onClick=\"window.open('https://m.facebook.com', '_blank')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcF6i.png\">\n                            <img onClick=\"window.open('https://twitter.com/youngron131_', '_blank')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcDas.png\">\n                            <img onClick=\"window.open('https://m.youtube.com/@ag8888','_blank')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcQjd.png\">\n                            <img onClick=\"window.open('https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=', '_blank')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcBAL.png\">\n                        </div>\n                    </button>\n                </div>\n            `);\n        }\n    }\n}\n\n/**\n * Tronscan swiper component\n */\nexport class TronscanSwiperComponent {\n    static create(tronscanData: TronscanData[]): HTMLDivElement {\n        const textContainer = ComponentFactory.createContainer({\n            className: \"TronscanTextContainer\",\n            id: AppConfig.CONTAINER_IDS.TRONSCAN_TEXT_CONTAINER\n        });\n        textContainer.innerHTML = \"<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度\";\n\n        const swiper = ComponentFactory.createSwiperContainer(AppConfig.CSS_CLASSES.TRONSCAN_SWIPER);\n        const wrapper = ComponentFactory.createSwiperWrapper();\n\n        tronscanData.forEach(data => {\n            const tronscanValueUsd = parseInt(data.valueUsd()) + \" USD\";\n            const tronscanBackground = \"url(\" + data.img() + \");\";\n\n            const slideContent = `\n                <div style='width:100px;height:130px;border-radius: 12px;background: ${tronscanBackground};background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'>\n                    <div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div>\n                    <div class='tronscanMask'>\n                        <div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'>\n                            <span>${tronscanValueUsd}</span>\n                        </div>\n                    </div>\n                </div>\n            `;\n\n            const slide = ComponentFactory.createSwiperSlide(AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG, slideContent);\n            wrapper.appendChild(slide);\n        });\n\n        swiper.appendChild(wrapper);\n\n        const container = ComponentFactory.createContainer({\n            className: \"tronscanSwiperContainer\"\n        });\n        container.appendChild(textContainer);\n        container.appendChild(swiper);\n\n        return container;\n    }\n}\n\n/**\n * Button navigation component\n */\nexport class ButtonNavigationComponent {\n    static create(buttonsCustomizationData: ButtonsCustomizationData[]): HTMLDivElement {\n        const container = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.SELECT_TITLE_CONTAINER,\n            id: AppConfig.CONTAINER_IDS.SELECT_TITLE_CONTAINER\n        });\n\n        let buttonsCustomization = \"\";\n        let totalButtons = AppConfig.DEFAULT_BUTTON_COUNT;\n\n        buttonsCustomizationData.forEach(data => {\n            totalButtons++;\n            buttonsCustomization += ComponentFactory.createButton(\n                `client1HeaderButton${totalButtons}`,\n                totalButtons.toString(),\n                data.icon(),\n                data.name()\n            );\n        });\n\n        const selectTitle = ComponentFactory.createContainer({\n            className: \"selectTitle\"\n        });\n\n        const tagsPageWidth = $(\".TagsPage-content\").width() || 0;\n        const defaultButtons = `\n            ${ComponentFactory.createButton(\"client1HeaderButton0\", \"0\", \"fas fa-paw\", \"论坛\")}\n            ${ComponentFactory.createButton(\"client1HeaderButton1\", \"1\", \"fab fa-twitch\", \"直播\")}\n            ${ComponentFactory.createButton(\"client1HeaderButton2\", \"2\", \"fas fa-dice\", \"游戏\")}\n            ${ComponentFactory.createButton(\"client1HeaderButton3\", \"3\", \"fas fa-gifts\", \"商城\")}\n        `;\n\n        selectTitle.innerHTML = `\n            <div class=\"switch-btns\" style=\"max-width:${tagsPageWidth}px\">\n                <div class=\"btns-container\">\n                    ${defaultButtons}\n                    ${buttonsCustomization}\n                    <div id=\"${AppConfig.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND}\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div>\n                </div>\n            </div>\n        `;\n\n        container.appendChild(selectTitle);\n        return container;\n    }\n}\n\n/**\n * Container components for different sections\n */\nexport class ContainerComponents {\n    static createZhiboContainer(): HTMLDivElement {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        const container = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.ZHIBO_CONTAINER,\n            height: $(\".swiperTagContainer\").css(\"height\") || ''\n        });\n\n        const refreshButton = `\n            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>\n                <div class='switch-btns'>\n                    <div class='btns-container'>\n                        <button type='button' class='u-btn'>\n                            <div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div>\n                        </button>\n                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>\n                    </div>\n                </div>\n            </div>\n        `;\n\n        const prevButton = `\n            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>\n                <div class='switch-btns'>\n                    <div class='btns-container'>\n                        <button type='button' class='u-btn'>\n                            <div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div>\n                        </button>\n                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>\n                    </div>\n                </div>\n            </div>\n        `;\n\n        const nextButton = `\n            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>\n                <div class='switch-btns'>\n                    <div class='btns-container'>\n                        <button type='button' class='u-btn'>\n                            <div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div>\n                        </button>\n                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>\n                    </div>\n                </div>\n            </div>\n        `;\n\n        const iframe = ComponentFactory.createIframe(\n            AppConfig.CONTAINER_IDS.ZHIBO_IFRAME,\n            \"zhiboIframe\"\n        );\n\n        container.innerHTML = `\n            <div id='${AppConfig.CONTAINER_IDS.LINKS_QUEUE_REFRESH}' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: ${responsive.isMobile ? 0 : -6}px;'>\n                ${refreshButton}\n            </div>\n            <div class='zhiboSubContainer'>\n                <div id='${AppConfig.CONTAINER_IDS.LINKS_QUEUE_PREV}' style='display:inline-block;scale:0.8'>\n                    ${prevButton}\n                </div>\n                <div id='${AppConfig.CONTAINER_IDS.LINKS_QUEUE_NEXT}' style='display:inline-block;scale:0.8'>\n                    ${nextButton}\n                </div>\n            </div>\n        `;\n\n        container.appendChild(iframe);\n        return container;\n    }\n\n    static createYouXiContainer(): HTMLDivElement {\n        const container = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.YOUXI_CONTAINER,\n            height: $(\".swiperTagContainer\").css(\"height\") || ''\n        });\n        container.innerText = app.translator.trans(\"wusong8899-client1.forum.under-construction\") as string;\n        return container;\n    }\n\n    static createShangChengContainer(): HTMLDivElement {\n        const container = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.SHANGCHENG_CONTAINER,\n            height: $(\".swiperTagContainer\").css(\"height\") || ''\n        });\n        container.innerText = app.translator.trans(\"wusong8899-client1.forum.under-construction\") as string;\n        return container;\n    }\n\n    static createButtonCustomizationContainer(): HTMLDivElement {\n        const container = ComponentFactory.createContainer({\n            className: AppConfig.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER,\n            height: $(\".swiperTagContainer\").css(\"height\") || ''\n        });\n\n        const iframe = ComponentFactory.createIframe(\n            AppConfig.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME,\n            \"customButtonIframe\"\n        );\n\n        container.appendChild(iframe);\n        return container;\n    }\n\n    static createHeaderIcon(): HTMLDivElement {\n        const container = ComponentFactory.createContainer({\n            className: \"headerIconContainer\",\n            id: AppConfig.CONTAINER_IDS.HEADER_ICON\n        });\n\n        container.style.display = 'inline-block';\n        container.style.marginTop = '8px';\n        container.innerHTML = '<img src=\"https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png\" style=\"height: 24px;\" />';\n\n        return container;\n    }\n}", "import { extend } from 'flarum/common/extend';\nimport app from 'flarum/forum/app';\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\nimport { Vnode } from 'mithril';\n\nimport { DataManager } from '../services/data-service';\nimport { EventManager } from '../services/event-manager';\nimport { SwiperManager } from '../services/swiper-manager';\nimport { \n    AdSwiperComponent, \n    TagSwiperComponent, \n    TronscanSwiperComponent,\n    ButtonNavigationComponent,\n    ContainerComponents\n} from '../components';\nimport { AppConfig, DeviceConfig, SettingsConfig } from '../config/app-config';\nimport { ButtonsCustomizationMap, LeftValueMap, AppState, Initializable } from '../types';\n\n/**\n * Main application controller that orchestrates all modules\n */\nexport class AppController implements Initializable {\n    private static instance: AppController;\n    \n    private dataManager: DataManager;\n    private eventManager: EventManager;\n    private appState: AppState;\n    private isInitialized: boolean = false;\n\n    private constructor() {\n        this.dataManager = DataManager.getInstance();\n        this.eventManager = EventManager.getInstance();\n        this.appState = {\n            currentView: 'forum',\n            selectedButton: 0,\n            isMobile: DeviceConfig.isMobile(),\n            isInitialized: false\n        };\n    }\n\n    public static getInstance(): AppController {\n        if (!AppController.instance) {\n            AppController.instance = new AppController();\n        }\n        return AppController.instance;\n    }\n\n    /**\n     * Initialize the application\n     */\n    public async initialize(): Promise<void> {\n        if (this.isInitialized) {\n            return;\n        }\n\n        try {\n            this.setupHeaderExtension();\n            this.isInitialized = true;\n            this.appState.isInitialized = true;\n        } catch (error) {\n            console.error('Failed to initialize AppController:', error);\n            throw error;\n        }\n    }\n\n    public isInitialized(): boolean {\n        return this.isInitialized;\n    }\n\n    /**\n     * Setup the header extension\n     */\n    private setupHeaderExtension(): void {\n        extend(HeaderPrimary.prototype, 'view', (vnode: Vnode<any, HeaderPrimary>) => {\n            const routeName = app.current.get('routeName');\n\n            if (routeName === \"tags\") {\n                this.attachAdvertiseHeader(vnode);\n            }\n        });\n    }\n\n    /**\n     * Attach the advertise header functionality\n     */\n    private attachAdvertiseHeader(vdom: Vnode<any>): void {\n        this.eventManager.setupMobileAdjustments();\n\n        const task = setInterval(() => {\n            if (vdom.dom) {\n                clearInterval(task);\n                this.initializeMainComponents(vdom);\n            }\n        }, AppConfig.CHECK_TIME);\n    }\n\n    /**\n     * Initialize main components\n     */\n    private async initializeMainComponents(vdom: Vnode<any>): Promise<void> {\n        try {\n            // Check if already initialized\n            if (document.getElementById(AppConfig.CONTAINER_IDS.SWIPER_AD_CONTAINER)) {\n                return;\n            }\n\n            // Create and attach advertisement swiper\n            const adSwiper = AdSwiperComponent.create();\n            $(AppConfig.SELECTORS.CONTENT_CONTAINER).prepend(adSwiper);\n            SwiperManager.createAdSwiper();\n\n            // Load all data\n            await this.dataManager.loadAllData();\n\n            // Wait for data to be loaded\n            this.waitForDataAndInitialize();\n\n        } catch (error) {\n            console.error('Error initializing main components:', error);\n        }\n    }\n\n    /**\n     * Wait for data to load and initialize components\n     */\n    private waitForDataAndInitialize(): void {\n        const checkDataTask = setInterval(() => {\n            if (this.dataManager.isAllDataLoaded()) {\n                clearInterval(checkDataTask);\n                this.initializeDataDependentComponents();\n            }\n        }, AppConfig.DATA_CHECK_INTERVAL);\n    }\n\n    /**\n     * Initialize components that depend on loaded data\n     */\n    private initializeDataDependentComponents(): void {\n        try {\n            if (document.getElementById(AppConfig.CONTAINER_IDS.SWIPER_TAG_CONTAINER)) {\n                return;\n            }\n\n            this.createCategoryLayout();\n            this.createContainers();\n            this.createButtonNavigation();\n            this.setupEventHandlers();\n            this.setupMiscellaneousFeatures();\n\n        } catch (error) {\n            console.error('Error initializing data-dependent components:', error);\n        }\n    }\n\n    /**\n     * Create category layout with tag swiper\n     */\n    private createCategoryLayout(): void {\n        const tagSwiper = TagSwiperComponent.create();\n        $(AppConfig.SELECTORS.TAGS_PAGE_CONTENT).prepend(tagSwiper);\n        \n        TagSwiperComponent.addTextContent(tagSwiper);\n        SwiperManager.createTagSwiper();\n\n        // Add Tronscan component\n        this.addTronscanComponent(tagSwiper);\n\n        // Remove original tag tiles and apply mobile styles\n        $(AppConfig.SELECTORS.TAG_TILES).remove();\n        this.applyMobileStyles();\n    }\n\n    /**\n     * Add Tronscan component to the layout\n     */\n    private addTronscanComponent(parentContainer: HTMLDivElement): void {\n        const tronscanData = this.dataManager.getTronscanService().getData();\n        if (tronscanData && tronscanData.length > 0) {\n            const tronscanSwiper = TronscanSwiperComponent.create(tronscanData);\n            $(parentContainer).append(tronscanSwiper);\n            SwiperManager.createTronscanSwiper();\n        }\n    }\n\n    /**\n     * Create all container components\n     */\n    private createContainers(): void {\n        const tagsPageContent = $(AppConfig.SELECTORS.TAGS_PAGE_CONTENT);\n        \n        // Create containers\n        const zhiboContainer = ContainerComponents.createZhiboContainer();\n        const youxiContainer = ContainerComponents.createYouXiContainer();\n        const buttonCustomizationContainer = ContainerComponents.createButtonCustomizationContainer();\n        const shangchengContainer = ContainerComponents.createShangChengContainer();\n\n        // Append containers\n        tagsPageContent.prepend(zhiboContainer);\n        tagsPageContent.prepend(youxiContainer);\n        tagsPageContent.prepend(buttonCustomizationContainer);\n        tagsPageContent.prepend(shangchengContainer);\n    }\n\n    /**\n     * Create button navigation\n     */\n    private createButtonNavigation(): void {\n        const buttonsCustomizationData = this.dataManager.getButtonsCustomizationService().getData() || [];\n        const buttonNavigation = ButtonNavigationComponent.create(buttonsCustomizationData);\n        \n        $(AppConfig.SELECTORS.TAGS_PAGE_CONTENT).prepend(buttonNavigation);\n    }\n\n    /**\n     * Setup event handlers\n     */\n    private setupEventHandlers(): void {\n        const buttonsCustomizationData = this.dataManager.getButtonsCustomizationService().getData() || [];\n        const { buttonsCustomizationMap, leftValueMap, totalButtons } = this.calculateButtonMappings(buttonsCustomizationData);\n\n        this.eventManager.setupButtonNavigation(buttonsCustomizationMap, leftValueMap, totalButtons);\n        this.eventManager.setupZhiboNavigation();\n    }\n\n    /**\n     * Calculate button mappings for navigation\n     */\n    private calculateButtonMappings(buttonsCustomizationData: any[]) {\n        const responsive = DeviceConfig.getResponsiveConfig();\n        const buttonsCustomizationMap: ButtonsCustomizationMap = {};\n        const leftValueMap: LeftValueMap = {};\n        let totalButtons = AppConfig.DEFAULT_BUTTON_COUNT;\n\n        // Map custom buttons\n        buttonsCustomizationData.forEach(data => {\n            totalButtons++;\n            buttonsCustomizationMap[totalButtons] = { url: data.url() };\n        });\n\n        // Calculate left positions for button selection indicator\n        let leftValuePrev = 0;\n        leftValueMap[0] = 0;\n\n        for (let i = 0; i <= totalButtons; i++) {\n            const buttonElement = $(`#client1HeaderButton${i}`);\n            const leftValue = buttonElement.outerWidth();\n\n            if (i === 1 || i === 2) {\n                continue;\n            }\n\n            if (i === 0 && leftValue) {\n                const buttonSelectedBackground = $(`#${AppConfig.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND}`);\n                buttonSelectedBackground.width(leftValue);\n            }\n\n            if (leftValue) {\n                leftValueMap[i + 1] = leftValue + leftValuePrev - responsive.leftModifier;\n                leftValuePrev += leftValue;\n            }\n        }\n\n        return { buttonsCustomizationMap, leftValueMap, totalButtons };\n    }\n\n    /**\n     * Setup miscellaneous features\n     */\n    private setupMiscellaneousFeatures(): void {\n        this.eventManager.setupLeaderboardPosition();\n\n        if (!app.session.user) {\n            this.addHeaderIcon();\n        }\n    }\n\n    /**\n     * Add header icon\n     */\n    private addHeaderIcon(): void {\n        const headerIcon = ContainerComponents.createHeaderIcon();\n        $(AppConfig.SELECTORS.APP_BACK_CONTROL).prepend(headerIcon);\n    }\n\n    /**\n     * Apply mobile-specific styles\n     */\n    private applyMobileStyles(): void {\n        if (this.appState.isMobile) {\n            $(AppConfig.SELECTORS.APP).css(\"overflow-x\", \"hidden\");\n            $(AppConfig.SELECTORS.APP_CONTENT).css({\n                \"min-height\": \"auto\",\n                \"background\": \"\"\n            });\n        }\n    }\n\n    /**\n     * Get current application state\n     */\n    public getAppState(): AppState {\n        return { ...this.appState };\n    }\n\n    /**\n     * Update application state\n     */\n    public updateAppState(updates: Partial<AppState>): void {\n        this.appState = { ...this.appState, ...updates };\n    }\n\n    /**\n     * Cleanup resources\n     */\n    public dispose(): void {\n        this.eventManager.offAll();\n        SwiperManager.destroyAll();\n        this.isInitialized = false;\n        this.appState.isInitialized = false;\n    }\n\n    /**\n     * Restart the application\n     */\n    public async restart(): Promise<void> {\n        this.dispose();\n        await this.initialize();\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { AppController } from './controllers/app-controller';\n\n/**\n * Main entry point for the forum extension\n */\napp.initializers.add('wusong8899-client1-header-adv', async () => {\n    try {\n        const appController = AppController.getInstance();\n        await appController.initialize();\n\n        console.log('Client1 Header Adv extension initialized successfully');\n    } catch (error) {\n        console.error('Failed to initialize Client1 Header Adv extension:', error);\n    }\n});\n\n\n\n\n"], "names": ["regeneratorDefine", "require", "_regenerator", "e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "this", "setPrototypeOf", "__proto__", "displayName", "module", "exports", "w", "m", "__esModule", "regenerator", "regeneratorAsyncIterator", "Promise", "unshift", "pop", "k", "runtime", "regeneratorRuntime", "accidentalStrictMode", "globalThis", "Function", "OverloadYield", "AsyncIterator", "resolve", "then", "next", "asyncIterator", "regeneratorAsyncGen", "regeneratorAsync", "regeneratorKeys", "regeneratorValues", "_regeneratorRuntime", "constructor", "name", "stop", "abrupt", "<PERSON><PERSON><PERSON>", "resultName", "finish", "_t", "prev", "sent", "wrap", "reverse", "isGeneratorFunction", "mark", "awrap", "async", "keys", "values", "_typeof", "_regeneratorDefine", "defineProperty", "_invoke", "enumerable", "configurable", "writable", "isNaN", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "getter", "definition", "key", "get", "obj", "prop", "hasOwnProperty", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "flarum", "core", "compat", "_extends", "assign", "_setPrototypeOf", "_inherits<PERSON><PERSON>e", "BaseDataService", "loading", "data", "_proto", "isLoading", "getData", "fetchData", "_fetchData", "_callee", "endpoint", "_results", "_context", "app", "find", "results", "parseResults", "console", "error", "_x", "TronscanService", "_BaseDataService2", "_proto2", "load", "list", "push", "ButtonsCustomizationService", "_BaseDataService3", "_proto3", "LinksQueueService", "_BaseDataService4", "_this", "_len", "args", "Array", "_key", "concat", "pointer", "_proto4", "getPointer", "setPointer", "incrementPointer", "decrementPointer", "getCurrentItem", "hasNext", "has<PERSON>revious", "DataManager", "tronscanService", "buttonsCustomizationService", "linksQueueService", "getInstance", "instance", "_proto5", "getTronscanService", "getButtonsCustomizationService", "getLinksQueueService", "loadAllData", "_loadAllData", "_callee2", "promises", "_context2", "filter", "promise", "all", "isAllDataLoaded", "isAnyDataLoading", "AppConfig", "CHECK_TIME", "DEFAULT_TRANSITION_TIME", "DATA_CHECK_INTERVAL", "ZHIBO_REFRESH_DELAY", "MOBILE_SPACE_BETWEEN", "DESKTOP_SPACE_BETWEEN", "MOBILE_SLIDES_PER_VIEW", "DESKTOP_SLIDES_PER_VIEW", "TRONSCAN_MOBILE_SLIDES", "TRONSCAN_DESKTOP_SLIDES", "TRONSCAN_MOBILE_SPACE", "EXTENSION_PREFIX", "TRANSLATION_PREFIX", "DEFAULT_BUTTON_COUNT", "MAX_LINK_IMAGE_PAIRS", "SELECTORS", "APP", "APP_NAVIGATION", "APP_CONTENT", "CONTENT_CONTAINER", "TAGS_PAGE_CONTENT", "TAG_TILES", "TAG_TILE", "ITEM_NEW_DISCUSSION", "ITEM_NAV", "ITEM_MONEY_LEADERBOARD", "ITEM_FORUM_CHECKIN", "APP_BACK_CONTROL", "CONTAINER_IDS", "SWIPER_TAG_CONTAINER", "SWIPER_AD_CONTAINER", "TRONSCAN_TEXT_CONTAINER", "SELECT_TITLE_CONTAINER", "HEADER_ICON", "ZHIBO_IFRAME", "CUSTOM_BUTTON_IFRAME", "BUTTON_SELECTED_BACKGROUND", "LINKS_QUEUE_REFRESH", "LINKS_QUEUE_PREV", "LINKS_QUEUE_NEXT", "CSS_CLASSES", "TAG_SWIPER", "AD_SWIPER", "TRONSCAN_SWIPER", "ZHIBO_CONTAINER", "YOUXI_CONTAINER", "SHANGCHENG_CONTAINER", "BUTTON_CUSTOMIZATION_CONTAINER", "U_BTN", "SWIPER_SLIDE_TAG", "SWIPER_SLIDE_TAG_INNER", "SWIPER_SLIDE_TAG_INNER_MOBILE", "SettingsConfig", "getTransitionTime", "transitionTime", "attribute", "Number", "getImageSrc", "index", "getImageLink", "getImageLinkPairs", "pairs", "src", "link", "DeviceConfig", "isMobile", "_isMobile", "mobileCheck", "check", "userAgent", "navigator", "vendor", "window", "opera", "test", "substr", "getResponsiveConfig", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "tronscanSlidesPerView", "tronscanSpaceBetween", "eventType", "leftModifier", "EventManager", "handlers", "Map", "dataManager", "on", "id", "selector", "event", "handler", "has", "set", "<PERSON><PERSON><PERSON><PERSON>", "$", "document", "off", "for<PERSON>ach", "_ref", "offAll", "setupButtonNavigation", "buttonsCustomizationMap", "leftValueMap", "totalButtons", "_this2", "responsive", "target", "currentTarget", "numberStr", "attr", "number", "parseInt", "zhiboIframe", "getElementById", "css", "handleButtonClick", "updateButtonSelection", "hideAllContainers", "showForumView", "showZhiboView", "showYouxiView", "showShangchengView", "showCustomButtonView", "appNavHeight", "outerHeight", "selectTitleHeight", "linksQueueHeight", "iframeHeight", "innerHeight", "currentItem", "linksQueueURL", "customButtonData", "paddingBottom", "scrolling", "containerHeight", "customButtonIframe", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outerWidth", "buttonSelectedBackground", "width", "setupZhiboNavigation", "_this3", "setTimeout", "updateZhiboButtonStates", "setupMobileAdjustments", "html", "remove", "setupLeaderboardPosition", "addClass", "parent", "append", "extend", "noExtend", "indexOf", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "doc", "ssrWindow", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "win", "delay", "now", "toString", "slice", "isNode", "node", "HTMLElement", "nodeType", "to", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "__swiper__", "el", "varName", "varValue", "setProperty", "animateCSSModeScroll", "swiper", "targetPosition", "side", "startPosition", "translate", "time", "startTime", "duration", "params", "speed", "wrapperEl", "scrollSnapType", "cssModeFrameID", "dir", "isOutOfBound", "current", "animate", "getTime", "progress", "Math", "max", "min", "easeProgress", "cos", "PI", "currentPosition", "scrollTo", "overflow", "slideEl", "shadowRoot", "element", "HTMLSlotElement", "assignedElements", "matches", "showWarning", "text", "warn", "err", "tag", "classes", "classList", "add", "isArray", "trim", "split", "elementStyle", "child", "previousSibling", "parents", "parentElement", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "parseFloat", "offsetWidth", "trustedTypes", "innerHTML", "createPolicy", "createHTML", "s", "support", "deviceCached", "browser", "getSupport", "smoothScroll", "documentElement", "touch", "DocumentTouch", "calcSupport", "getDevice", "overrides", "_temp", "platform", "ua", "device", "ios", "android", "screenWidth", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "os", "calcDevice", "<PERSON><PERSON><PERSON><PERSON>", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "String", "includes", "major", "minor", "map", "num", "isWebView", "isSafariB<PERSON><PERSON>", "need3dFix", "calcB<PERSON>er", "eventsEmitter", "events", "priority", "self", "eventsListeners", "destroyed", "method", "once", "once<PERSON><PERSON><PERSON>", "__emitterProxy", "onAny", "eventsAnyListeners", "offAny", "splice", "emit", "context", "_len2", "_key2", "toggleSlideClasses$1", "condition", "className", "contains", "toggleSlideClasses", "processLazyPreloader", "imageEl", "closest", "isElement", "slideClass", "lazyEl", "lazyPreloaderClass", "unlazy", "slides", "removeAttribute", "preload", "amount", "lazyPreloadPrevNext", "slidesPerViewDynamic", "ceil", "activeIndex", "grid", "rows", "activeColumn", "preloadColumns", "from", "_", "column", "slideIndexLastInView", "rewind", "loop", "realIndex", "update", "updateSize", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "updateSlides", "getDirectionPropertyValue", "label", "getDirectionLabel", "slidesEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "slidePosition", "prevSlideSize", "replace", "virtualSize", "marginLeft", "marginRight", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "slideSize", "initSlides", "unsetSlides", "shouldResetSlideSize", "breakpoints", "slide", "updateSlide", "slideStyles", "currentTransform", "transform", "currentWebKitTransform", "webkitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "floor", "swiperSlideSize", "abs", "slidesPerGroup", "slidesPerGroupSkip", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "groups", "slidesBefore", "slidesAfter", "groupSize", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "maxBackfaceHiddenSlides", "updateAutoHeight", "activeSlides", "newHeight", "setTransition", "getSlideByIndex", "getSlideIndexByData", "visibleSlides", "offsetHeight", "minusOffset", "offsetLeft", "offsetTop", "swiperSlideOffset", "cssOverflowAdjustment", "updateSlidesProgress", "offsetCenter", "visibleSlidesIndexes", "slideOffset", "slideProgress", "minTranslate", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "slideVisibleClass", "slideFullyVisibleClass", "originalProgress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "autoHeight", "updateSlidesClasses", "getFilteredSlide", "activeSlide", "prevSlide", "nextSlide", "nextEls", "nextElement<PERSON><PERSON>ling", "elementNextAll", "prevEls", "previousElementSibling", "elementPrevAll", "slideActiveClass", "slideNextClass", "slidePrevClass", "emitSlidesClasses", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "normalizeSlideIndex", "getActiveIndexByTranslate", "skip", "firstSlideInColumn", "activeSlideIndex", "getAttribute", "initialized", "runCallbacksOnInit", "updateClickedSlide", "path", "pathEl", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "transitionEmit", "runCallbacks", "direction", "step", "slideTo", "internal", "initial", "animating", "preventInteractionOnTransition", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "allowSlideNext", "allowSlidePrev", "setTranslate", "transitionStart", "transitionEnd", "isH", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "behavior", "onSlideToWrapperTransitionEnd", "slideToLoop", "newIndex", "targetSlideIndex", "cols", "needLoopFix", "loopFix", "slideRealIndex", "slideNext", "perGroup", "slidesPerGroupAuto", "increment", "loopPreventsSliding", "_clientLeft", "clientLeft", "slidePrev", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "freeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "slideToIndex", "getSlideIndexWhenGrid", "slideSelector", "isGrid", "getSlideIndex", "loopCreate", "loopAddBlankSlides", "slideBlankClass", "recalcSlides", "clearBlankSlides", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "byController", "byMousewheel", "loopedSlides", "loopAdditionalSlides", "fill", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndexWithShift", "colIndexToPrepend", "__preventObserver__", "swiperLoopMoveDOM", "prepend", "currentSlideTranslate", "diff", "touchEventsData", "startTranslate", "currentTranslate", "shift", "controller", "control", "loopParams", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "swiperSlideIndex", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "innerWidth", "preventDefault", "onTouchStart", "originalEvent", "type", "pointerId", "targetTouches", "touchId", "identifier", "pageX", "touches", "simulate<PERSON>ouch", "pointerType", "targetEl", "touchEventsTarget", "<PERSON><PERSON><PERSON><PERSON>", "slot", "elementsQueue", "elementToCheck", "elementIsChildOfSlot", "elementIsChildOf", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "noSwipingSelector", "isTargetShadow", "noSwiping", "base", "__closestFrom", "assignedSlot", "found", "getRootNode", "closestElement", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "currentY", "pageY", "startY", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "focusableElements", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "onTouchMove", "targetTouch", "changedTouches", "preventedByNestedSwiper", "touchReleaseOnEdges", "previousX", "previousY", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "preventTouchMoveFromPointerMove", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "touchesDiff", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "isLoop", "allowLoopFix", "getTranslate", "evt", "bubbles", "detail", "bySwiperTouchMove", "dispatchEvent", "allowMomentumBounce", "grabCursor", "setGrabCursor", "_loopSwapReset", "loopSwapReset", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "onTouchEnd", "touchEndTime", "timeDiff", "pathTree", "lastClickTime", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "longSwipesMs", "longSwipes", "longSwipesRatio", "shortSwipes", "navigation", "nextEl", "prevEl", "onResize", "setBreakpoint", "isVirtualLoop", "autoplay", "running", "paused", "resizeTimeout", "resume", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "newProgress", "previousTranslate", "scrollLeft", "scrollTop", "onLoad", "onDocumentTouchStart", "documentTouchHandlerProceeded", "touchAction", "capture", "dom<PERSON>ethod", "swiperMethod", "passive", "updateOnWindowResize", "isGridEnabled", "defaults", "init", "swiperElementNodeName", "resizeObserver", "createElements", "eventsPrefix", "virtualTranslate", "breakpointsBase", "uniqueNavElements", "passiveListeners", "wrapperClass", "_emitClasses", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "auto", "prototypes", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "currentStyle", "WebKitCSSMatrix", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "m41", "m42", "x", "translateTo", "translateBounds", "newTranslate", "onTranslateToWrapperTransitionEnd", "transition", "transitionDuration", "transitionDelay", "moving", "isLocked", "cursor", "unsetGrabCursor", "attachEvents", "detachEvents", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "emitContainerClasses", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "changeDirection", "isEnabled", "<PERSON><PERSON><PERSON>", "containerEl", "currentHeight", "points", "point", "minRatio", "sort", "b", "wasLocked", "lastSlideRightEdge", "addClasses", "classNames", "suffixes", "entries", "prefix", "resultClasses", "item", "prepareClasses", "removeClasses", "extendedDefaults", "Swiper", "swipers", "newParams", "modules", "__modules__", "mod", "extendParams", "swiperParams", "passedParams", "eventName", "velocity", "trunc", "clickTimeout", "velocities", "imagesToLoad", "imagesLoaded", "property", "setProgress", "cls", "getSlideClasses", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "complete", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "mounted", "parentNode", "toUpperCase", "getWrapperSelector", "slideSlots", "hostEl", "lazyElements", "destroy", "deleteInstance", "cleanStyles", "object", "deleteProps", "extendDefaults", "newDefaults", "installModule", "use", "checkProps", "Navigation", "getEl", "res", "toggleEl", "disabled", "subEl", "disabledClass", "tagName", "lockClass", "onPrevClick", "onNextClick", "initButton", "destroyButton", "hideOnClick", "hiddenClass", "navigationDisabledClass", "_s", "targetIsButton", "pagination", "clickable", "isHidden", "toggle", "Pagination", "pfx", "bulletSize", "bulletElement", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "formatFractionTotal", "bulletClass", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "bulletEl", "position", "onBulletClick", "moveDirection", "total", "firstIndex", "midIndex", "classesToRemove", "suffix", "flat", "bullet", "bulletIndex", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "fractionEl", "textContent", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "render", "paginationHTML", "numberOfBullets", "Autoplay", "timeout", "raf", "timeLeft", "waitForTransition", "disableOnInteraction", "stopOnLastSlide", "reverseDirection", "pauseOnMouseEnter", "autoplayTimeLeft", "wasPaused", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "pausedByPointerEnter", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayStartTime", "onTransitionEnd", "calcTimeLeft", "run", "delayForce", "currentSlideDelay", "activeSlideEl", "getSlideDelay", "proceed", "start", "pause", "reset", "onVisibilityChange", "visibilityState", "onPointerEnter", "onPointerLeave", "effectParams", "transformEl", "backfaceVisibility", "shadowClass", "shadow<PERSON><PERSON><PERSON>", "shadowEl", "EffectCoverflow", "coverflowEffect", "rotate", "stretch", "depth", "modifier", "slideShadows", "overwriteParams", "perspective", "recreateShadows", "getEffectParams", "requireUpdateOnVirtual", "overwriteParamsResult", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "center", "centerOffset", "offsetMultiplier", "rotateY", "rotateX", "translateZ", "translateY", "translateX", "slideTransform", "zIndex", "round", "shadowBeforeEl", "shadowAfterEl", "opacity", "prototypeGroup", "protoMethod", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "ResizeObserver", "newWidth", "_ref2", "contentBoxSize", "contentRect", "inlineSize", "blockSize", "observe", "unobserve", "observers", "attach", "options", "MutationObserver", "WebkitMutationObserver", "mutations", "observerUpdate", "attributes", "childList", "characterData", "observeParents", "observeSlideChildren", "containerParents", "disconnect", "SwiperConfigFactory", "createTagSwiperConfig", "createTronscanSwiperConfig", "createAdSwiperConfig", "SwiperManager", "config", "instanceId", "instances", "destroyAll", "clear", "createTagSwiper", "createTronscanSwiper", "createAdSwiper", "updateSwiper", "startAutoplay", "stopAutoplay", "getAllInstances", "hasInstance", "ComponentFactory", "createContainer", "container", "createSwiperContainer", "createSwiperWrapper", "wrapper", "createSwiperSlide", "createSwiperNavigation", "createButton", "iconClass", "createIframe", "iframe", "AdSwiperComponent", "styleWidth", "swiper<PERSON><PERSON><PERSON>", "pair", "append<PERSON><PERSON><PERSON>", "nextButton", "prevButton", "TagSwiperComponent", "tagTile", "tagTextOuterContainer", "tagURL", "tagBackground", "tagNameColor", "slideContent", "addTextContent", "tagTextContainer", "TronscanSwiperComponent", "tronscanData", "textContainer", "tronscanValueUsd", "valueUsd", "img", "ButtonNavigationComponent", "buttonsCustomizationData", "buttonsCustomization", "icon", "selectTitle", "tagsPageWidth", "defaultButtons", "ContainerComponents", "createZhiboContainer", "createYouXiContainer", "innerText", "trans", "createShangChengContainer", "createButtonCustomizationContainer", "createHeaderIcon", "display", "AppController", "eventManager", "appState", "isInitialized", "current<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "initialize", "_initialize", "setupHeaderExtension", "HeaderPrimary", "vnode", "attachAdvertiseHeader", "vdom", "task", "setInterval", "dom", "clearInterval", "initializeMainComponents", "_initializeMainComponents", "adSwiper", "_t2", "waitForDataAndInitialize", "checkDataTask", "initializeDataDependentComponents", "createCategoryLayout", "createContainers", "createButtonNavigation", "setupEventHandlers", "setupMiscellaneousFeatures", "tagSwiper", "addTronscanComponent", "applyMobileStyles", "parentContainer", "tronscanSwiper", "tagsPageContent", "zhiboContainer", "youxiC<PERSON><PERSON>", "buttonCustomizationContainer", "shangchengContainer", "buttonNavigation", "_this$calculateButton", "calculateButtonMappings", "leftValuePrev", "leftValue", "user", "addHeaderIcon", "headerIcon", "getAppState", "updateAppState", "dispose", "restart", "_restart", "_callee3", "_context3", "appController", "log"], "sourceRoot": ""}
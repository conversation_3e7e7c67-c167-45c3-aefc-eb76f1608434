import app from 'flarum/forum/app';
import { TronscanData, ButtonsCustomizationData, LinksQueueData } from '../types';

/**
 * Base interface for data services
 */
interface DataService<T> {
    load(): Promise<T[]> | undefined;
    isLoading(): boolean;
    getData(): T[] | null;
    parseResults(results: any): T[];
}

/**
 * Abstract base class for data services
 */
abstract class BaseDataService<T> implements DataService<T> {
    protected loading: boolean = false;
    protected data: T[] | null = null;

    public isLoading(): boolean {
        return this.loading;
    }

    public getData(): T[] | null {
        return this.data;
    }

    public abstract load(): Promise<T[]> | undefined;
    public abstract parseResults(results: any): T[];

    protected async fetchData(endpoint: string): Promise<T[]> {
        if (this.loading) {
            return Promise.resolve([]);
        }

        this.loading = true;
        
        try {
            const results = await app.store.find(endpoint);
            this.data = this.parseResults(results);
            return this.data;
        } catch (error) {
            console.error(`Error loading data from ${endpoint}:`, error);
            this.data = [];
            return this.data;
        } finally {
            this.loading = false;
        }
    }
}

/**
 * Service for managing Tronscan data
 */
export class TronscanService extends BaseDataService<TronscanData> {
    public load(): Promise<TronscanData[]> | undefined {
        return this.fetchData("syncTronscanList");
    }

    public parseResults(results: any): TronscanData[] {
        const list: TronscanData[] = [];
        if (results) {
            [].push.apply(list, results);
        }
        return list;
    }
}

/**
 * Service for managing buttons customization data
 */
export class ButtonsCustomizationService extends BaseDataService<ButtonsCustomizationData> {
    public load(): Promise<ButtonsCustomizationData[]> | undefined {
        return this.fetchData("buttonsCustomizationList");
    }

    public parseResults(results: any): ButtonsCustomizationData[] {
        const list: ButtonsCustomizationData[] = [];
        if (results) {
            [].push.apply(list, results);
        }
        return list;
    }
}

/**
 * Service for managing links queue data
 */
export class LinksQueueService extends BaseDataService<LinksQueueData> {
    private pointer: number = 0;

    public load(): Promise<LinksQueueData[]> | undefined {
        return this.fetchData("linksQueueList");
    }

    public parseResults(results: any): LinksQueueData[] {
        const list: LinksQueueData[] = [];
        if (results) {
            [].push.apply(list, results);
        }
        return list;
    }

    public getPointer(): number {
        return this.pointer;
    }

    public setPointer(value: number): void {
        this.pointer = value;
    }

    public incrementPointer(): void {
        this.pointer++;
    }

    public decrementPointer(): void {
        this.pointer--;
    }

    public getCurrentItem(): LinksQueueData | null {
        if (this.data && this.data[this.pointer]) {
            return this.data[this.pointer];
        }
        return null;
    }

    public hasNext(): boolean {
        return this.data !== null && this.data[this.pointer + 1] !== undefined;
    }

    public hasPrevious(): boolean {
        return this.data !== null && this.data[this.pointer - 1] !== undefined;
    }
}

/**
 * Centralized data manager for all services
 */
export class DataManager {
    private static instance: DataManager;
    
    private tronscanService: TronscanService;
    private buttonsCustomizationService: ButtonsCustomizationService;
    private linksQueueService: LinksQueueService;

    private constructor() {
        this.tronscanService = new TronscanService();
        this.buttonsCustomizationService = new ButtonsCustomizationService();
        this.linksQueueService = new LinksQueueService();
    }

    public static getInstance(): DataManager {
        if (!DataManager.instance) {
            DataManager.instance = new DataManager();
        }
        return DataManager.instance;
    }

    public getTronscanService(): TronscanService {
        return this.tronscanService;
    }

    public getButtonsCustomizationService(): ButtonsCustomizationService {
        return this.buttonsCustomizationService;
    }

    public getLinksQueueService(): LinksQueueService {
        return this.linksQueueService;
    }

    /**
     * Load all data services
     */
    public async loadAllData(): Promise<void> {
        const promises = [
            this.tronscanService.load(),
            this.buttonsCustomizationService.load(),
            this.linksQueueService.load()
        ].filter(promise => promise !== undefined) as Promise<any>[];

        await Promise.all(promises);
    }

    /**
     * Check if all data is loaded
     */
    public isAllDataLoaded(): boolean {
        return this.tronscanService.getData() !== null &&
               this.buttonsCustomizationService.getData() !== null &&
               this.linksQueueService.getData() !== null;
    }

    /**
     * Check if any data is loading
     */
    public isAnyDataLoading(): boolean {
        return this.tronscanService.isLoading() ||
               this.buttonsCustomizationService.isLoading() ||
               this.linksQueueService.isLoading();
    }
}

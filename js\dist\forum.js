/*! For license information please see forum.js.LICENSE.txt */
(()=>{var e={126:(e,t,i)=>{var n=i(893);function s(){var t,i,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function l(e,s,r,a){var o=s&&s.prototype instanceof c?s:c,l=Object.create(o.prototype);return n(l,"_invoke",function(e,n,s){var r,a,o,l=0,c=s||[],p=!1,u={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,i){return r=e,a=0,o=t,u.n=i,d}};function f(e,n){for(a=e,o=n,i=0;!p&&l&&!s&&i<c.length;i++){var s,r=c[i],f=u.p,h=r[2];e>3?(s=h===n)&&(o=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=t):r[0]<=f&&((s=e<2&&f<r[1])?(a=0,u.v=n,u.n=r[1]):f<h&&(s=e<3||r[0]>n||n>h)&&(r[4]=e,r[5]=n,u.n=h,a=0))}if(s||e>1)return d;throw p=!0,n}return function(s,c,h){if(l>1)throw TypeError("Generator is already running");for(p&&1===c&&f(c,h),a=c,o=h;(i=a<2?t:o)||!p;){r||(a?a<3?(a>1&&(u.n=-1),f(a,o)):u.n=o:u.v=o);try{if(l=2,r){if(a||(s="next"),i=r[s]){if(!(i=i.call(r,o)))throw TypeError("iterator result is not an object");if(!i.done)return i;o=i.value,a<2&&(a=0)}else 1===a&&(i=r.return)&&i.call(r),a<2&&(o=TypeError("The iterator does not provide a '"+s+"' method"),a=1);r=t}else if((i=(p=u.n<0)?o:e.call(n,u))!==d)break}catch(e){r=t,a=1,o=e}finally{l=1}}return{value:i,done:p}}}(e,r,a),!0),l}var d={};function c(){}function p(){}function u(){}i=Object.getPrototypeOf;var f=[][a]?i(i([][a]())):(n(i={},a,function(){return this}),i),h=u.prototype=c.prototype=Object.create(f);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,n(e,o,"GeneratorFunction")),e.prototype=Object.create(h),e}return p.prototype=u,n(h,"constructor",u),n(u,"constructor",p),p.displayName="GeneratorFunction",n(u,o,"GeneratorFunction"),n(h),n(h,o,"Generator"),n(h,a,function(){return this}),n(h,"toString",function(){return"[object Generator]"}),(e.exports=s=function(){return{w:l,m}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports},160:(e,t,i)=>{var n=i(126),s=i(638);e.exports=function(e,t,i,r,a){return new s(n().w(e,t,i,r),a||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},262:e=>{e.exports=function(e){var t=Object(e),i=[];for(var n in t)i.unshift(n);return function e(){for(;i.length;)if((n=i.pop())in t)return e.value=n,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},347:e=>{e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},407:(e,t,i)=>{var n=i(744)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},638:(e,t,i)=>{var n=i(347),s=i(893);e.exports=function e(t,i){function r(e,s,a,o){try{var l=t[e](s),d=l.value;return d instanceof n?i.resolve(d.v).then(function(e){r("next",e,a,o)},function(e){r("throw",e,a,o)}):i.resolve(d).then(function(e){l.value=e,a(l)},function(e){return r("throw",e,a,o)})}catch(e){o(e)}}var a;this.next||(s(e.prototype),s(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),s(this,"_invoke",function(e,t,n){function s(){return new i(function(t,i){r(e,n,t,i)})}return a=a?a.then(s,s):s()},!0)},e.exports.__esModule=!0,e.exports.default=e.exports},676:(e,t,i)=>{var n=i(160);e.exports=function(e,t,i,s,r){var a=n(e,t,i,s,r);return a.next().then(function(e){return e.done?e.value:a.next()})},e.exports.__esModule=!0,e.exports.default=e.exports},744:(e,t,i)=>{var n=i(347),s=i(126),r=i(676),a=i(160),o=i(638),l=i(262),d=i(992);function c(){"use strict";var t=s(),i=t.m(c),p=(Object.getPrototypeOf?Object.getPrototypeOf(i):i.__proto__).constructor;function u(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))}var f={throw:1,return:2,break:3,continue:3};function h(e){var t,i;return function(n){t||(t={stop:function(){return i(n.a,2)},catch:function(){return n.v},abrupt:function(e,t){return i(n.a,f[e],t)},delegateYield:function(e,s,r){return t.resultName=s,i(n.d,d(e),r)},finish:function(e){return i(n.f,e)}},i=function(e,i,s){n.p=t.prev,n.n=t.next;try{return e(i,s)}finally{t.next=n.n}}),t.resultName&&(t[t.resultName]=n.v,t.resultName=void 0),t.sent=n.v,t.next=n.n;try{return e.call(this,t)}finally{n.p=t.prev,n.n=t.next}}}return(e.exports=c=function(){return{wrap:function(e,i,n,s){return t.w(h(e),i,n,s&&s.reverse())},isGeneratorFunction:u,mark:t.m,awrap:function(e,t){return new n(e,t)},AsyncIterator:o,async:function(e,t,i,n,s){return(u(t)?a:r)(h(e),t,i,n,s)},keys:l,values:d}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=c,e.exports.__esModule=!0,e.exports.default=e.exports},791:e=>{function t(i){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(i)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},893:e=>{function t(i,n,s,r){var a=Object.defineProperty;try{a({},"",{})}catch(i){a=0}e.exports=t=function(e,i,n,s){function r(i,n){t(e,i,function(e){return this._invoke(i,n,e)})}i?a?a(e,i,{value:n,enumerable:!s,configurable:!s,writable:!s}):e[i]=n:(r("next",0),r("throw",1),r("return",2))},e.exports.__esModule=!0,e.exports.default=e.exports,t(i,n,s,r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},992:(e,t,i)=>{var n=i(791).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],i=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}}}throw new TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function i(n){var s=t[n];if(void 0!==s)return s.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,i),r.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";function e(e,t,i,n,s,r,a){try{var o=e[r](a),l=o.value}catch(e){return void i(e)}o.done?t(l):Promise.resolve(l).then(n,s)}function t(t){return function(){var i=this,n=arguments;return new Promise(function(s,r){var a=t.apply(i,n);function o(t){e(a,s,r,o,l,"next",t)}function l(t){e(a,s,r,o,l,"throw",t)}o(void 0)})}}var n=i(407),s=i.n(n);const r=flarum.core.compat["forum/app"];var a=i.n(r);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)({}).hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},o.apply(null,arguments)}const l=flarum.core.compat["common/extend"],d=flarum.core.compat["forum/components/HeaderPrimary"];var c=i.n(d);function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}function u(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,p(e,t)}var f=function(){function e(){this.loading=!1,this.data=null}var i=e.prototype;return i.isLoading=function(){return this.loading},i.getData=function(){return this.data},i.fetchData=function(){var e=t(s().mark(function e(t){var i,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.loading){e.next=1;break}return e.abrupt("return",Promise.resolve([]));case 1:return this.loading=!0,e.prev=2,e.next=3,a().store.find(t);case 3:return i=e.sent,this.data=this.parseResults(i),e.abrupt("return",this.data);case 4:return e.prev=4,n=e.catch(2),console.error("Error loading data from "+t+":",n),this.data=[],e.abrupt("return",this.data);case 5:return e.prev=5,this.loading=!1,e.finish(5);case 6:case"end":return e.stop()}},e,this,[[2,4,5,6]])}));return function(t){return e.apply(this,arguments)}}(),e}(),h=function(e){function t(){return e.apply(this,arguments)||this}u(t,e);var i=t.prototype;return i.load=function(){return this.fetchData("syncTronscanList")},i.parseResults=function(e){var t=[];return e&&[].push.apply(t,e),t},t}(f),m=function(e){function t(){return e.apply(this,arguments)||this}u(t,e);var i=t.prototype;return i.load=function(){return this.fetchData("buttonsCustomizationList")},i.parseResults=function(e){var t=[];return e&&[].push.apply(t,e),t},t}(f),v=function(e){function t(){for(var t,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];return(t=e.call.apply(e,[this].concat(n))||this).pointer=0,t}u(t,e);var i=t.prototype;return i.load=function(){return this.fetchData("linksQueueList")},i.parseResults=function(e){var t=[];return e&&[].push.apply(t,e),t},i.getPointer=function(){return this.pointer},i.setPointer=function(e){this.pointer=e},i.incrementPointer=function(){this.pointer++},i.decrementPointer=function(){this.pointer--},i.getCurrentItem=function(){return this.data&&this.data[this.pointer]?this.data[this.pointer]:null},i.hasNext=function(){return null!==this.data&&void 0!==this.data[this.pointer+1]},i.hasPrevious=function(){return null!==this.data&&void 0!==this.data[this.pointer-1]},t}(f),g=function(){function e(){this.tronscanService=void 0,this.buttonsCustomizationService=void 0,this.linksQueueService=void 0,this.tronscanService=new h,this.buttonsCustomizationService=new m,this.linksQueueService=new v}e.getInstance=function(){return e.instance||(e.instance=new e),e.instance};var i=e.prototype;return i.getTronscanService=function(){return this.tronscanService},i.getButtonsCustomizationService=function(){return this.buttonsCustomizationService},i.getLinksQueueService=function(){return this.linksQueueService},i.loadAllData=function(){var e=t(s().mark(function e(){var t;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=[this.tronscanService.load(),this.buttonsCustomizationService.load(),this.linksQueueService.load()].filter(function(e){return void 0!==e}),e.next=1,Promise.all(t);case 1:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}(),i.isAllDataLoaded=function(){return null!==this.tronscanService.getData()&&null!==this.buttonsCustomizationService.getData()&&null!==this.linksQueueService.getData()},i.isAnyDataLoading=function(){return this.tronscanService.isLoading()||this.buttonsCustomizationService.isLoading()||this.linksQueueService.isLoading()},e}();g.instance=void 0;var S=function(){};S.CHECK_TIME=10,S.DEFAULT_TRANSITION_TIME=5e3,S.DATA_CHECK_INTERVAL=100,S.ZHIBO_REFRESH_DELAY=100,S.MOBILE_SPACE_BETWEEN=90,S.DESKTOP_SPACE_BETWEEN=10,S.MOBILE_SLIDES_PER_VIEW=2,S.DESKTOP_SLIDES_PER_VIEW=7,S.TRONSCAN_MOBILE_SLIDES=4,S.TRONSCAN_DESKTOP_SLIDES=7,S.TRONSCAN_MOBILE_SPACE=80,S.EXTENSION_PREFIX="wusong8899-client1-header-adv",S.TRANSLATION_PREFIX="wusong8899-client1",S.DEFAULT_BUTTON_COUNT=3,S.MAX_LINK_IMAGE_PAIRS=30,S.SELECTORS={APP:"#app",APP_NAVIGATION:"#app-navigation",APP_CONTENT:".App-content",CONTENT_CONTAINER:"#content .container",TAGS_PAGE_CONTENT:".TagsPage-content",TAG_TILES:".TagTiles",TAG_TILE:".TagTile",ITEM_NEW_DISCUSSION:".item-newDiscussion",ITEM_NAV:".item-nav",ITEM_MONEY_LEADERBOARD:".item-MoneyLeaderboard",ITEM_FORUM_CHECKIN:".item-forum-checkin",APP_BACK_CONTROL:".App-backControl"},S.CONTAINER_IDS={SWIPER_TAG_CONTAINER:"swiperTagContainer",SWIPER_AD_CONTAINER:"swiperAdContainer",TRONSCAN_TEXT_CONTAINER:"TronscanTextContainer",SELECT_TITLE_CONTAINER:"selectTitleContainer",HEADER_ICON:"wusong8899Client1HeaderIcon",ZHIBO_IFRAME:"zhiboIframe",CUSTOM_BUTTON_IFRAME:"customButtonIframe",BUTTON_SELECTED_BACKGROUND:"buttonSelectedBackground",LINKS_QUEUE_REFRESH:"linksQueueRefresh",LINKS_QUEUE_PREV:"linksQueuePrev",LINKS_QUEUE_NEXT:"linksQueueNext"},S.CSS_CLASSES={SWIPER_TAG_CONTAINER:"swiperTagContainer",SWIPER_AD_CONTAINER:"swiperAdContainer",TAG_SWIPER:"swiper tagSwiper",AD_SWIPER:"swiper adSwiper",TRONSCAN_SWIPER:"swiper tronscanSwiper",ZHIBO_CONTAINER:"zhiboContainer",YOUXI_CONTAINER:"youxiContainer",SHANGCHENG_CONTAINER:"shangchengContainer",BUTTON_CUSTOMIZATION_CONTAINER:"buttonCustomizationContainer",SELECT_TITLE_CONTAINER:"selectTitleContainer",U_BTN:"u-btn",SWIPER_SLIDE_TAG:"swiper-slide swiper-slide-tag",SWIPER_SLIDE_TAG_INNER:"swiper-slide-tag-inner",SWIPER_SLIDE_TAG_INNER_MOBILE:"swiper-slide-tag-inner-mobile"};var w=function(){function e(){}return e.getTransitionTime=function(){var e=a().forum.attribute("Client1HeaderAdvTransitionTime");return e?Number(e):S.DEFAULT_TRANSITION_TIME},e.getImageSrc=function(e){return a().forum.attribute("Client1HeaderAdvImage"+e)||null},e.getImageLink=function(e){return a().forum.attribute("Client1HeaderAdvLink"+e)||null},e.getImageLinkPairs=function(){for(var e=[],t=1;t<=S.MAX_LINK_IMAGE_PAIRS;t++){var i=this.getImageSrc(t),n=this.getImageLink(t);i&&e.push({src:i,link:n||"#",index:t})}return e},e}(),b=function(){function e(){}return e.isMobile=function(){return null===this._isMobile&&(this._isMobile=this.mobileCheck()),this._isMobile},e.mobileCheck=function(){var e=!1,t=navigator.userAgent||navigator.vendor||window.opera;return t&&(e=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4))),e},e.getResponsiveConfig=function(){var e=this.isMobile();return{isMobile:e,spaceBetween:e?S.MOBILE_SPACE_BETWEEN:S.DESKTOP_SPACE_BETWEEN,slidesPerView:e?S.MOBILE_SLIDES_PER_VIEW:S.DESKTOP_SLIDES_PER_VIEW,tronscanSlidesPerView:e?S.TRONSCAN_MOBILE_SLIDES:S.TRONSCAN_DESKTOP_SLIDES,tronscanSpaceBetween:e?S.TRONSCAN_MOBILE_SPACE:S.DESKTOP_SPACE_BETWEEN,eventType:e?"touchend":"click",leftModifier:e?3:0}},e}();b._isMobile=null;var T=function(){function e(){this.handlers=new Map,this.dataManager=void 0,this.dataManager=g.getInstance()}e.getInstance=function(){return e.instance||(e.instance=new e),e.instance};var t=e.prototype;return t.on=function(e,t,i,n){this.handlers.has(e)||this.handlers.set(e,[]);var s={selector:t,event:i,handler:n};this.handlers.get(e).push(s),$(document).on(i,t,n)},t.off=function(e){var t=this.handlers.get(e);t&&(t.forEach(function(e){var t=e.selector,i=e.event,n=e.handler;$(document).off(i,t,n)}),this.handlers.delete(e))},t.offAll=function(){var e=this;this.handlers.forEach(function(t,i){e.off(i)})},t.setupButtonNavigation=function(e,t,i){var n=this,s=b.getResponsiveConfig(),r=this.dataManager.getLinksQueueService();this.on("buttonNavigation",".u-btn",s.eventType,function(i){var s=i.currentTarget,a=$(s).attr("number"),o=a?parseInt(a):0,l=document.getElementById(S.CONTAINER_IDS.ZHIBO_IFRAME);$(".App").css("min-height","100vh"),n.handleButtonClick(o,e,l,r),n.updateButtonSelection(s,t,o)})},t.handleButtonClick=function(e,t,i,n){switch(this.hideAllContainers(),e){case 0:this.showForumView(i);break;case 1:this.showZhiboView(i,n);break;case 2:this.showYouxiView(i);break;case 3:this.showShangchengView(i);break;default:this.showCustomButtonView(e,t)}},t.hideAllContainers=function(){$("."+S.CSS_CLASSES.SWIPER_TAG_CONTAINER).css("display","none"),$("."+S.CSS_CLASSES.ZHIBO_CONTAINER).css("display","none"),$("."+S.CSS_CLASSES.YOUXI_CONTAINER).css("display","none"),$("."+S.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER).css("display","none"),$("."+S.CSS_CLASSES.SHANGCHENG_CONTAINER).css("display","none")},t.showForumView=function(e){$("."+S.CSS_CLASSES.SWIPER_TAG_CONTAINER).css("display",""),$(".App").css("min-height","50vh"),e&&(e.src="")},t.showZhiboView=function(e,t){$("."+S.CSS_CLASSES.ZHIBO_CONTAINER).css("display","inline-block");var i=$("#app-navigation").outerHeight()||0,n=$(".selectTitleContainer").outerHeight()||0,s=$("#linksQueuePrev").outerHeight()||0,r=window.innerHeight-i-n-s;if(e){$("#zhiboIframe").css("height",r+"px");var a=t.getCurrentItem();if(a){var o=a.attribute("links");e.src!==o&&(e.src=o)}}},t.showYouxiView=function(e){$("."+S.CSS_CLASSES.YOUXI_CONTAINER).css("display","flex"),e&&(e.src="")},t.showShangchengView=function(e){$("."+S.CSS_CLASSES.SHANGCHENG_CONTAINER).css("display","flex"),e&&(e.src="")},t.showCustomButtonView=function(e,t){var i=t[e];if(i){var n=$("#app-navigation").outerHeight()||0,s=$(".selectTitleContainer").outerHeight()||0,r=$("#linksQueuePrev").outerHeight()||0,a=window.innerHeight-n-s-r,o=0,l="yes",d=$(".swiperTagContainer").css("height")||"0px";5==e?(a=550,d=550):6==e?(a=d=380,l="no"):7!=e&&8!=e||(o=20),$("."+S.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER).css({"padding-bottom":o+"px",height:d+"px",display:"inline-block"}),$("#"+S.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME).css({"padding-bottom":o+"px",height:a+"px"}).attr("scrolling",l);var c=document.getElementById(S.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME);c&&(c.src=i.url)}},t.updateButtonSelection=function(e,t,i){var n=$(e).outerWidth(),s=$("#"+S.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND);s.length&&n&&s.width(n),void 0!==t[i]&&s.css("left",t[i])},t.setupZhiboNavigation=function(){var e=this,t=b.getResponsiveConfig(),i=this.dataManager.getLinksQueueService(),n=document.getElementById(S.CONTAINER_IDS.ZHIBO_IFRAME);this.on("zhiboRefresh","#"+S.CONTAINER_IDS.LINKS_QUEUE_REFRESH,t.eventType,function(){n&&(n.src="",setTimeout(function(){var e=i.getCurrentItem();e&&n&&(n.src=e.attribute("links"))},S.ZHIBO_REFRESH_DELAY))}),this.on("zhiboPrev","#"+S.CONTAINER_IDS.LINKS_QUEUE_PREV,t.eventType,function(){i.decrementPointer();var t=i.getCurrentItem();t&&n&&(n.src=t.attribute("links")),e.updateZhiboButtonStates(i)}),this.on("zhiboNext","#"+S.CONTAINER_IDS.LINKS_QUEUE_NEXT,t.eventType,function(){i.incrementPointer();var t=i.getCurrentItem();t&&n&&(n.src=t.attribute("links")),e.updateZhiboButtonStates(i)})},t.updateZhiboButtonStates=function(e){$("#nextZhiBoButton").css("color",e.hasNext()?"":"#666"),$("#prevZhiBoButton").css("color",e.hasPrevious()?"":"#666")},t.setupMobileAdjustments=function(){b.getResponsiveConfig().isMobile&&($(".item-newDiscussion").find("span.Button-label").html("<div class='buttonRegister'>登录</div>"),$(".item-newDiscussion").find("span.Button-label").css({display:"block","font-size":"14px","word-spacing":"-1px"})),$(".item-newDiscussion").find("i").css("display","none"),$(".item-nav").remove(),$(".TagTiles").css("display","none")},t.setupLeaderboardPosition=function(){$(".item-MoneyLeaderboard").addClass("App-primaryControl"),$(".item-forum-checkin").parent().append($(".item-MoneyLeaderboard")),$(".item-MoneyLeaderboard").css("right","75px")},e}();function E(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function y(e,t){void 0===e&&(e={}),void 0===t&&(t={});const i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>i.indexOf(e)<0).forEach(i=>{void 0===e[i]?e[i]=t[i]:E(t[i])&&E(e[i])&&Object.keys(t[i]).length>0&&y(e[i],t[i])})}T.instance=void 0;const C={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function x(){const e="undefined"!=typeof document?document:{};return y(e,C),e}const I={document:C,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function _(){const e="undefined"!=typeof window?window:{};return y(e,I),e}function A(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function M(){return Date.now()}function N(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function O(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function L(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const n=i<0||arguments.length<=i?void 0:arguments[i];if(null!=n&&!O(n)){const i=Object.keys(Object(n)).filter(e=>t.indexOf(e)<0);for(let t=0,s=i.length;t<s;t+=1){const s=i[t],r=Object.getOwnPropertyDescriptor(n,s);void 0!==r&&r.enumerable&&(N(e[s])&&N(n[s])?n[s].__swiper__?e[s]=n[s]:L(e[s],n[s]):!N(e[s])&&N(n[s])?(e[s]={},n[s].__swiper__?e[s]=n[s]:L(e[s],n[s])):e[s]=n[s])}}}return e}function P(e,t,i){e.style.setProperty(t,i)}function k(e){let{swiper:t,targetPosition:i,side:n}=e;const s=_(),r=-t.translate;let a,o=null;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",s.cancelAnimationFrame(t.cssModeFrameID);const d=i>r?"next":"prev",c=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,p=()=>{a=(new Date).getTime(),null===o&&(o=a);const e=Math.max(Math.min((a-o)/l,1),0),d=.5-Math.cos(e*Math.PI)/2;let u=r+d*(i-r);if(c(u,i)&&(u=i),t.wrapperEl.scrollTo({[n]:u}),c(u,i))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[n]:u})}),void s.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=s.requestAnimationFrame(p)};p()}function R(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function D(e,t){void 0===t&&(t="");const i=_(),n=[...e.children];return i.HTMLSlotElement&&e instanceof HTMLSlotElement&&n.push(...e.assignedElements()),t?n.filter(e=>e.matches(t)):n}function B(e){try{return void console.warn(e)}catch(e){}}function z(e,t){void 0===t&&(t=[]);const i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter(e=>!!e.trim())}(t)),i}function G(e,t){return _().getComputedStyle(e,null).getPropertyValue(t)}function H(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function F(e,t){const i=[];let n=e.parentElement;for(;n;)t?n.matches(t)&&i.push(n):i.push(n),n=n.parentElement;return i}function V(e,t,i){const n=_();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function W(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function j(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}let U,q,X;function Y(){return U||(U=function(){const e=_(),t=x();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),U}function K(e){return void 0===e&&(e={}),q||(q=function(e){let{userAgent:t}=void 0===e?{}:e;const i=Y(),n=_(),s=n.navigator.platform,r=t||n.navigator.userAgent,a={ios:!1,android:!1},o=n.screen.width,l=n.screen.height,d=r.match(/(Android);?[\s\/]+([\d.]+)?/);let c=r.match(/(iPad).*OS\s([\d_]+)/);const p=r.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="Win32"===s;let h="MacIntel"===s;return!c&&h&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${l}`)>=0&&(c=r.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),h=!1),d&&!f&&(a.os="android",a.android=!0),(c||u||p)&&(a.os="ios",a.ios=!0),a}(e)),q}function Z(){return X||(X=function(){const e=_(),t=K();let i=!1;function n(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(n()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,n]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&n<2}}const s=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),r=n();return{isSafari:i||r,needPerspectiveFix:i,need3dFix:r||s&&t.ios,isWebView:s}}()),X}var Q={on(e,t,i){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof t)return n;const s=i?"unshift":"push";return e.split(" ").forEach(e=>{n.eventsListeners[e]||(n.eventsListeners[e]=[]),n.eventsListeners[e][s](t)}),n},once(e,t,i){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof t)return n;function s(){n.off(e,s),s.__emitterProxy&&delete s.__emitterProxy;for(var i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];t.apply(n,r)}return s.__emitterProxy=t,n.on(e,s,i)},onAny(e,t){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;const n=t?"unshift":"push";return i.eventsAnyListeners.indexOf(e)<0&&i.eventsAnyListeners[n](e),i},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const i=t.eventsAnyListeners.indexOf(e);return i>=0&&t.eventsAnyListeners.splice(i,1),t},off(e,t){const i=this;return!i.eventsListeners||i.destroyed?i:i.eventsListeners?(e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((n,s)=>{(n===t||n.__emitterProxy&&n.__emitterProxy===t)&&i.eventsListeners[e].splice(s,1)})}),i):i},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,i,n;for(var s=arguments.length,r=new Array(s),a=0;a<s;a++)r[a]=arguments[a];return"string"==typeof r[0]||Array.isArray(r[0])?(t=r[0],i=r.slice(1,r.length),n=e):(t=r[0].events,i=r[0].data,n=r[0].context||e),i.unshift(n),(Array.isArray(t)?t:t.split(" ")).forEach(t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(e=>{e.apply(n,[t,...i])}),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach(e=>{e.apply(n,i)})}),e}};const J=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},ee=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},te=(e,t)=>{if(!e||e.destroyed||!e.params)return;const i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())})),t&&t.remove()}},ie=(e,t)=>{if(!e.slides[t])return;const i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},ne=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);const n="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),s=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const i=s,r=[i-t];return r.push(...Array.from({length:t}).map((e,t)=>i+n+t)),void e.slides.forEach((t,i)=>{r.includes(t.column)&&ie(e,i)})}const r=s+n-1;if(e.params.rewind||e.params.loop)for(let n=s-t;n<=r+t;n+=1){const t=(n%i+i)%i;(t<s||t>r)&&ie(e,t)}else for(let n=Math.max(s-t,0);n<=Math.min(r+t,i-1);n+=1)n!==s&&(n>r||n<s)&&ie(e,n)};var se={updateSize:function(){const e=this;let t,i;const n=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:n.clientWidth,i=void 0!==e.params.height&&null!==e.params.height?e.params.height:n.clientHeight,0===t&&e.isHorizontal()||0===i&&e.isVertical()||(t=t-parseInt(G(n,"padding-left")||0,10)-parseInt(G(n,"padding-right")||0,10),i=i-parseInt(G(n,"padding-top")||0,10)-parseInt(G(n,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(i)&&(i=0),Object.assign(e,{width:t,height:i,size:e.isHorizontal()?t:i}))},updateSlides:function(){const e=this;function t(t,i){return parseFloat(t.getPropertyValue(e.getDirectionLabel(i))||0)}const i=e.params,{wrapperEl:n,slidesEl:s,size:r,rtlTranslate:a,wrongRTL:o}=e,l=e.virtual&&i.virtual.enabled,d=l?e.virtual.slides.length:e.slides.length,c=D(s,`.${e.params.slideClass}, swiper-slide`),p=l?e.virtual.slides.length:c.length;let u=[];const f=[],h=[];let m=i.slidesOffsetBefore;"function"==typeof m&&(m=i.slidesOffsetBefore.call(e));let v=i.slidesOffsetAfter;"function"==typeof v&&(v=i.slidesOffsetAfter.call(e));const g=e.snapGrid.length,S=e.slidesGrid.length;let w=i.spaceBetween,b=-m,T=0,E=0;if(void 0===r)return;"string"==typeof w&&w.indexOf("%")>=0?w=parseFloat(w.replace("%",""))/100*r:"string"==typeof w&&(w=parseFloat(w)),e.virtualSize=-w,c.forEach(e=>{a?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),i.centeredSlides&&i.cssMode&&(P(n,"--swiper-centered-offset-before",""),P(n,"--swiper-centered-offset-after",""));const y=i.grid&&i.grid.rows>1&&e.grid;let C;y?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();const x="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter(e=>void 0!==i.breakpoints[e].slidesPerView).length>0;for(let n=0;n<p;n+=1){let s;if(C=0,c[n]&&(s=c[n]),y&&e.grid.updateSlide(n,s,c),!c[n]||"none"!==G(s,"display")){if("auto"===i.slidesPerView){x&&(c[n].style[e.getDirectionLabel("width")]="");const r=getComputedStyle(s),a=s.style.transform,o=s.style.webkitTransform;if(a&&(s.style.transform="none"),o&&(s.style.webkitTransform="none"),i.roundLengths)C=e.isHorizontal()?V(s,"width",!0):V(s,"height",!0);else{const e=t(r,"width"),i=t(r,"padding-left"),n=t(r,"padding-right"),a=t(r,"margin-left"),o=t(r,"margin-right"),l=r.getPropertyValue("box-sizing");if(l&&"border-box"===l)C=e+a+o;else{const{clientWidth:t,offsetWidth:r}=s;C=e+i+n+a+o+(r-t)}}a&&(s.style.transform=a),o&&(s.style.webkitTransform=o),i.roundLengths&&(C=Math.floor(C))}else C=(r-(i.slidesPerView-1)*w)/i.slidesPerView,i.roundLengths&&(C=Math.floor(C)),c[n]&&(c[n].style[e.getDirectionLabel("width")]=`${C}px`);c[n]&&(c[n].swiperSlideSize=C),h.push(C),i.centeredSlides?(b=b+C/2+T/2+w,0===T&&0!==n&&(b=b-r/2-w),0===n&&(b=b-r/2-w),Math.abs(b)<.001&&(b=0),i.roundLengths&&(b=Math.floor(b)),E%i.slidesPerGroup===0&&u.push(b),f.push(b)):(i.roundLengths&&(b=Math.floor(b)),(E-Math.min(e.params.slidesPerGroupSkip,E))%e.params.slidesPerGroup===0&&u.push(b),f.push(b),b=b+C+w),e.virtualSize+=C+w,T=C,E+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,a&&o&&("slide"===i.effect||"coverflow"===i.effect)&&(n.style.width=`${e.virtualSize+w}px`),i.setWrapperSize&&(n.style[e.getDirectionLabel("width")]=`${e.virtualSize+w}px`),y&&e.grid.updateWrapperSize(C,u),!i.centeredSlides){const t=[];for(let n=0;n<u.length;n+=1){let s=u[n];i.roundLengths&&(s=Math.floor(s)),u[n]<=e.virtualSize-r&&t.push(s)}u=t,Math.floor(e.virtualSize-r)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-r)}if(l&&i.loop){const t=h[0]+w;if(i.slidesPerGroup>1){const n=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/i.slidesPerGroup),s=t*i.slidesPerGroup;for(let e=0;e<n;e+=1)u.push(u[u.length-1]+s)}for(let n=0;n<e.virtual.slidesBefore+e.virtual.slidesAfter;n+=1)1===i.slidesPerGroup&&u.push(u[u.length-1]+t),f.push(f[f.length-1]+t),e.virtualSize+=t}if(0===u.length&&(u=[0]),0!==w){const t=e.isHorizontal()&&a?"marginLeft":e.getDirectionLabel("marginRight");c.filter((e,t)=>!(i.cssMode&&!i.loop)||t!==c.length-1).forEach(e=>{e.style[t]=`${w}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let e=0;h.forEach(t=>{e+=t+(w||0)}),e-=w;const t=e>r?e-r:0;u=u.map(e=>e<=0?-m:e>t?t+v:e)}if(i.centerInsufficientSlides){let e=0;h.forEach(t=>{e+=t+(w||0)}),e-=w;const t=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(e+t<r){const i=(r-e-t)/2;u.forEach((e,t)=>{u[t]=e-i}),f.forEach((e,t)=>{f[t]=e+i})}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:f,slidesSizesGrid:h}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){P(n,"--swiper-centered-offset-before",-u[0]+"px"),P(n,"--swiper-centered-offset-after",e.size/2-h[h.length-1]/2+"px");const t=-e.snapGrid[0],i=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(e=>e+t),e.slidesGrid=e.slidesGrid.map(e=>e+i)}if(p!==d&&e.emit("slidesLengthChange"),u.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),f.length!==S&&e.emit("slidesGridLengthChange"),i.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(l||i.cssMode||"slide"!==i.effect&&"fade"!==i.effect)){const t=`${i.containerModifierClass}backface-hidden`,n=e.el.classList.contains(t);p<=i.maxBackfaceHiddenSlides?n||e.el.classList.add(t):n&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,i=[],n=t.virtual&&t.params.virtual.enabled;let s,r=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const a=e=>n?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(e=>{i.push(e)});else for(s=0;s<Math.ceil(t.params.slidesPerView);s+=1){const e=t.activeIndex+s;if(e>t.slides.length&&!n)break;i.push(a(e))}else i.push(a(t.activeIndex));for(s=0;s<i.length;s+=1)if(void 0!==i[s]){const e=i[s].offsetHeight;r=e>r?e:r}(r||0===r)&&(t.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,i=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let n=0;n<t.length;n+=1)t[n].swiperSlideOffset=(e.isHorizontal()?t[n].offsetLeft:t[n].offsetTop)-i-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,i=t.params,{slides:n,rtlTranslate:s,snapGrid:r}=t;if(0===n.length)return;void 0===n[0].swiperSlideOffset&&t.updateSlidesOffset();let a=-e;s&&(a=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=i.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(let e=0;e<n.length;e+=1){const l=n[e];let d=l.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(d-=n[0].swiperSlideOffset);const c=(a+(i.centeredSlides?t.minTranslate():0)-d)/(l.swiperSlideSize+o),p=(a-r[0]+(i.centeredSlides?t.minTranslate():0)-d)/(l.swiperSlideSize+o),u=-(a-d),f=u+t.slidesSizesGrid[e],h=u>=0&&u<=t.size-t.slidesSizesGrid[e],m=u>=0&&u<t.size-1||f>1&&f<=t.size||u<=0&&f>=t.size;m&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(e)),J(l,m,i.slideVisibleClass),J(l,h,i.slideFullyVisibleClass),l.progress=s?-c:c,l.originalProgress=s?-p:p}},updateProgress:function(e){const t=this;if(void 0===e){const i=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*i||0}const i=t.params,n=t.maxTranslate()-t.minTranslate();let{progress:s,isBeginning:r,isEnd:a,progressLoop:o}=t;const l=r,d=a;if(0===n)s=0,r=!0,a=!0;else{s=(e-t.minTranslate())/n;const i=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;r=i||s<=0,a=o||s>=1,i&&(s=0),o&&(s=1)}if(i.loop){const i=t.getSlideIndexByData(0),n=t.getSlideIndexByData(t.slides.length-1),s=t.slidesGrid[i],r=t.slidesGrid[n],a=t.slidesGrid[t.slidesGrid.length-1],l=Math.abs(e);o=l>=s?(l-s)/a:(l+a-r)/a,o>1&&(o-=1)}Object.assign(t,{progress:s,progressLoop:o,isBeginning:r,isEnd:a}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),r&&!l&&t.emit("reachBeginning toEdge"),a&&!d&&t.emit("reachEnd toEdge"),(l&&!r||d&&!a)&&t.emit("fromEdge"),t.emit("progress",s)},updateSlidesClasses:function(){const e=this,{slides:t,params:i,slidesEl:n,activeIndex:s}=e,r=e.virtual&&i.virtual.enabled,a=e.grid&&i.grid&&i.grid.rows>1,o=e=>D(n,`.${i.slideClass}${e}, swiper-slide${e}`)[0];let l,d,c;if(r)if(i.loop){let t=s-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),l=o(`[data-swiper-slide-index="${t}"]`)}else l=o(`[data-swiper-slide-index="${s}"]`);else a?(l=t.find(e=>e.column===s),c=t.find(e=>e.column===s+1),d=t.find(e=>e.column===s-1)):l=t[s];l&&(a||(c=function(e,t){const i=[];for(;e.nextElementSibling;){const n=e.nextElementSibling;t?n.matches(t)&&i.push(n):i.push(n),e=n}return i}(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!c&&(c=t[0]),d=function(e,t){const i=[];for(;e.previousElementSibling;){const n=e.previousElementSibling;t?n.matches(t)&&i.push(n):i.push(n),e=n}return i}(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&0===!d&&(d=t[t.length-1]))),t.forEach(e=>{ee(e,e===l,i.slideActiveClass),ee(e,e===c,i.slideNextClass),ee(e,e===d,i.slidePrevClass)}),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,i=t.rtlTranslate?t.translate:-t.translate,{snapGrid:n,params:s,activeIndex:r,realIndex:a,snapIndex:o}=t;let l,d=e;const c=e=>{let i=e-t.virtual.slidesBefore;return i<0&&(i=t.virtual.slides.length+i),i>=t.virtual.slides.length&&(i-=t.virtual.slides.length),i};if(void 0===d&&(d=function(e){const{slidesGrid:t,params:i}=e,n=e.rtlTranslate?e.translate:-e.translate;let s;for(let e=0;e<t.length;e+=1)void 0!==t[e+1]?n>=t[e]&&n<t[e+1]-(t[e+1]-t[e])/2?s=e:n>=t[e]&&n<t[e+1]&&(s=e+1):n>=t[e]&&(s=e);return i.normalizeSlideIndex&&(s<0||void 0===s)&&(s=0),s}(t)),n.indexOf(i)>=0)l=n.indexOf(i);else{const e=Math.min(s.slidesPerGroupSkip,d);l=e+Math.floor((d-e)/s.slidesPerGroup)}if(l>=n.length&&(l=n.length-1),d===r&&!t.params.loop)return void(l!==o&&(t.snapIndex=l,t.emit("snapIndexChange")));if(d===r&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=c(d));const p=t.grid&&s.grid&&s.grid.rows>1;let u;if(t.virtual&&s.virtual.enabled&&s.loop)u=c(d);else if(p){const e=t.slides.find(e=>e.column===d);let i=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(i)&&(i=Math.max(t.slides.indexOf(e),0)),u=Math.floor(i/s.grid.rows)}else if(t.slides[d]){const e=t.slides[d].getAttribute("data-swiper-slide-index");u=e?parseInt(e,10):d}else u=d;Object.assign(t,{previousSnapIndex:o,snapIndex:l,previousRealIndex:a,realIndex:u,previousIndex:r,activeIndex:d}),t.initialized&&ne(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(a!==u&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const i=this,n=i.params;let s=e.closest(`.${n.slideClass}, swiper-slide`);!s&&i.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!s&&e.matches&&e.matches(`.${n.slideClass}, swiper-slide`)&&(s=e)});let r,a=!1;if(s)for(let e=0;e<i.slides.length;e+=1)if(i.slides[e]===s){a=!0,r=e;break}if(!s||!a)return i.clickedSlide=void 0,void(i.clickedIndex=void 0);i.clickedSlide=s,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(s.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=r,n.slideToClickedSlide&&void 0!==i.clickedIndex&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}};function re(e){let{swiper:t,runCallbacks:i,direction:n,step:s}=e;const{activeIndex:r,previousIndex:a}=t;let o=n;o||(o=r>a?"next":r<a?"prev":"reset"),t.emit(`transition${s}`),i&&"reset"===o?t.emit(`slideResetTransition${s}`):i&&r!==a&&(t.emit(`slideChangeTransition${s}`),"next"===o?t.emit(`slideNextTransition${s}`):t.emit(`slidePrevTransition${s}`))}var ae={slideTo:function(e,t,i,n,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));const r=this;let a=e;a<0&&(a=0);const{params:o,snapGrid:l,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:u,wrapperEl:f,enabled:h}=r;if(!h&&!n&&!s||r.destroyed||r.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=r.params.speed);const m=Math.min(r.params.slidesPerGroupSkip,a);let v=m+Math.floor((a-m)/r.params.slidesPerGroup);v>=l.length&&(v=l.length-1);const g=-l[v];if(o.normalizeSlideIndex)for(let e=0;e<d.length;e+=1){const t=-Math.floor(100*g),i=Math.floor(100*d[e]),n=Math.floor(100*d[e+1]);void 0!==d[e+1]?t>=i&&t<n-(n-i)/2?a=e:t>=i&&t<n&&(a=e+1):t>=i&&(a=e)}if(r.initialized&&a!==p){if(!r.allowSlideNext&&(u?g>r.translate&&g>r.minTranslate():g<r.translate&&g<r.minTranslate()))return!1;if(!r.allowSlidePrev&&g>r.translate&&g>r.maxTranslate()&&(p||0)!==a)return!1}let S;a!==(c||0)&&i&&r.emit("beforeSlideChangeStart"),r.updateProgress(g),S=a>p?"next":a<p?"prev":"reset";const w=r.virtual&&r.params.virtual.enabled;if((!w||!s)&&(u&&-g===r.translate||!u&&g===r.translate))return r.updateActiveIndex(a),o.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==o.effect&&r.setTranslate(g),"reset"!==S&&(r.transitionStart(i,S),r.transitionEnd(i,S)),!1;if(o.cssMode){const e=r.isHorizontal(),i=u?g:-g;if(0===t)w&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),w&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{f[e?"scrollLeft":"scrollTop"]=i})):f[e?"scrollLeft":"scrollTop"]=i,w&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return k({swiper:r,targetPosition:i,side:e?"left":"top"}),!0;f.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}const b=Z().isSafari;return w&&!s&&b&&r.isElement&&r.virtual.update(!1,!1,a),r.setTransition(t),r.setTranslate(g),r.updateActiveIndex(a),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,n),r.transitionStart(i,S),0===t?r.transitionEnd(i,S):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(i,S))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,n){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));const s=this;if(s.destroyed)return;void 0===t&&(t=s.params.speed);const r=s.grid&&s.params.grid&&s.params.grid.rows>1;let a=e;if(s.params.loop)if(s.virtual&&s.params.virtual.enabled)a+=s.virtual.slidesBefore;else{let e;if(r){const t=a*s.params.grid.rows;e=s.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else e=s.getSlideIndexByData(a);const t=r?Math.ceil(s.slides.length/s.params.grid.rows):s.slides.length,{centeredSlides:i}=s.params;let o=s.params.slidesPerView;"auto"===o?o=s.slidesPerViewDynamic():(o=Math.ceil(parseFloat(s.params.slidesPerView,10)),i&&o%2==0&&(o+=1));let l=t-e<o;if(i&&(l=l||e<Math.ceil(o/2)),n&&i&&"auto"!==s.params.slidesPerView&&!r&&(l=!1),l){const n=i?e<s.activeIndex?"prev":"next":e-s.activeIndex-1<s.params.slidesPerView?"next":"prev";s.loopFix({direction:n,slideTo:!0,activeSlideIndex:"next"===n?e+1:e-t+1,slideRealIndex:"next"===n?s.realIndex:void 0})}if(r){const e=a*s.params.grid.rows;a=s.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else a=s.getSlideIndexByData(a)}return requestAnimationFrame(()=>{s.slideTo(a,t,i,n)}),s},slideNext:function(e,t,i){void 0===t&&(t=!0);const n=this,{enabled:s,params:r,animating:a}=n;if(!s||n.destroyed)return n;void 0===e&&(e=n.params.speed);let o=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(o=Math.max(n.slidesPerViewDynamic("current",!0),1));const l=n.activeIndex<r.slidesPerGroupSkip?1:o,d=n.virtual&&r.virtual.enabled;if(r.loop){if(a&&!d&&r.loopPreventsSliding)return!1;if(n.loopFix({direction:"next"}),n._clientLeft=n.wrapperEl.clientLeft,n.activeIndex===n.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{n.slideTo(n.activeIndex+l,e,t,i)}),!0}return r.rewind&&n.isEnd?n.slideTo(0,e,t,i):n.slideTo(n.activeIndex+l,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);const n=this,{params:s,snapGrid:r,slidesGrid:a,rtlTranslate:o,enabled:l,animating:d}=n;if(!l||n.destroyed)return n;void 0===e&&(e=n.params.speed);const c=n.virtual&&s.virtual.enabled;if(s.loop){if(d&&!c&&s.loopPreventsSliding)return!1;n.loopFix({direction:"prev"}),n._clientLeft=n.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=p(o?n.translate:-n.translate),f=r.map(e=>p(e)),h=s.freeMode&&s.freeMode.enabled;let m=r[f.indexOf(u)-1];if(void 0===m&&(s.cssMode||h)){let e;r.forEach((t,i)=>{u>=t&&(e=i)}),void 0!==e&&(m=h?r[e]:r[e>0?e-1:e])}let v=0;if(void 0!==m&&(v=a.indexOf(m),v<0&&(v=n.activeIndex-1),"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(v=v-n.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),s.rewind&&n.isBeginning){const s=n.params.virtual&&n.params.virtual.enabled&&n.virtual?n.virtual.slides.length-1:n.slides.length-1;return n.slideTo(s,e,t,i)}return s.loop&&0===n.activeIndex&&s.cssMode?(requestAnimationFrame(()=>{n.slideTo(v,e,t,i)}),!0):n.slideTo(v,e,t,i)},slideReset:function(e,t,i){void 0===t&&(t=!0);const n=this;if(!n.destroyed)return void 0===e&&(e=n.params.speed),n.slideTo(n.activeIndex,e,t,i)},slideToClosest:function(e,t,i,n){void 0===t&&(t=!0),void 0===n&&(n=.5);const s=this;if(s.destroyed)return;void 0===e&&(e=s.params.speed);let r=s.activeIndex;const a=Math.min(s.params.slidesPerGroupSkip,r),o=a+Math.floor((r-a)/s.params.slidesPerGroup),l=s.rtlTranslate?s.translate:-s.translate;if(l>=s.snapGrid[o]){const e=s.snapGrid[o];l-e>(s.snapGrid[o+1]-e)*n&&(r+=s.params.slidesPerGroup)}else{const e=s.snapGrid[o-1];l-e<=(s.snapGrid[o]-e)*n&&(r-=s.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,s.slidesGrid.length-1),s.slideTo(r,e,t,i)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:i}=e,n="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let s,r=e.getSlideIndexWhenGrid(e.clickedIndex);const a=e.isElement?"swiper-slide":`.${t.slideClass}`,o=e.grid&&e.params.grid&&e.params.grid.rows>1;if(t.loop){if(e.animating)return;s=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?e.slideToLoop(s):r>(o?(e.slides.length-n)/2-(e.params.grid.rows-1):e.slides.length-n)?(e.loopFix(),r=e.getSlideIndex(D(i,`${a}[data-swiper-slide-index="${s}"]`)[0]),A(()=>{e.slideTo(r)})):e.slideTo(r)}else e.slideTo(r)}},oe={loopCreate:function(e,t){const i=this,{params:n,slidesEl:s}=i;if(!n.loop||i.virtual&&i.params.virtual.enabled)return;const r=()=>{D(s,`.${n.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)})},a=i.grid&&n.grid&&n.grid.rows>1;n.loopAddBlankSlides&&(n.slidesPerGroup>1||a)&&(()=>{const e=D(s,`.${n.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(i.recalcSlides(),i.updateSlides())})();const o=n.slidesPerGroup*(a?n.grid.rows:1),l=i.slides.length%o!==0,d=a&&i.slides.length%n.grid.rows!==0,c=e=>{for(let t=0;t<e;t+=1){const e=i.isElement?z("swiper-slide",[n.slideBlankClass]):z("div",[n.slideClass,n.slideBlankClass]);i.slidesEl.append(e)}};l?(n.loopAddBlankSlides?(c(o-i.slides.length%o),i.recalcSlides(),i.updateSlides()):B("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),r()):d?(n.loopAddBlankSlides?(c(n.grid.rows-i.slides.length%n.grid.rows),i.recalcSlides(),i.updateSlides()):B("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),r()):r(),i.loopFix({slideRealIndex:e,direction:n.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:n,setTranslate:s,activeSlideIndex:r,initial:a,byController:o,byMousewheel:l}=void 0===e?{}:e;const d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");const{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:f,params:h}=d,{centeredSlides:m,initialSlide:v}=h;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&h.virtual.enabled)return i&&(h.centeredSlides||0!==d.snapIndex?h.centeredSlides&&d.snapIndex<h.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,void d.emit("loopFix");let g=h.slidesPerView;"auto"===g?g=d.slidesPerViewDynamic():(g=Math.ceil(parseFloat(h.slidesPerView,10)),m&&g%2==0&&(g+=1));const S=h.slidesPerGroupAuto?g:h.slidesPerGroup;let w=m?Math.max(S,Math.ceil(g/2)):S;w%S!==0&&(w+=S-w%S),w+=h.loopAdditionalSlides,d.loopedSlides=w;const b=d.grid&&h.grid&&h.grid.rows>1;c.length<g+w||"cards"===d.params.effect&&c.length<g+2*w?B("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):b&&"row"===h.grid.fill&&B("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const T=[],E=[],y=b?Math.ceil(c.length/h.grid.rows):c.length,C=a&&y-v<g&&!m;let x=C?v:d.activeIndex;void 0===r?r=d.getSlideIndex(c.find(e=>e.classList.contains(h.slideActiveClass))):x=r;const I="next"===n||!n,_="prev"===n||!n;let A=0,M=0;const N=(b?c[r].column:r)+(m&&void 0===s?-g/2+.5:0);if(N<w){A=Math.max(w-N,S);for(let e=0;e<w-N;e+=1){const t=e-Math.floor(e/y)*y;if(b){const e=y-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&T.push(t)}else T.push(y-t-1)}}else if(N+g>y-w){M=Math.max(N-(y-2*w),S),C&&(M=Math.max(M,g-y+v+1));for(let e=0;e<M;e+=1){const t=e-Math.floor(e/y)*y;b?c.forEach((e,i)=>{e.column===t&&E.push(i)}):E.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),"cards"===d.params.effect&&c.length<g+2*w&&(E.includes(r)&&E.splice(E.indexOf(r),1),T.includes(r)&&T.splice(T.indexOf(r),1)),_&&T.forEach(e=>{c[e].swiperLoopMoveDOM=!0,f.prepend(c[e]),c[e].swiperLoopMoveDOM=!1}),I&&E.forEach(e=>{c[e].swiperLoopMoveDOM=!0,f.append(c[e]),c[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===h.slidesPerView?d.updateSlides():b&&(T.length>0&&_||E.length>0&&I)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),h.watchSlidesProgress&&d.updateSlidesOffset(),i)if(T.length>0&&_){if(void 0===t){const e=d.slidesGrid[x],t=d.slidesGrid[x+A]-e;l?d.setTranslate(d.translate-t):(d.slideTo(x+Math.ceil(A),0,!1,!0),s&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(s){const e=b?T.length/h.grid.rows:T.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(E.length>0&&I)if(void 0===t){const e=d.slidesGrid[x],t=d.slidesGrid[x-M]-e;l?d.setTranslate(d.translate-t):(d.slideTo(x-M,0,!1,!0),s&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{const e=b?E.length/h.grid.rows:E.length;d.slideTo(d.activeIndex-e,0,!1,!0)}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!o){const e={slideRealIndex:t,direction:n,setTranslate:s,activeSlideIndex:r,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===h.slidesPerView&&i})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===h.slidesPerView&&i})}d.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:i}=e;if(!t.loop||!i||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const n=[];e.slides.forEach(e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;n[t]=e}),e.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),n.forEach(e=>{i.append(e)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}};function le(e,t,i){const n=_(),{params:s}=e,r=s.edgeSwipeDetection,a=s.edgeSwipeThreshold;return!r||!(i<=a||i>=n.innerWidth-a)||"prevent"===r&&(t.preventDefault(),!0)}function de(e){const t=this,i=x();let n=e;n.originalEvent&&(n=n.originalEvent);const s=t.touchEventsData;if("pointerdown"===n.type){if(null!==s.pointerId&&s.pointerId!==n.pointerId)return;s.pointerId=n.pointerId}else"touchstart"===n.type&&1===n.targetTouches.length&&(s.touchId=n.targetTouches[0].identifier);if("touchstart"===n.type)return void le(t,n,n.targetTouches[0].pageX);const{params:r,touches:a,enabled:o}=t;if(!o)return;if(!r.simulateTouch&&"mouse"===n.pointerType)return;if(t.animating&&r.preventInteractionOnTransition)return;!t.animating&&r.cssMode&&r.loop&&t.loopFix();let l=n.target;if("wrapper"===r.touchEventsTarget&&!function(e,t){const i=_();let n=t.contains(e);return!n&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&(n=[...t.assignedElements()].includes(e),n||(n=function(e,t){const i=[t];for(;i.length>0;){const t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t))),n}(l,t.wrapperEl))return;if("which"in n&&3===n.which)return;if("button"in n&&n.button>0)return;if(s.isTouched&&s.isMoved)return;const d=!!r.noSwipingClass&&""!==r.noSwipingClass,c=n.composedPath?n.composedPath():n.path;d&&n.target&&n.target.shadowRoot&&c&&(l=c[0]);const p=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,u=!(!n.target||!n.target.shadowRoot);if(r.noSwiping&&(u?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===x()||i===_())return null;i.assignedSlot&&(i=i.assignedSlot);const n=i.closest(e);return n||i.getRootNode?n||t(i.getRootNode().host):null}(t)}(p,l):l.closest(p)))return void(t.allowClick=!0);if(r.swipeHandler&&!l.closest(r.swipeHandler))return;a.currentX=n.pageX,a.currentY=n.pageY;const f=a.currentX,h=a.currentY;if(!le(t,n,f))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=f,a.startY=h,s.touchStartTime=M(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let m=!0;l.matches(s.focusableElements)&&(m=!1,"SELECT"===l.nodeName&&(s.isTouched=!1)),i.activeElement&&i.activeElement.matches(s.focusableElements)&&i.activeElement!==l&&("mouse"===n.pointerType||"mouse"!==n.pointerType&&!l.matches(s.focusableElements))&&i.activeElement.blur();const v=m&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!v||l.isContentEditable||n.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",n)}function ce(e){const t=x(),i=this,n=i.touchEventsData,{params:s,touches:r,rtlTranslate:a,enabled:o}=i;if(!o)return;if(!s.simulateTouch&&"mouse"===e.pointerType)return;let l,d=e;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type){if(null!==n.touchId)return;if(d.pointerId!==n.pointerId)return}if("touchmove"===d.type){if(l=[...d.changedTouches].find(e=>e.identifier===n.touchId),!l||l.identifier!==n.touchId)return}else l=d;if(!n.isTouched)return void(n.startMoving&&n.isScrolling&&i.emit("touchMoveOpposite",d));const c=l.pageX,p=l.pageY;if(d.preventedByNestedSwiper)return r.startX=c,void(r.startY=p);if(!i.allowTouchMove)return d.target.matches(n.focusableElements)||(i.allowClick=!1),void(n.isTouched&&(Object.assign(r,{startX:c,startY:p,currentX:c,currentY:p}),n.touchStartTime=M()));if(s.touchReleaseOnEdges&&!s.loop)if(i.isVertical()){if(p<r.startY&&i.translate<=i.maxTranslate()||p>r.startY&&i.translate>=i.minTranslate())return n.isTouched=!1,void(n.isMoved=!1)}else{if(a&&(c>r.startX&&-i.translate<=i.maxTranslate()||c<r.startX&&-i.translate>=i.minTranslate()))return;if(!a&&(c<r.startX&&i.translate<=i.maxTranslate()||c>r.startX&&i.translate>=i.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(n.focusableElements)&&t.activeElement!==d.target&&"mouse"!==d.pointerType&&t.activeElement.blur(),t.activeElement&&d.target===t.activeElement&&d.target.matches(n.focusableElements))return n.isMoved=!0,void(i.allowClick=!1);n.allowTouchCallbacks&&i.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=c,r.currentY=p;const u=r.currentX-r.startX,f=r.currentY-r.startY;if(i.params.threshold&&Math.sqrt(u**2+f**2)<i.params.threshold)return;if(void 0===n.isScrolling){let e;i.isHorizontal()&&r.currentY===r.startY||i.isVertical()&&r.currentX===r.startX?n.isScrolling=!1:u*u+f*f>=25&&(e=180*Math.atan2(Math.abs(f),Math.abs(u))/Math.PI,n.isScrolling=i.isHorizontal()?e>s.touchAngle:90-e>s.touchAngle)}if(n.isScrolling&&i.emit("touchMoveOpposite",d),void 0===n.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(n.startMoving=!0)),n.isScrolling||"touchmove"===d.type&&n.preventTouchMoveFromPointerMove)return void(n.isTouched=!1);if(!n.startMoving)return;i.allowClick=!1,!s.cssMode&&d.cancelable&&d.preventDefault(),s.touchMoveStopPropagation&&!s.nested&&d.stopPropagation();let h=i.isHorizontal()?u:f,m=i.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;s.oneWayMovement&&(h=Math.abs(h)*(a?1:-1),m=Math.abs(m)*(a?1:-1)),r.diff=h,h*=s.touchRatio,a&&(h=-h,m=-m);const v=i.touchesDirection;i.swipeDirection=h>0?"prev":"next",i.touchesDirection=m>0?"prev":"next";const g=i.params.loop&&!s.cssMode,S="next"===i.touchesDirection&&i.allowSlideNext||"prev"===i.touchesDirection&&i.allowSlidePrev;if(!n.isMoved){if(g&&S&&i.loopFix({direction:i.swipeDirection}),n.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});i.wrapperEl.dispatchEvent(e)}n.allowMomentumBounce=!1,!s.grabCursor||!0!==i.allowSlideNext&&!0!==i.allowSlidePrev||i.setGrabCursor(!0),i.emit("sliderFirstMove",d)}if((new Date).getTime(),!1!==s._loopSwapReset&&n.isMoved&&n.allowThresholdMove&&v!==i.touchesDirection&&g&&S&&Math.abs(h)>=1)return Object.assign(r,{startX:c,startY:p,currentX:c,currentY:p,startTranslate:n.currentTranslate}),n.loopSwapReset=!0,void(n.startTranslate=n.currentTranslate);i.emit("sliderMove",d),n.isMoved=!0,n.currentTranslate=h+n.startTranslate;let w=!0,b=s.resistanceRatio;if(s.touchReleaseOnEdges&&(b=0),h>0?(g&&S&&n.allowThresholdMove&&n.currentTranslate>(s.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]-("auto"!==s.slidesPerView&&i.slides.length-s.slidesPerView>=2?i.slidesSizesGrid[i.activeIndex+1]+i.params.spaceBetween:0)-i.params.spaceBetween:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),n.currentTranslate>i.minTranslate()&&(w=!1,s.resistance&&(n.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+n.startTranslate+h)**b))):h<0&&(g&&S&&n.allowThresholdMove&&n.currentTranslate<(s.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween+("auto"!==s.slidesPerView&&i.slides.length-s.slidesPerView>=2?i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween:0):i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-("auto"===s.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(s.slidesPerView,10)))}),n.currentTranslate<i.maxTranslate()&&(w=!1,s.resistance&&(n.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-n.startTranslate-h)**b))),w&&(d.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),i.allowSlidePrev||i.allowSlideNext||(n.currentTranslate=n.startTranslate),s.threshold>0){if(!(Math.abs(h)>s.threshold||n.allowThresholdMove))return void(n.currentTranslate=n.startTranslate);if(!n.allowThresholdMove)return n.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,n.currentTranslate=n.startTranslate,void(r.diff=i.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}s.followFinger&&!s.cssMode&&((s.freeMode&&s.freeMode.enabled&&i.freeMode||s.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),s.freeMode&&s.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(n.currentTranslate),i.setTranslate(n.currentTranslate))}function pe(e){const t=this,i=t.touchEventsData;let n,s=e;if(s.originalEvent&&(s=s.originalEvent),"touchend"===s.type||"touchcancel"===s.type){if(n=[...s.changedTouches].find(e=>e.identifier===i.touchId),!n||n.identifier!==i.touchId)return}else{if(null!==i.touchId)return;if(s.pointerId!==i.pointerId)return;n=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&(!["pointercancel","contextmenu"].includes(s.type)||!t.browser.isSafari&&!t.browser.isWebView))return;i.pointerId=null,i.touchId=null;const{params:r,touches:a,rtlTranslate:o,slidesGrid:l,enabled:d}=t;if(!d)return;if(!r.simulateTouch&&"mouse"===s.pointerType)return;if(i.allowTouchCallbacks&&t.emit("touchEnd",s),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&r.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);r.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const c=M(),p=c-i.touchStartTime;if(t.allowClick){const e=s.path||s.composedPath&&s.composedPath();t.updateClickedSlide(e&&e[0]||s.target,e),t.emit("tap click",s),p<300&&c-i.lastClickTime<300&&t.emit("doubleTap doubleClick",s)}if(i.lastClickTime=M(),A(()=>{t.destroyed||(t.allowClick=!0)}),!i.isTouched||!i.isMoved||!t.swipeDirection||0===a.diff&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);let u;if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,u=r.followFinger?o?t.translate:-t.translate:-i.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:u});const f=u>=-t.maxTranslate()&&!t.params.loop;let h=0,m=t.slidesSizesGrid[0];for(let e=0;e<l.length;e+=e<r.slidesPerGroupSkip?1:r.slidesPerGroup){const t=e<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==l[e+t]?(f||u>=l[e]&&u<l[e+t])&&(h=e,m=l[e+t]-l[e]):(f||u>=l[e])&&(h=e,m=l[l.length-1]-l[l.length-2])}let v=null,g=null;r.rewind&&(t.isBeginning?g=r.virtual&&r.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(v=0));const S=(u-l[h])/m,w=h<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(p>r.longSwipesMs){if(!r.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(S>=r.longSwipesRatio?t.slideTo(r.rewind&&t.isEnd?v:h+w):t.slideTo(h)),"prev"===t.swipeDirection&&(S>1-r.longSwipesRatio?t.slideTo(h+w):null!==g&&S<0&&Math.abs(S)>r.longSwipesRatio?t.slideTo(g):t.slideTo(h))}else{if(!r.shortSwipes)return void t.slideTo(t.activeIndex);!t.navigation||s.target!==t.navigation.nextEl&&s.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(null!==v?v:h+w),"prev"===t.swipeDirection&&t.slideTo(null!==g?g:h)):s.target===t.navigation.nextEl?t.slideTo(h+w):t.slideTo(h)}}function ue(){const e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:n,allowSlidePrev:s,snapGrid:r}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=a&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=s,e.allowSlideNext=n,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function fe(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function he(){const e=this,{wrapperEl:t,rtlTranslate:i,enabled:n}=e;if(!n)return;let s;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const r=e.maxTranslate()-e.minTranslate();s=0===r?0:(e.translate-e.minTranslate())/r,s!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function me(e){const t=this;te(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function ve(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const ge=(e,t)=>{const i=x(),{params:n,el:s,wrapperEl:r,device:a}=e,o=!!n.nested,l="on"===t?"addEventListener":"removeEventListener",d=t;s&&"string"!=typeof s&&(i[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),s[l]("touchstart",e.onTouchStart,{passive:!1}),s[l]("pointerdown",e.onTouchStart,{passive:!1}),i[l]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[l]("touchend",e.onTouchEnd,{passive:!0}),i[l]("pointerup",e.onTouchEnd,{passive:!0}),i[l]("pointercancel",e.onTouchEnd,{passive:!0}),i[l]("touchcancel",e.onTouchEnd,{passive:!0}),i[l]("pointerout",e.onTouchEnd,{passive:!0}),i[l]("pointerleave",e.onTouchEnd,{passive:!0}),i[l]("contextmenu",e.onTouchEnd,{passive:!0}),(n.preventClicks||n.preventClicksPropagation)&&s[l]("click",e.onClick,!0),n.cssMode&&r[l]("scroll",e.onScroll),n.updateOnWindowResize?e[d](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",ue,!0):e[d]("observerUpdate",ue,!0),s[l]("load",e.onLoad,{capture:!0}))},Se=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var we={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function be(e,t){return function(i){void 0===i&&(i={});const n=Object.keys(i)[0],s=i[n];"object"==typeof s&&null!==s?(!0===e[n]&&(e[n]={enabled:!0}),"navigation"===n&&e[n]&&e[n].enabled&&!e[n].prevEl&&!e[n].nextEl&&(e[n].auto=!0),["pagination","scrollbar"].indexOf(n)>=0&&e[n]&&e[n].enabled&&!e[n].el&&(e[n].auto=!0),n in e&&"enabled"in s?("object"!=typeof e[n]||"enabled"in e[n]||(e[n].enabled=!0),e[n]||(e[n]={enabled:!1}),L(t,i)):L(t,i)):L(t,i)}}const Te={eventsEmitter:Q,update:se,translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:i,translate:n,wrapperEl:s}=this;if(t.virtualTranslate)return i?-n:n;if(t.cssMode)return n;let r=function(e,t){void 0===t&&(t="x");const i=_();let n,s,r;const a=function(e){const t=_();let i;return t.getComputedStyle&&(i=t.getComputedStyle(e,null)),!i&&e.currentStyle&&(i=e.currentStyle),i||(i=e.style),i}(e);return i.WebKitCSSMatrix?(s=a.transform||a.webkitTransform,s.split(",").length>6&&(s=s.split(", ").map(e=>e.replace(",",".")).join(", ")),r=new i.WebKitCSSMatrix("none"===s?"":s)):(r=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),n=r.toString().split(",")),"x"===t&&(s=i.WebKitCSSMatrix?r.m41:16===n.length?parseFloat(n[12]):parseFloat(n[4])),"y"===t&&(s=i.WebKitCSSMatrix?r.m42:16===n.length?parseFloat(n[13]):parseFloat(n[5])),s||0}(s,e);return r+=this.cssOverflowAdjustment(),i&&(r=-r),r||0},setTranslate:function(e,t){const i=this,{rtlTranslate:n,params:s,wrapperEl:r,progress:a}=i;let o,l=0,d=0;i.isHorizontal()?l=n?-e:e:d=e,s.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:d,s.cssMode?r[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-l:-d:s.virtualTranslate||(i.isHorizontal()?l-=i.cssOverflowAdjustment():d-=i.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${d}px, 0px)`);const c=i.maxTranslate()-i.minTranslate();o=0===c?0:(e-i.minTranslate())/c,o!==a&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,n,s){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===n&&(n=!0);const r=this,{params:a,wrapperEl:o}=r;if(r.animating&&a.preventInteractionOnTransition)return!1;const l=r.minTranslate(),d=r.maxTranslate();let c;if(c=n&&e>l?l:n&&e<d?d:e,r.updateProgress(c),a.cssMode){const e=r.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return k({swiper:r,targetPosition:-c,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(c),i&&(r.emit("beforeTransitionStart",t,s),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),i&&(r.emit("beforeTransitionStart",t,s),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,i&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${e}ms`,i.wrapperEl.style.transitionDelay=0===e?"0ms":""),i.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const i=this,{params:n}=i;n.cssMode||(n.autoHeight&&i.updateAutoHeight(),re({swiper:i,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const i=this,{params:n}=i;i.animating=!1,n.cssMode||(i.setTransition(0),re({swiper:i,runCallbacks:e,direction:t,step:"End"}))}},slide:ae,loop:oe,grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=de.bind(e),e.onTouchMove=ce.bind(e),e.onTouchEnd=pe.bind(e),e.onDocumentTouchStart=ve.bind(e),t.cssMode&&(e.onScroll=he.bind(e)),e.onClick=fe.bind(e),e.onLoad=me.bind(e),ge(e,"on")},detachEvents:function(){ge(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{realIndex:t,initialized:i,params:n,el:s}=e,r=n.breakpoints;if(!r||r&&0===Object.keys(r).length)return;const a=x(),o="window"!==n.breakpointsBase&&n.breakpointsBase?"container":n.breakpointsBase,l=["window","container"].includes(n.breakpointsBase)||!n.breakpointsBase?e.el:a.querySelector(n.breakpointsBase),d=e.getBreakpoint(r,o,l);if(!d||e.currentBreakpoint===d)return;const c=(d in r?r[d]:void 0)||e.originalParams,p=Se(e,n),u=Se(e,c),f=e.params.grabCursor,h=c.grabCursor,m=n.enabled;p&&!u?(s.classList.remove(`${n.containerModifierClass}grid`,`${n.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&u&&(s.classList.add(`${n.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===n.grid.fill)&&s.classList.add(`${n.containerModifierClass}grid-column`),e.emitContainerClasses()),f&&!h?e.unsetGrabCursor():!f&&h&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===c[t])return;const i=n[t]&&n[t].enabled,s=c[t]&&c[t].enabled;i&&!s&&e[t].disable(),!i&&s&&e[t].enable()});const v=c.direction&&c.direction!==n.direction,g=n.loop&&(c.slidesPerView!==n.slidesPerView||v),S=n.loop;v&&i&&e.changeDirection(),L(e.params,c);const w=e.params.enabled,b=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),m&&!w?e.disable():!m&&w&&e.enable(),e.currentBreakpoint=d,e.emit("_beforeBreakpoint",c),i&&(g?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!S&&b?(e.loopCreate(t),e.updateSlides()):S&&!b&&e.loopDestroy()),e.emit("breakpoint",c)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let n=!1;const s=_(),r="window"===t?s.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:r*t,point:e}}return{value:e,point:e}});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){const{point:r,value:o}=a[e];"window"===t?s.matchMedia(`(min-width: ${o}px)`).matches&&(n=r):o<=i.clientWidth&&(n=r)}return n||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:i}=e,{slidesOffsetBefore:n}=i;if(n){const t=e.slides.length-1,i=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*n;e.isLocked=e.size>i}else e.isLocked=1===e.snapGrid.length;!0===i.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===i.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:i,rtl:n,el:s,device:r}=e,a=function(e,t){const i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(n=>{e[n]&&i.push(t+n)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",i.direction,{"free-mode":e.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:n},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&"column"===i.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);t.push(...a),s.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},Ee={};class ye{constructor(){let e,t;for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?t=n[0]:[e,t]=n,t||(t={}),t=L({},t),e&&!t.el&&(t.el=e);const r=x();if(t.el&&"string"==typeof t.el&&r.querySelectorAll(t.el).length>1){const e=[];return r.querySelectorAll(t.el).forEach(i=>{const n=L({},t,{el:i});e.push(new ye(n))}),e}const a=this;a.__swiper__=!0,a.support=Y(),a.device=K({userAgent:t.userAgent}),a.browser=Z(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const o={};a.modules.forEach(e=>{e({params:t,swiper:a,extendParams:be(t,o),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const l=L({},we,o);return a.params=L({},l,Ee,t),a.originalParams=L({},a.params),a.passedParams=L({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(e=>{a.on(e,a.params.on[e])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===a.params.direction,isVertical:()=>"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:i}=this,n=H(D(t,`.${i.slideClass}, swiper-slide`)[0]);return H(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=D(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const n=i.minTranslate(),s=(i.maxTranslate()-n)*e+n;i.translateTo(s,void 0===t?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(i=>{const n=e.getSlideClasses(i);t.push({slideEl:i,classNames:n}),e.emit("_slideClass",i,n)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:i,slides:n,slidesGrid:s,slidesSizesGrid:r,size:a,activeIndex:o}=this;let l=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=n[o]?Math.ceil(n[o].swiperSlideSize):0;for(let i=o+1;i<n.length;i+=1)n[i]&&!e&&(t+=Math.ceil(n[i].swiperSlideSize),l+=1,t>a&&(e=!0));for(let i=o-1;i>=0;i-=1)n[i]&&!e&&(t+=n[i].swiperSlideSize,l+=1,t>a&&(e=!0))}else if("current"===e)for(let e=o+1;e<n.length;e+=1)(t?s[e]+r[e]-s[o]<a:s[e]-s[o]<a)&&(l+=1);else for(let e=o-1;e>=0;e-=1)s[o]-s[e]<a&&(l+=1);return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;function n(){const t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}let s;if(i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(t=>{t.complete&&te(e,t)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)n(),i.autoHeight&&e.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const t=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;s=e.slideTo(t.length-1,0,!1,!0)}else s=e.slideTo(e.activeIndex,0,!1,!0);s||n()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const i=this,n=i.params.direction;return e||(e="horizontal"===n?"vertical":"horizontal"),e===n||"horizontal"!==e&&"vertical"!==e||(i.el.classList.remove(`${i.params.containerModifierClass}${n}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const n=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let s=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(n()):D(i,n())[0];return!s&&t.params.createElements&&(s=z("div",t.params.wrapperClass),i.append(s),D(i,`.${t.params.slideClass}`).forEach(e=>{s.append(e)})),Object.assign(t,{el:i,wrapperEl:s,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:s,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===G(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===G(i,"direction")),wrongRTL:"-webkit-box"===G(s,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?te(t,e):e.addEventListener("load",e=>{te(t,e.target)})}),ne(t),t.initialized=!0,ne(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const i=this,{params:n,el:s,wrapperEl:r,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),n.loop&&i.loopDestroy(),t&&(i.removeClasses(),s&&"string"!=typeof s&&s.removeAttribute("style"),r&&r.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),function(e){const t=e;Object.keys(t).forEach(e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}})}(i)),i.destroyed=!0),null}static extendDefaults(e){L(Ee,e)}static get extendedDefaults(){return Ee}static get defaults(){return we}static installModule(e){ye.prototype.__modules__||(ye.prototype.__modules__=[]);const t=ye.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(e=>ye.installModule(e)),ye):(ye.installModule(e),ye)}}function Ce(e,t,i,n){return e.params.createElements&&Object.keys(n).forEach(s=>{if(!i[s]&&!0===i.auto){let r=D(e.el,`.${n[s]}`)[0];r||(r=z("div",n[s]),r.className=n[s],e.el.append(r)),i[s]=r,t[s]=r}}),i}function xe(e){let{swiper:t,extendParams:i,on:n,emit:s}=e;function r(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e),i)?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i?e:i)}function a(e,i){const n=t.params.navigation;(e=W(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...n.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](n.lockClass))})}function o(){const{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop)return a(i,!1),void a(e,!1);a(i,t.isBeginning&&!t.params.rewind),a(e,t.isEnd&&!t.params.rewind)}function l(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),s("navigationPrev"))}function d(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),s("navigationNext"))}function c(){const e=t.params.navigation;if(t.params.navigation=Ce(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!e.nextEl&&!e.prevEl)return;let i=r(e.nextEl),n=r(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:n}),i=W(i),n=W(n);const s=(i,n)=>{i&&i.addEventListener("click","next"===n?d:l),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>s(e,"next")),n.forEach(e=>s(e,"prev"))}function p(){let{nextEl:e,prevEl:i}=t.navigation;e=W(e),i=W(i);const n=(e,i)=>{e.removeEventListener("click","next"===i?d:l),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>n(e,"next")),i.forEach(e=>n(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},n("init",()=>{!1===t.params.navigation.enabled?u():(c(),o())}),n("toEdge fromEdge lock unlock",()=>{o()}),n("destroy",()=>{p()}),n("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;e=W(e),i=W(i),t.enabled?o():[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),n("click",(e,i)=>{let{nextEl:n,prevEl:r}=t.navigation;n=W(n),r=W(r);const a=i.target;let o=r.includes(a)||n.includes(a);if(t.isElement&&!o){const e=i.path||i.composedPath&&i.composedPath();e&&(o=e.find(e=>n.includes(e)||r.includes(e)))}if(t.params.navigation.hideOnClick&&!o){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===a||t.pagination.el.contains(a)))return;let e;n.length?e=n[0].classList.contains(t.params.navigation.hiddenClass):r.length&&(e=r[0].classList.contains(t.params.navigation.hiddenClass)),s(!0===e?"navigationShow":"navigationHide"),[...n,...r].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});const u=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),o()},disable:u,update:o,init:c,destroy:p})}function Ie(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function _e(e){let{swiper:t,extendParams:i,on:n,emit:s}=e;const r="swiper-pagination";let a;i({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),t.pagination={el:null,bullets:[]};let o=0;function l(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&0===t.pagination.el.length}function d(e,i){const{bulletActiveClass:n}=t.params.pagination;e&&(e=e[("prev"===i?"previous":"next")+"ElementSibling"])&&(e.classList.add(`${n}-${i}`),(e=e[("prev"===i?"previous":"next")+"ElementSibling"])&&e.classList.add(`${n}-${i}-${i}`))}function c(e){const i=e.target.closest(Ie(t.params.pagination.bulletClass));if(!i)return;e.preventDefault();const n=H(i)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===n)return;const e=(s=t.realIndex,r=n,(r%=a=t.slides.length)===1+(s%=a)?"next":r===s-1?"previous":void 0);"next"===e?t.slideNext():"previous"===e?t.slidePrev():t.slideToLoop(n)}else t.slideTo(n);var s,r,a}function p(){const e=t.rtl,i=t.params.pagination;if(l())return;let n,r,c=t.pagination.el;c=W(c);const p=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,u=t.params.loop?Math.ceil(p/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(r=t.previousRealIndex||0,n=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):void 0!==t.snapIndex?(n=t.snapIndex,r=t.previousSnapIndex):(r=t.previousIndex||0,n=t.activeIndex||0),"bullets"===i.type&&t.pagination.bullets&&t.pagination.bullets.length>0){const s=t.pagination.bullets;let l,p,u;if(i.dynamicBullets&&(a=V(s[0],t.isHorizontal()?"width":"height",!0),c.forEach(e=>{e.style[t.isHorizontal()?"width":"height"]=a*(i.dynamicMainBullets+4)+"px"}),i.dynamicMainBullets>1&&void 0!==r&&(o+=n-(r||0),o>i.dynamicMainBullets-1?o=i.dynamicMainBullets-1:o<0&&(o=0)),l=Math.max(n-o,0),p=l+(Math.min(s.length,i.dynamicMainBullets)-1),u=(p+l)/2),s.forEach(e=>{const t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${i.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),c.length>1)s.forEach(e=>{const s=H(e);s===n?e.classList.add(...i.bulletActiveClass.split(" ")):t.isElement&&e.setAttribute("part","bullet"),i.dynamicBullets&&(s>=l&&s<=p&&e.classList.add(...`${i.bulletActiveClass}-main`.split(" ")),s===l&&d(e,"prev"),s===p&&d(e,"next"))});else{const e=s[n];if(e&&e.classList.add(...i.bulletActiveClass.split(" ")),t.isElement&&s.forEach((e,t)=>{e.setAttribute("part",t===n?"bullet-active":"bullet")}),i.dynamicBullets){const e=s[l],t=s[p];for(let e=l;e<=p;e+=1)s[e]&&s[e].classList.add(...`${i.bulletActiveClass}-main`.split(" "));d(e,"prev"),d(t,"next")}}if(i.dynamicBullets){const n=Math.min(s.length,i.dynamicMainBullets+4),r=(a*n-a)/2-u*a,o=e?"right":"left";s.forEach(e=>{e.style[t.isHorizontal()?o:"top"]=`${r}px`})}}c.forEach((e,r)=>{if("fraction"===i.type&&(e.querySelectorAll(Ie(i.currentClass)).forEach(e=>{e.textContent=i.formatFractionCurrent(n+1)}),e.querySelectorAll(Ie(i.totalClass)).forEach(e=>{e.textContent=i.formatFractionTotal(u)})),"progressbar"===i.type){let s;s=i.progressbarOpposite?t.isHorizontal()?"vertical":"horizontal":t.isHorizontal()?"horizontal":"vertical";const r=(n+1)/u;let a=1,o=1;"horizontal"===s?a=r:o=r,e.querySelectorAll(Ie(i.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${a}) scaleY(${o})`,e.style.transitionDuration=`${t.params.speed}ms`})}"custom"===i.type&&i.renderCustom?(j(e,i.renderCustom(t,n+1,u)),0===r&&s("paginationRender",e)):(0===r&&s("paginationRender",e),s("paginationUpdate",e)),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](i.lockClass)})}function u(){const e=t.params.pagination;if(l())return;const i=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let n=t.pagination.el;n=W(n);let r="";if("bullets"===e.type){let n=t.params.loop?Math.ceil(i/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&n>i&&(n=i);for(let i=0;i<n;i+=1)e.renderBullet?r+=e.renderBullet.call(t,i,e.bulletClass):r+=`<${e.bulletElement} ${t.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(r=e.renderFraction?e.renderFraction.call(t,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(r=e.renderProgressbar?e.renderProgressbar.call(t,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),t.pagination.bullets=[],n.forEach(i=>{"custom"!==e.type&&j(i,r||""),"bullets"===e.type&&t.pagination.bullets.push(...i.querySelectorAll(Ie(e.bulletClass)))}),"custom"!==e.type&&s("paginationRender",n[0])}function f(){t.params.pagination=Ce(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const e=t.params.pagination;if(!e.el)return;let i;"string"==typeof e.el&&t.isElement&&(i=t.el.querySelector(e.el)),i||"string"!=typeof e.el||(i=[...document.querySelectorAll(e.el)]),i||(i=e.el),i&&0!==i.length&&(t.params.uniqueNavElements&&"string"==typeof e.el&&Array.isArray(i)&&i.length>1&&(i=[...t.el.querySelectorAll(e.el)],i.length>1&&(i=i.find(e=>F(e,".swiper")[0]===t.el))),Array.isArray(i)&&1===i.length&&(i=i[0]),Object.assign(t.pagination,{el:i}),i=W(i),i.forEach(i=>{"bullets"===e.type&&e.clickable&&i.classList.add(...(e.clickableClass||"").split(" ")),i.classList.add(e.modifierClass+e.type),i.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(i.classList.add(`${e.modifierClass}${e.type}-dynamic`),o=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&i.classList.add(e.progressbarOppositeClass),e.clickable&&i.addEventListener("click",c),t.enabled||i.classList.add(e.lockClass)}))}function h(){const e=t.params.pagination;if(l())return;let i=t.pagination.el;i&&(i=W(i),i.forEach(i=>{i.classList.remove(e.hiddenClass),i.classList.remove(e.modifierClass+e.type),i.classList.remove(t.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(i.classList.remove(...(e.clickableClass||"").split(" ")),i.removeEventListener("click",c))})),t.pagination.bullets&&t.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}n("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const e=t.params.pagination;let{el:i}=t.pagination;i=W(i),i.forEach(i=>{i.classList.remove(e.horizontalClass,e.verticalClass),i.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass)})}),n("init",()=>{!1===t.params.pagination.enabled?m():(f(),u(),p())}),n("activeIndexChange",()=>{void 0===t.snapIndex&&p()}),n("snapIndexChange",()=>{p()}),n("snapGridLengthChange",()=>{u(),p()}),n("destroy",()=>{h()}),n("enable disable",()=>{let{el:e}=t.pagination;e&&(e=W(e),e.forEach(e=>e.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),n("lock unlock",()=>{p()}),n("click",(e,i)=>{const n=i.target,r=W(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&r&&r.length>0&&!n.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&n===t.navigation.nextEl||t.navigation.prevEl&&n===t.navigation.prevEl))return;const e=r[0].classList.contains(t.params.pagination.hiddenClass);s(!0===e?"paginationShow":"paginationHide"),r.forEach(e=>e.classList.toggle(t.params.pagination.hiddenClass))}});const m=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=W(e),e.forEach(e=>e.classList.add(t.params.pagination.paginationDisabledClass))),h()};Object.assign(t.pagination,{enable:()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=W(e),e.forEach(e=>e.classList.remove(t.params.pagination.paginationDisabledClass))),f(),u(),p()},disable:m,render:u,update:p,init:f,destroy:h})}function Ae(e){let t,i,{swiper:n,extendParams:s,on:r,emit:a,params:o}=e;n.autoplay={running:!1,paused:!1,timeLeft:0},s({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,d,c,p,u,f,h,m,v=o&&o.autoplay?o.autoplay.delay:3e3,g=o&&o.autoplay?o.autoplay.delay:3e3,S=(new Date).getTime();function w(e){n&&!n.destroyed&&n.wrapperEl&&e.target===n.wrapperEl&&(n.wrapperEl.removeEventListener("transitionend",w),m||e.detail&&e.detail.bySwiperTouchMove||I())}const b=()=>{if(n.destroyed||!n.autoplay.running)return;n.autoplay.paused?d=!0:d&&(g=l,d=!1);const e=n.autoplay.paused?l:S+g-(new Date).getTime();n.autoplay.timeLeft=e,a("autoplayTimeLeft",e,e/v),i=requestAnimationFrame(()=>{b()})},T=e=>{if(n.destroyed||!n.autoplay.running)return;cancelAnimationFrame(i),b();let s=void 0===e?n.params.autoplay.delay:e;v=n.params.autoplay.delay,g=n.params.autoplay.delay;const r=(()=>{let e;if(e=n.virtual&&n.params.virtual.enabled?n.slides.find(e=>e.classList.contains("swiper-slide-active")):n.slides[n.activeIndex],e)return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(r)&&r>0&&void 0===e&&(s=r,v=r,g=r),l=s;const o=n.params.speed,d=()=>{n&&!n.destroyed&&(n.params.autoplay.reverseDirection?!n.isBeginning||n.params.loop||n.params.rewind?(n.slidePrev(o,!0,!0),a("autoplay")):n.params.autoplay.stopOnLastSlide||(n.slideTo(n.slides.length-1,o,!0,!0),a("autoplay")):!n.isEnd||n.params.loop||n.params.rewind?(n.slideNext(o,!0,!0),a("autoplay")):n.params.autoplay.stopOnLastSlide||(n.slideTo(0,o,!0,!0),a("autoplay")),n.params.cssMode&&(S=(new Date).getTime(),requestAnimationFrame(()=>{T()})))};return s>0?(clearTimeout(t),t=setTimeout(()=>{d()},s)):requestAnimationFrame(()=>{d()}),s},E=()=>{S=(new Date).getTime(),n.autoplay.running=!0,T(),a("autoplayStart")},y=()=>{n.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(i),a("autoplayStop")},C=(e,i)=>{if(n.destroyed||!n.autoplay.running)return;clearTimeout(t),e||(h=!0);const s=()=>{a("autoplayPause"),n.params.autoplay.waitForTransition?n.wrapperEl.addEventListener("transitionend",w):I()};if(n.autoplay.paused=!0,i)return f&&(l=n.params.autoplay.delay),f=!1,void s();const r=l||n.params.autoplay.delay;l=r-((new Date).getTime()-S),n.isEnd&&l<0&&!n.params.loop||(l<0&&(l=0),s())},I=()=>{n.isEnd&&l<0&&!n.params.loop||n.destroyed||!n.autoplay.running||(S=(new Date).getTime(),h?(h=!1,T(l)):T(),n.autoplay.paused=!1,a("autoplayResume"))},_=()=>{if(n.destroyed||!n.autoplay.running)return;const e=x();"hidden"===e.visibilityState&&(h=!0,C(!0)),"visible"===e.visibilityState&&I()},A=e=>{"mouse"===e.pointerType&&(h=!0,m=!0,n.animating||n.autoplay.paused||C(!0))},M=e=>{"mouse"===e.pointerType&&(m=!1,n.autoplay.paused&&I())};r("init",()=>{n.params.autoplay.enabled&&(n.params.autoplay.pauseOnMouseEnter&&(n.el.addEventListener("pointerenter",A),n.el.addEventListener("pointerleave",M)),x().addEventListener("visibilitychange",_),E())}),r("destroy",()=>{n.el&&"string"!=typeof n.el&&(n.el.removeEventListener("pointerenter",A),n.el.removeEventListener("pointerleave",M)),x().removeEventListener("visibilitychange",_),n.autoplay.running&&y()}),r("_freeModeStaticRelease",()=>{(p||h)&&I()}),r("_freeModeNoMomentumRelease",()=>{n.params.autoplay.disableOnInteraction?y():C(!0,!0)}),r("beforeTransitionStart",(e,t,i)=>{!n.destroyed&&n.autoplay.running&&(i||!n.params.autoplay.disableOnInteraction?C(!0,!0):y())}),r("sliderFirstMove",()=>{!n.destroyed&&n.autoplay.running&&(n.params.autoplay.disableOnInteraction?y():(c=!0,p=!1,h=!1,u=setTimeout(()=>{h=!0,p=!0,C(!0)},200)))}),r("touchEnd",()=>{if(!n.destroyed&&n.autoplay.running&&c){if(clearTimeout(u),clearTimeout(t),n.params.autoplay.disableOnInteraction)return p=!1,void(c=!1);p&&n.params.cssMode&&I(),p=!1,c=!1}}),r("slideChange",()=>{!n.destroyed&&n.autoplay.running&&(f=!0)}),Object.assign(n.autoplay,{start:E,stop:y,pause:C,resume:I})}function Me(e,t){const i=R(t);return i!==t&&(i.style.backfaceVisibility="hidden",i.style["-webkit-backface-visibility"]="hidden"),i}function Ne(e,t,i){const n=`swiper-slide-shadow${i?`-${i}`:""}${e?` swiper-slide-shadow-${e}`:""}`,s=R(t);let r=s.querySelector(`.${n.split(" ").join(".")}`);return r||(r=z("div",n.split(" ")),s.append(r)),r}function Oe(e){let{swiper:t,extendParams:i,on:n}=e;i({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),function(e){const{effect:t,swiper:i,on:n,setTranslate:s,setTransition:r,overwriteParams:a,perspective:o,recreateShadows:l,getEffectParams:d}=e;let c;n("beforeInit",()=>{if(i.params.effect!==t)return;i.classNames.push(`${i.params.containerModifierClass}${t}`),o&&o()&&i.classNames.push(`${i.params.containerModifierClass}3d`);const e=a?a():{};Object.assign(i.params,e),Object.assign(i.originalParams,e)}),n("setTranslate _virtualUpdated",()=>{i.params.effect===t&&s()}),n("setTransition",(e,n)=>{i.params.effect===t&&r(n)}),n("transitionEnd",()=>{if(i.params.effect===t&&l){if(!d||!d().slideShadows)return;i.slides.forEach(e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(e=>e.remove())}),l()}}),n("virtualUpdate",()=>{i.params.effect===t&&(i.slides.length||(c=!0),requestAnimationFrame(()=>{c&&i.slides&&i.slides.length&&(s(),c=!1)}))})}({effect:"coverflow",swiper:t,on:n,setTranslate:()=>{const{width:e,height:i,slides:n,slidesSizesGrid:s}=t,r=t.params.coverflowEffect,a=t.isHorizontal(),o=t.translate,l=a?e/2-o:i/2-o,d=a?r.rotate:-r.rotate,c=r.depth,p=function(e){return t=>Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90==0?t+.001:t}(t);for(let e=0,t=n.length;e<t;e+=1){const t=n[e],i=s[e],o=(l-t.swiperSlideOffset-i/2)/i,u="function"==typeof r.modifier?r.modifier(o):o*r.modifier;let f=a?d*u:0,h=a?0:d*u,m=-c*Math.abs(u),v=r.stretch;"string"==typeof v&&-1!==v.indexOf("%")&&(v=parseFloat(r.stretch)/100*i);let g=a?0:v*u,S=a?v*u:0,w=1-(1-r.scale)*Math.abs(u);Math.abs(S)<.001&&(S=0),Math.abs(g)<.001&&(g=0),Math.abs(m)<.001&&(m=0),Math.abs(f)<.001&&(f=0),Math.abs(h)<.001&&(h=0),Math.abs(w)<.001&&(w=0);const b=`translate3d(${S}px,${g}px,${m}px)  rotateX(${p(h)}deg) rotateY(${p(f)}deg) scale(${w})`;if(Me(0,t).style.transform=b,t.style.zIndex=1-Math.abs(Math.round(u)),r.slideShadows){let e=a?t.querySelector(".swiper-slide-shadow-left"):t.querySelector(".swiper-slide-shadow-top"),i=a?t.querySelector(".swiper-slide-shadow-right"):t.querySelector(".swiper-slide-shadow-bottom");e||(e=Ne("coverflow",t,a?"left":"top")),i||(i=Ne("coverflow",t,a?"right":"bottom")),e&&(e.style.opacity=u>0?u:0),i&&(i.style.opacity=-u>0?-u:0)}}},setTransition:e=>{t.slides.map(e=>R(e)).forEach(t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(t=>{t.style.transitionDuration=`${e}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}Object.keys(Te).forEach(e=>{Object.keys(Te[e]).forEach(t=>{ye.prototype[t]=Te[e][t]})}),ye.use([function(e){let{swiper:t,on:i,emit:n}=e;const s=_();let r=null,a=null;const o=()=>{t&&!t.destroyed&&t.initialized&&(n("beforeResize"),n("resize"))},l=()=>{t&&!t.destroyed&&t.initialized&&n("orientationchange")};i("init",()=>{t.params.resizeObserver&&void 0!==s.ResizeObserver?t&&!t.destroyed&&t.initialized&&(r=new ResizeObserver(e=>{a=s.requestAnimationFrame(()=>{const{width:i,height:n}=t;let s=i,r=n;e.forEach(e=>{let{contentBoxSize:i,contentRect:n,target:a}=e;a&&a!==t.el||(s=n?n.width:(i[0]||i).inlineSize,r=n?n.height:(i[0]||i).blockSize)}),s===i&&r===n||o()})}),r.observe(t.el)):(s.addEventListener("resize",o),s.addEventListener("orientationchange",l))}),i("destroy",()=>{a&&s.cancelAnimationFrame(a),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null),s.removeEventListener("resize",o),s.removeEventListener("orientationchange",l)})},function(e){let{swiper:t,extendParams:i,on:n,emit:s}=e;const r=[],a=_(),o=function(e,i){void 0===i&&(i={});const n=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void s("observerUpdate",e[0]);const i=function(){s("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});n.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),r.push(n)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),n("init",()=>{if(t.params.observer){if(t.params.observeParents){const e=F(t.hostEl);for(let t=0;t<e.length;t+=1)o(e[t])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}}),n("destroy",()=>{r.forEach(e=>{e.disconnect()}),r.splice(0,r.length)})}]);var Le=function(){function e(){}return e.createTagSwiperConfig=function(){var e=b.getResponsiveConfig();return{loop:!0,spaceBetween:e.spaceBetween,slidesPerView:e.slidesPerView,autoplay:{delay:3e3,disableOnInteraction:!1},modules:[Ae]}},e.createTronscanSwiperConfig=function(){var e=b.getResponsiveConfig();return{loop:!0,spaceBetween:e.tronscanSpaceBetween,slidesPerView:e.tronscanSlidesPerView,modules:[]}},e.createAdSwiperConfig=function(){return{autoplay:{delay:w.getTransitionTime(),disableOnInteraction:!1},loop:!0,spaceBetween:30,effect:"coverflow",centeredSlides:!0,slidesPerView:2,coverflowEffect:{rotate:0,depth:100,modifier:1,slideShadows:!0,stretch:0},pagination:{el:".swiper-pagination",type:"bullets"},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},modules:[Oe,xe,_e,Ae]}},e}(),Pe=function(){function e(){}return e.create=function(e,t,i){var n=new ye(e,t),s=i||e;return this.instances.set(s,n),n},e.getInstance=function(e){return this.instances.get(e)},e.destroy=function(e){var t=this.instances.get(e);t&&(t.destroy(),this.instances.delete(e))},e.destroyAll=function(){this.instances.forEach(function(e,t){e.destroy()}),this.instances.clear()},e.createTagSwiper=function(){var e=Le.createTagSwiperConfig();return this.create(".tagSwiper",e,"tagSwiper")},e.createTronscanSwiper=function(){var e=Le.createTronscanSwiperConfig();return this.create(".tronscanSwiper",e,"tronscanSwiper")},e.createAdSwiper=function(){var e=Le.createAdSwiperConfig();return this.create(".adSwiper",e,"adSwiper")},e.updateSwiper=function(e){var t=this.getInstance(e);t&&t.update()},e.slideTo=function(e,t,i){var n=this.getInstance(e);n&&n.slideTo(t,i)},e.startAutoplay=function(e){var t=this.getInstance(e);t&&t.autoplay&&t.autoplay.start()},e.stopAutoplay=function(e){var t=this.getInstance(e);t&&t.autoplay&&t.autoplay.stop()},e.getAllInstances=function(){return new Map(this.instances)},e.hasInstance=function(e){return this.instances.has(e)},e}();Pe.instances=new Map;var ke=function(){function e(){}return e.createContainer=function(e){var t=document.createElement("div");return t.className=e.className,e.id&&(t.id=e.id),e.height&&(t.style.height="number"==typeof e.height?e.height+"px":e.height),t},e.createSwiperContainer=function(e){var t=document.createElement("div");return t.className=e,t},e.createSwiperWrapper=function(e){var t=document.createElement("div");return t.className="swiper-wrapper",e&&(t.id=e),t},e.createSwiperSlide=function(e,t){var i=document.createElement("div");return i.className=e,i.innerHTML=t,i},e.createSwiperNavigation=function(e){var t=document.createElement("div");return t.className=e,t},e.createButton=function(e,t,i,n){return'<button id="'+e+'" number="'+t+'" type="button" class="u-btn">\n                    <i class="'+i+'"></i>\n                    <div class="u-btn-text">'+n+"</div>\n                </button>"},e.createIframe=function(e,t,i){void 0===i&&(i="");var n=document.createElement("iframe");return n.id=e,n.name="contentOnly",n.className=t,n.src=i,n},e}(),Re=function(){function e(){}return e.create=function(){var e=b.getResponsiveConfig(),t=2*($(window).width()||0)-50,i=ke.createContainer({className:S.CSS_CLASSES.SWIPER_AD_CONTAINER,id:S.CONTAINER_IDS.SWIPER_AD_CONTAINER});e.isMobile&&(i.style.width=t+"px",i.style.marginLeft=-.254*t+"px");var n=ke.createSwiperContainer(S.CSS_CLASSES.AD_SWIPER),s=ke.createSwiperWrapper();w.getImageLinkPairs().forEach(function(e){var t=ke.createSwiperSlide("swiper-slide","<img onclick='window.location.href=\""+e.link+"\"' src='"+e.src+"' />");s.appendChild(t)});var r=ke.createSwiperNavigation("swiper-button-next"),a=ke.createSwiperNavigation("swiper-button-prev"),o=ke.createSwiperNavigation("swiper-pagination");return n.appendChild(s),n.appendChild(r),n.appendChild(a),n.appendChild(o),i.appendChild(n),i},e}(),De=function(){function e(){}return e.create=function(){var e=b.getResponsiveConfig(),t=$(".TagTile"),i=ke.createContainer({className:S.CSS_CLASSES.SWIPER_TAG_CONTAINER,id:S.CONTAINER_IDS.SWIPER_TAG_CONTAINER}),n=ke.createSwiperContainer(S.CSS_CLASSES.TAG_SWIPER),s=ke.createContainer({className:"TagTextOuterContainer"});i.appendChild(s),s.appendChild(n);var r=ke.createSwiperWrapper(S.CONTAINER_IDS.SWIPER_TAG_CONTAINER.replace("Container","Wrapper"));n.appendChild(r);for(var a=0;a<t.length;a++){var o=t[a],l=$(o).find("a").attr("href")||"",d=$(o).css("background")||"",c=$(o).find(".TagTile-name").text()||"",p=$(o).find(".TagTile-name").css("color")||"",u="<a href='"+l+"'>\n                <div class='"+(e.isMobile?S.CSS_CLASSES.SWIPER_SLIDE_TAG_INNER_MOBILE:S.CSS_CLASSES.SWIPER_SLIDE_TAG_INNER)+"'\n                     style='background:"+d+";background-size: cover;background-position: center;background-repeat: no-repeat;'>\n                    <div style='font-weight:bold;font-size:14px;color:"+p+"'>"+c+"</div>\n                </div>\n            </a>",f=ke.createSwiperSlide(S.CSS_CLASSES.SWIPER_SLIDE_TAG,u);r.appendChild(f)}return i},e.addTextContent=function(e){var t=e.querySelector(".TagTextOuterContainer");t&&($(t).prepend("<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>"),$(t).append('\n                <div style="text-align:center;padding-top: 10px;">\n                    <button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;">\n                        <div style="margin-top: 5px;" class="Button-label">\n                            <img onClick="window.open(\'https://kick.com/wangming886\', \'_blank\')" style="width: 32px;" src="https://mutluresim.com/images/2023/04/10/KcgSG.png">\n                            <img onClick="window.open(\'https://m.facebook.com\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcF6i.png">\n                            <img onClick="window.open(\'https://twitter.com/youngron131_\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcDas.png">\n                            <img onClick="window.open(\'https://m.youtube.com/@ag8888\',\'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcQjd.png">\n                            <img onClick="window.open(\'https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcBAL.png">\n                        </div>\n                    </button>\n                </div>\n            '))},e}(),Be=function(){function e(){}return e.create=function(e){var t=ke.createContainer({className:"TronscanTextContainer",id:S.CONTAINER_IDS.TRONSCAN_TEXT_CONTAINER});t.innerHTML="<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度";var i=ke.createSwiperContainer(S.CSS_CLASSES.TRONSCAN_SWIPER),n=ke.createSwiperWrapper();e.forEach(function(e){var t=parseInt(e.valueUsd())+" USD",i="\n                <div style='width:100px;height:130px;border-radius: 12px;background: url("+e.img()+");;background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'>\n                    <div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div>\n                    <div class='tronscanMask'>\n                        <div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'>\n                            <span>"+t+"</span>\n                        </div>\n                    </div>\n                </div>\n            ",s=ke.createSwiperSlide(S.CSS_CLASSES.SWIPER_SLIDE_TAG,i);n.appendChild(s)}),i.appendChild(n);var s=ke.createContainer({className:"tronscanSwiperContainer"});return s.appendChild(t),s.appendChild(i),s},e}(),ze=function(){function e(){}return e.create=function(e){var t=ke.createContainer({className:S.CSS_CLASSES.SELECT_TITLE_CONTAINER,id:S.CONTAINER_IDS.SELECT_TITLE_CONTAINER}),i="",n=S.DEFAULT_BUTTON_COUNT;e.forEach(function(e){n++,i+=ke.createButton("client1HeaderButton"+n,n.toString(),e.icon(),e.name())});var s=ke.createContainer({className:"selectTitle"}),r=$(".TagsPage-content").width()||0,a="\n            "+ke.createButton("client1HeaderButton0","0","fas fa-paw","论坛")+"\n            "+ke.createButton("client1HeaderButton1","1","fab fa-twitch","直播")+"\n            "+ke.createButton("client1HeaderButton2","2","fas fa-dice","游戏")+"\n            "+ke.createButton("client1HeaderButton3","3","fas fa-gifts","商城")+"\n        ";return s.innerHTML='\n            <div class="switch-btns" style="max-width:'+r+'px">\n                <div class="btns-container">\n                    '+a+"\n                    "+i+'\n                    <div id="'+S.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND+'" class="selected-bg" style="left: 0px; top: 0px; opacity: 1;"></div>\n                </div>\n            </div>\n        ',t.appendChild(s),t},e}(),Ge=function(){function e(){}return e.createZhiboContainer=function(){var e=b.getResponsiveConfig(),t=ke.createContainer({className:S.CSS_CLASSES.ZHIBO_CONTAINER,height:$(".swiperTagContainer").css("height")||""}),i=ke.createIframe(S.CONTAINER_IDS.ZHIBO_IFRAME,"zhiboIframe");return t.innerHTML="\n            <div id='"+S.CONTAINER_IDS.LINKS_QUEUE_REFRESH+"' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: "+(e.isMobile?0:-6)+"px;'>\n                \n            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>\n                <div class='switch-btns'>\n                    <div class='btns-container'>\n                        <button type='button' class='u-btn'>\n                            <div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div>\n                        </button>\n                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>\n                    </div>\n                </div>\n            </div>\n        \n            </div>\n            <div class='zhiboSubContainer'>\n                <div id='"+S.CONTAINER_IDS.LINKS_QUEUE_PREV+"' style='display:inline-block;scale:0.8'>\n                    \n            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>\n                <div class='switch-btns'>\n                    <div class='btns-container'>\n                        <button type='button' class='u-btn'>\n                            <div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div>\n                        </button>\n                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>\n                    </div>\n                </div>\n            </div>\n        \n                </div>\n                <div id='"+S.CONTAINER_IDS.LINKS_QUEUE_NEXT+"' style='display:inline-block;scale:0.8'>\n                    \n            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>\n                <div class='switch-btns'>\n                    <div class='btns-container'>\n                        <button type='button' class='u-btn'>\n                            <div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div>\n                        </button>\n                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>\n                    </div>\n                </div>\n            </div>\n        \n                </div>\n            </div>\n        ",t.appendChild(i),t},e.createYouXiContainer=function(){var e=ke.createContainer({className:S.CSS_CLASSES.YOUXI_CONTAINER,height:$(".swiperTagContainer").css("height")||""});return e.innerText=a().translator.trans("wusong8899-client1.forum.under-construction"),e},e.createShangChengContainer=function(){var e=ke.createContainer({className:S.CSS_CLASSES.SHANGCHENG_CONTAINER,height:$(".swiperTagContainer").css("height")||""});return e.innerText=a().translator.trans("wusong8899-client1.forum.under-construction"),e},e.createButtonCustomizationContainer=function(){var e=ke.createContainer({className:S.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER,height:$(".swiperTagContainer").css("height")||""}),t=ke.createIframe(S.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME,"customButtonIframe");return e.appendChild(t),e},e.createHeaderIcon=function(){var e=ke.createContainer({className:"headerIconContainer",id:S.CONTAINER_IDS.HEADER_ICON});return e.style.display="inline-block",e.style.marginTop="8px",e.innerHTML='<img src="https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png" style="height: 24px;" />',e},e}(),$e=function(){function e(){this.dataManager=void 0,this.eventManager=void 0,this.appState=void 0,this.isInitialized=!1,this.dataManager=g.getInstance(),this.eventManager=T.getInstance(),this.appState={currentView:"forum",selectedButton:0,isMobile:b.isMobile(),isInitialized:!1}}e.getInstance=function(){return e.instance||(e.instance=new e),e.instance};var i=e.prototype;return i.initialize=function(){var e=t(s().mark(function e(){var t;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isInitialized){e.next=1;break}return e.abrupt("return");case 1:e.prev=1,this.setupHeaderExtension(),this.isInitialized=!0,this.appState.isInitialized=!0,e.next=3;break;case 2:throw e.prev=2,t=e.catch(1),console.error("Failed to initialize AppController:",t),t;case 3:case"end":return e.stop()}},e,this,[[1,2]])}));return function(){return e.apply(this,arguments)}}(),i.isInitialized=function(){return this.isInitialized},i.setupHeaderExtension=function(){var e=this;(0,l.extend)(c().prototype,"view",function(t){"tags"===a().current.get("routeName")&&e.attachAdvertiseHeader(t)})},i.attachAdvertiseHeader=function(e){var t=this;this.eventManager.setupMobileAdjustments();var i=setInterval(function(){e.dom&&(clearInterval(i),t.initializeMainComponents(e))},S.CHECK_TIME)},i.initializeMainComponents=function(){var e=t(s().mark(function e(t){var i,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!document.getElementById(S.CONTAINER_IDS.SWIPER_AD_CONTAINER)){e.next=1;break}return e.abrupt("return");case 1:return i=Re.create(),$(S.SELECTORS.CONTENT_CONTAINER).prepend(i),Pe.createAdSwiper(),e.next=2,this.dataManager.loadAllData();case 2:this.waitForDataAndInitialize(),e.next=4;break;case 3:e.prev=3,n=e.catch(0),console.error("Error initializing main components:",n);case 4:case"end":return e.stop()}},e,this,[[0,3]])}));return function(t){return e.apply(this,arguments)}}(),i.waitForDataAndInitialize=function(){var e=this,t=setInterval(function(){e.dataManager.isAllDataLoaded()&&(clearInterval(t),e.initializeDataDependentComponents())},S.DATA_CHECK_INTERVAL)},i.initializeDataDependentComponents=function(){try{if(document.getElementById(S.CONTAINER_IDS.SWIPER_TAG_CONTAINER))return;this.createCategoryLayout(),this.createContainers(),this.createButtonNavigation(),this.setupEventHandlers(),this.setupMiscellaneousFeatures()}catch(e){console.error("Error initializing data-dependent components:",e)}},i.createCategoryLayout=function(){var e=De.create();$(S.SELECTORS.TAGS_PAGE_CONTENT).prepend(e),De.addTextContent(e),Pe.createTagSwiper(),this.addTronscanComponent(e),$(S.SELECTORS.TAG_TILES).remove(),this.applyMobileStyles()},i.addTronscanComponent=function(e){var t=this.dataManager.getTronscanService().getData();if(t&&t.length>0){var i=Be.create(t);$(e).append(i),Pe.createTronscanSwiper()}},i.createContainers=function(){var e=$(S.SELECTORS.TAGS_PAGE_CONTENT),t=Ge.createZhiboContainer(),i=Ge.createYouXiContainer(),n=Ge.createButtonCustomizationContainer(),s=Ge.createShangChengContainer();e.prepend(t),e.prepend(i),e.prepend(n),e.prepend(s)},i.createButtonNavigation=function(){var e=this.dataManager.getButtonsCustomizationService().getData()||[],t=ze.create(e);$(S.SELECTORS.TAGS_PAGE_CONTENT).prepend(t)},i.setupEventHandlers=function(){var e=this.dataManager.getButtonsCustomizationService().getData()||[],t=this.calculateButtonMappings(e),i=t.buttonsCustomizationMap,n=t.leftValueMap,s=t.totalButtons;this.eventManager.setupButtonNavigation(i,n,s),this.eventManager.setupZhiboNavigation()},i.calculateButtonMappings=function(e){var t=b.getResponsiveConfig(),i={},n={},s=S.DEFAULT_BUTTON_COUNT;e.forEach(function(e){s++,i[s]={url:e.url()}});var r=0;n[0]=0;for(var a=0;a<=s;a++){var o=$("#client1HeaderButton"+a).outerWidth();1!==a&&2!==a&&(0===a&&o&&$("#"+S.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND).width(o),o&&(n[a+1]=o+r-t.leftModifier,r+=o))}return{buttonsCustomizationMap:i,leftValueMap:n,totalButtons:s}},i.setupMiscellaneousFeatures=function(){this.eventManager.setupLeaderboardPosition(),a().session.user||this.addHeaderIcon()},i.addHeaderIcon=function(){var e=Ge.createHeaderIcon();$(S.SELECTORS.APP_BACK_CONTROL).prepend(e)},i.applyMobileStyles=function(){this.appState.isMobile&&($(S.SELECTORS.APP).css("overflow-x","hidden"),$(S.SELECTORS.APP_CONTENT).css({"min-height":"auto",background:""}))},i.getAppState=function(){return o({},this.appState)},i.updateAppState=function(e){this.appState=o({},this.appState,e)},i.dispose=function(){this.eventManager.offAll(),Pe.destroyAll(),this.isInitialized=!1,this.appState.isInitialized=!1},i.restart=function(){var e=t(s().mark(function e(){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.dispose(),e.next=1,this.initialize();case 1:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}(),e}();$e.instance=void 0,a().initializers.add("wusong8899-client1-header-adv",t(s().mark(function e(){var t,i;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=$e.getInstance(),e.next=1,t.initialize();case 1:console.log("Client1 Header Adv extension initialized successfully"),e.next=3;break;case 2:e.prev=2,i=e.catch(0),console.error("Failed to initialize Client1 Header Adv extension:",i);case 3:case"end":return e.stop()}},e,null,[[0,2]])})))})(),module.exports={}})();
//# sourceMappingURL=forum.js.map
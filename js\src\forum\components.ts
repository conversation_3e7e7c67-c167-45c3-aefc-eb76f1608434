import app from 'flarum/forum/app';
import { ContainerConfig, TronscanData, ButtonsCustomizationData } from './types';
import { AppConfig, DeviceConfig, SettingsConfig } from './config/app-config';

/**
 * Base component factory for creating DOM elements
 */
export class ComponentFactory {
    static createContainer(config: ContainerConfig): HTMLDivElement {
        const container = document.createElement("div");
        container.className = config.className;

        if (config.id) {
            container.id = config.id;
        }

        if (config.height) {
            container.style.height = typeof config.height === 'number'
                ? `${config.height}px`
                : config.height;
        }

        return container;
    }

    static createSwiperContainer(className: string): HTMLDivElement {
        const swiper = document.createElement("div");
        swiper.className = className;
        return swiper;
    }

    static createSwiperWrapper(id?: string): HTMLDivElement {
        const wrapper = document.createElement("div");
        wrapper.className = "swiper-wrapper";
        if (id) {
            wrapper.id = id;
        }
        return wrapper;
    }

    static createSwiperSlide(className: string, innerHTML: string): HTMLDivElement {
        const slide = document.createElement("div");
        slide.className = className;
        slide.innerHTML = innerHTML;
        return slide;
    }

    static createSwiperNavigation(className: string): HTMLDivElement {
        const navigation = document.createElement("div");
        navigation.className = className;
        return navigation;
    }

    static createButton(id: string, number: string, iconClass: string, text: string): string {
        return `<button id="${id}" number="${number}" type="button" class="u-btn">
                    <i class="${iconClass}"></i>
                    <div class="u-btn-text">${text}</div>
                </button>`;
    }

    static createIframe(id: string, className: string, src: string = ''): HTMLIFrameElement {
        const iframe = document.createElement("iframe");
        iframe.id = id;
        iframe.name = "contentOnly";
        iframe.className = className;
        iframe.src = src;
        return iframe;
    }
}

/**
 * Advertisement swiper component
 */
export class AdSwiperComponent {
    static create(): HTMLDivElement {
        const responsive = DeviceConfig.getResponsiveConfig();
        const screenWidth = $(window).width() || 0;
        let styleWidth = screenWidth * 2 - 50;

        const swiperContainer = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.SWIPER_AD_CONTAINER,
            id: AppConfig.CONTAINER_IDS.SWIPER_AD_CONTAINER
        });

        if (responsive.isMobile) {
            swiperContainer.style.width = styleWidth + "px";
            swiperContainer.style.marginLeft = -(styleWidth * 0.254) + "px";
        }

        const swiper = ComponentFactory.createSwiperContainer(AppConfig.CSS_CLASSES.AD_SWIPER);
        const wrapper = ComponentFactory.createSwiperWrapper();

        // Add slides from configuration
        const imagePairs = SettingsConfig.getImageLinkPairs();
        imagePairs.forEach(pair => {
            const slide = ComponentFactory.createSwiperSlide(
                "swiper-slide",
                `<img onclick='window.location.href="${pair.link}"' src='${pair.src}' />`
            );
            wrapper.appendChild(slide);
        });

        // Add navigation
        const nextButton = ComponentFactory.createSwiperNavigation("swiper-button-next");
        const prevButton = ComponentFactory.createSwiperNavigation("swiper-button-prev");
        const pagination = ComponentFactory.createSwiperNavigation("swiper-pagination");

        swiper.appendChild(wrapper);
        swiper.appendChild(nextButton);
        swiper.appendChild(prevButton);
        swiper.appendChild(pagination);
        swiperContainer.appendChild(swiper);

        return swiperContainer;
    }
}

/**
 * Tag swiper component
 */
export class TagSwiperComponent {
    static create(): HTMLDivElement {
        const responsive = DeviceConfig.getResponsiveConfig();
        const tagTile = $(".TagTile");

        const swiperContainer = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.SWIPER_TAG_CONTAINER,
            id: AppConfig.CONTAINER_IDS.SWIPER_TAG_CONTAINER
        });

        const swiper = ComponentFactory.createSwiperContainer(AppConfig.CSS_CLASSES.TAG_SWIPER);
        const tagTextOuterContainer = ComponentFactory.createContainer({
            className: "TagTextOuterContainer"
        });

        swiperContainer.appendChild(tagTextOuterContainer);
        tagTextOuterContainer.appendChild(swiper);

        const wrapper = ComponentFactory.createSwiperWrapper(AppConfig.CONTAINER_IDS.SWIPER_TAG_CONTAINER.replace('Container', 'Wrapper'));
        swiper.appendChild(wrapper);

        // Create slides from existing tag tiles
        for (let i = 0; i < tagTile.length; i++) {
            const tag = tagTile[i];
            const tagURL = $(tag).find("a").attr("href") || '';
            const tagBackground = $(tag).css("background") || '';
            const tagName = $(tag).find(".TagTile-name").text() || '';
            const tagNameColor = $(tag).find(".TagTile-name").css("color") || '';

            const slideContent = `<a href='${tagURL}'>
                <div class='${responsive.isMobile ? AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG_INNER_MOBILE : AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG_INNER}'
                     style='background:${tagBackground};background-size: cover;background-position: center;background-repeat: no-repeat;'>
                    <div style='font-weight:bold;font-size:14px;color:${tagNameColor}'>${tagName}</div>
                </div>
            </a>`;

            const slide = ComponentFactory.createSwiperSlide(AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG, slideContent);
            wrapper.appendChild(slide);
        }

        return swiperContainer;
    }

    static addTextContent(container: HTMLDivElement): void {
        const tagTextContainer = container.querySelector('.TagTextOuterContainer');
        if (tagTextContainer) {
            $(tagTextContainer).prepend("<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>");
            $(tagTextContainer).append(`
                <div style="text-align:center;padding-top: 10px;">
                    <button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;">
                        <div style="margin-top: 5px;" class="Button-label">
                            <img onClick="window.open('https://kick.com/wangming886', '_blank')" style="width: 32px;" src="https://mutluresim.com/images/2023/04/10/KcgSG.png">
                            <img onClick="window.open('https://m.facebook.com', '_blank')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcF6i.png">
                            <img onClick="window.open('https://twitter.com/youngron131_', '_blank')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcDas.png">
                            <img onClick="window.open('https://m.youtube.com/@ag8888','_blank')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcQjd.png">
                            <img onClick="window.open('https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=', '_blank')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcBAL.png">
                        </div>
                    </button>
                </div>
            `);
        }
    }
}

/**
 * Tronscan swiper component
 */
export class TronscanSwiperComponent {
    static create(tronscanData: TronscanData[]): HTMLDivElement {
        const textContainer = ComponentFactory.createContainer({
            className: "TronscanTextContainer",
            id: AppConfig.CONTAINER_IDS.TRONSCAN_TEXT_CONTAINER
        });
        textContainer.innerHTML = "<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度";

        const swiper = ComponentFactory.createSwiperContainer(AppConfig.CSS_CLASSES.TRONSCAN_SWIPER);
        const wrapper = ComponentFactory.createSwiperWrapper();

        tronscanData.forEach(data => {
            const tronscanValueUsd = parseInt(data.valueUsd()) + " USD";
            const tronscanBackground = "url(" + data.img() + ");";

            const slideContent = `
                <div style='width:100px;height:130px;border-radius: 12px;background: ${tronscanBackground};background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'>
                    <div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div>
                    <div class='tronscanMask'>
                        <div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'>
                            <span>${tronscanValueUsd}</span>
                        </div>
                    </div>
                </div>
            `;

            const slide = ComponentFactory.createSwiperSlide(AppConfig.CSS_CLASSES.SWIPER_SLIDE_TAG, slideContent);
            wrapper.appendChild(slide);
        });

        swiper.appendChild(wrapper);

        const container = ComponentFactory.createContainer({
            className: "tronscanSwiperContainer"
        });
        container.appendChild(textContainer);
        container.appendChild(swiper);

        return container;
    }
}

/**
 * Button navigation component
 */
export class ButtonNavigationComponent {
    static create(buttonsCustomizationData: ButtonsCustomizationData[]): HTMLDivElement {
        const container = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.SELECT_TITLE_CONTAINER,
            id: AppConfig.CONTAINER_IDS.SELECT_TITLE_CONTAINER
        });

        let buttonsCustomization = "";
        let totalButtons = AppConfig.DEFAULT_BUTTON_COUNT;

        buttonsCustomizationData.forEach(data => {
            totalButtons++;
            buttonsCustomization += ComponentFactory.createButton(
                `client1HeaderButton${totalButtons}`,
                totalButtons.toString(),
                data.icon(),
                data.name()
            );
        });

        const selectTitle = ComponentFactory.createContainer({
            className: "selectTitle"
        });

        const tagsPageWidth = $(".TagsPage-content").width() || 0;
        const defaultButtons = `
            ${ComponentFactory.createButton("client1HeaderButton0", "0", "fas fa-paw", "论坛")}
            ${ComponentFactory.createButton("client1HeaderButton1", "1", "fab fa-twitch", "直播")}
            ${ComponentFactory.createButton("client1HeaderButton2", "2", "fas fa-dice", "游戏")}
            ${ComponentFactory.createButton("client1HeaderButton3", "3", "fas fa-gifts", "商城")}
        `;

        selectTitle.innerHTML = `
            <div class="switch-btns" style="max-width:${tagsPageWidth}px">
                <div class="btns-container">
                    ${defaultButtons}
                    ${buttonsCustomization}
                    <div id="${AppConfig.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND}" class="selected-bg" style="left: 0px; top: 0px; opacity: 1;"></div>
                </div>
            </div>
        `;

        container.appendChild(selectTitle);
        return container;
    }
}

/**
 * Container components for different sections
 */
export class ContainerComponents {
    static createZhiboContainer(): HTMLDivElement {
        const responsive = DeviceConfig.getResponsiveConfig();
        const container = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.ZHIBO_CONTAINER,
            height: $(".swiperTagContainer").css("height") || ''
        });

        const refreshButton = `
            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>
                <div class='switch-btns'>
                    <div class='btns-container'>
                        <button type='button' class='u-btn'>
                            <div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div>
                        </button>
                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>
                    </div>
                </div>
            </div>
        `;

        const prevButton = `
            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>
                <div class='switch-btns'>
                    <div class='btns-container'>
                        <button type='button' class='u-btn'>
                            <div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div>
                        </button>
                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>
                    </div>
                </div>
            </div>
        `;

        const nextButton = `
            <div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'>
                <div class='switch-btns'>
                    <div class='btns-container'>
                        <button type='button' class='u-btn'>
                            <div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div>
                        </button>
                        <div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div>
                    </div>
                </div>
            </div>
        `;

        const iframe = ComponentFactory.createIframe(
            AppConfig.CONTAINER_IDS.ZHIBO_IFRAME,
            "zhiboIframe"
        );

        container.innerHTML = `
            <div id='${AppConfig.CONTAINER_IDS.LINKS_QUEUE_REFRESH}' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: ${responsive.isMobile ? 0 : -6}px;'>
                ${refreshButton}
            </div>
            <div class='zhiboSubContainer'>
                <div id='${AppConfig.CONTAINER_IDS.LINKS_QUEUE_PREV}' style='display:inline-block;scale:0.8'>
                    ${prevButton}
                </div>
                <div id='${AppConfig.CONTAINER_IDS.LINKS_QUEUE_NEXT}' style='display:inline-block;scale:0.8'>
                    ${nextButton}
                </div>
            </div>
        `;

        container.appendChild(iframe);
        return container;
    }

    static createYouXiContainer(): HTMLDivElement {
        const container = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.YOUXI_CONTAINER,
            height: $(".swiperTagContainer").css("height") || ''
        });
        container.innerText = app.translator.trans("wusong8899-client1.forum.under-construction") as string;
        return container;
    }

    static createShangChengContainer(): HTMLDivElement {
        const container = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.SHANGCHENG_CONTAINER,
            height: $(".swiperTagContainer").css("height") || ''
        });
        container.innerText = app.translator.trans("wusong8899-client1.forum.under-construction") as string;
        return container;
    }

    static createButtonCustomizationContainer(): HTMLDivElement {
        const container = ComponentFactory.createContainer({
            className: AppConfig.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER,
            height: $(".swiperTagContainer").css("height") || ''
        });

        const iframe = ComponentFactory.createIframe(
            AppConfig.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME,
            "customButtonIframe"
        );

        container.appendChild(iframe);
        return container;
    }

    static createHeaderIcon(): HTMLDivElement {
        const container = ComponentFactory.createContainer({
            className: "headerIconContainer",
            id: AppConfig.CONTAINER_IDS.HEADER_ICON
        });

        container.style.display = 'inline-block';
        container.style.marginTop = '8px';
        container.innerHTML = '<img src="https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png" style="height: 24px;" />';

        return container;
    }
}
// Core data interfaces
interface TronscanData {
    name(): string;
    valueUsd(): string;
    img(): string;
}

interface ButtonsCustomizationData {
    name(): string;
    icon(): string;
    color(): string;
    url(): string;
}

interface LinksQueueData {
    attribute(name: string): string;
}

// Map interfaces
interface ButtonsCustomizationMap {
    [key: number]: { url: string };
}

interface LeftValueMap {
    [key: number]: number;
}

// Swiper configuration interface
interface SwiperConfig {
    loop?: boolean;
    spaceBetween?: number;
    slidesPerView?: number | 'auto';
    autoplay?: {
        delay: number;
        disableOnInteraction?: boolean;
    };
    effect?: string;
    centeredSlides?: boolean;
    coverflowEffect?: {
        rotate: number;
        depth: number;
        modifier: number;
        slideShadows: boolean;
        stretch: number;
    };
    pagination?: {
        el: string;
        type: string;
    };
    navigation?: {
        nextEl: string;
        prevEl: string;
    };
    modules: Array<any>;
}

// UI configuration interfaces
interface ContainerConfig {
    className: string;
    id?: string;
    height?: string | number;
    width?: string | number;
}

interface ResponsiveConfig {
    isMobile: boolean;
    spaceBetween: number;
    slidesPerView: number;
    tronscanSlidesPerView: number;
    tronscanSpaceBetween: number;
    eventType: string;
    leftModifier: number;
}

interface ImageLinkPair {
    src: string;
    link: string;
    index: number;
}

// Component state interfaces
interface ComponentState {
    isVisible: boolean;
    isLoading: boolean;
    hasError: boolean;
    errorMessage?: string;
}

interface SwiperState extends ComponentState {
    currentSlide: number;
    totalSlides: number;
    isAutoplayActive: boolean;
}

// Event handling interfaces
interface EventConfig {
    selector: string;
    event: string;
    handler: (event: Event) => void;
    namespace?: string;
}

interface ButtonConfig {
    id: string;
    number: number;
    icon: string;
    text: string;
    url?: string;
    isActive?: boolean;
}

// Service interfaces
interface DataServiceConfig {
    endpoint: string;
    cacheTimeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
}

interface LoadingState {
    isLoading: boolean;
    hasLoaded: boolean;
    hasError: boolean;
    errorMessage?: string;
    lastLoadTime?: Date;
}

// Application state interfaces
interface AppState {
    currentView: 'forum' | 'zhibo' | 'youxi' | 'shangcheng' | 'custom';
    selectedButton: number;
    isMobile: boolean;
    isInitialized: boolean;
}

interface ViewState {
    name: string;
    isActive: boolean;
    container?: HTMLElement;
    data?: any;
}

// Configuration interfaces
interface ExtensionConfig {
    transitionTime: number;
    maxLinkImagePairs: number;
    checkInterval: number;
    refreshDelay: number;
}

interface UIConfig {
    selectors: Record<string, string>;
    containerIds: Record<string, string>;
    cssClasses: Record<string, string>;
}

// Utility type definitions
type EventType = 'click' | 'touchend' | 'change' | 'input' | 'submit';
type ViewType = 'forum' | 'zhibo' | 'youxi' | 'shangcheng' | 'custom';
type DeviceType = 'mobile' | 'tablet' | 'desktop';
type LoadingStatus = 'idle' | 'loading' | 'success' | 'error';

// Generic utility interfaces
interface Disposable {
    dispose(): void;
}

interface Initializable {
    initialize(): Promise<void> | void;
    isInitialized(): boolean;
}

interface Configurable<T> {
    configure(config: T): void;
    getConfig(): T;
}

// Export all types
export type {
    // Core data types
    TronscanData,
    ButtonsCustomizationData,
    LinksQueueData,
    ButtonsCustomizationMap,
    LeftValueMap,

    // Configuration types
    SwiperConfig,
    ContainerConfig,
    ResponsiveConfig,
    ImageLinkPair,
    ExtensionConfig,
    UIConfig,
    DataServiceConfig,

    // State types
    ComponentState,
    SwiperState,
    LoadingState,
    AppState,
    ViewState,

    // Event types
    EventConfig,
    ButtonConfig,

    // Utility types
    EventType,
    ViewType,
    DeviceType,
    LoadingStatus,

    // Interface types
    Disposable,
    Initializable,
    Configurable
};
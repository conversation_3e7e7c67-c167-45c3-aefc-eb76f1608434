import { ButtonsCustomizationMap, LeftValueMap } from '../types';
import { AppConfig, DeviceConfig } from '../config/app-config';
import { DataManager } from './data-service';

/**
 * Event handler interface
 */
interface EventHandler {
    selector: string;
    event: string;
    handler: (event: Event) => void;
}

/**
 * Event manager for centralized event handling
 */
export class EventManager {
    private static instance: EventManager;
    private handlers: Map<string, EventHandler[]> = new Map();
    private dataManager: DataManager;

    private constructor() {
        this.dataManager = DataManager.getInstance();
    }

    public static getInstance(): EventManager {
        if (!EventManager.instance) {
            EventManager.instance = new EventManager();
        }
        return EventManager.instance;
    }

    /**
     * Register an event handler
     */
    public on(id: string, selector: string, event: string, handler: (event: Event) => void): void {
        if (!this.handlers.has(id)) {
            this.handlers.set(id, []);
        }

        const eventHandler: EventHandler = { selector, event, handler };
        this.handlers.get(id)!.push(eventHandler);

        // Attach the event listener
        $(document).on(event, selector, handler);
    }

    /**
     * Remove event handlers by ID
     */
    public off(id: string): void {
        const handlers = this.handlers.get(id);
        if (handlers) {
            handlers.forEach(({ selector, event, handler }) => {
                $(document).off(event, selector, handler);
            });
            this.handlers.delete(id);
        }
    }

    /**
     * Remove all event handlers
     */
    public offAll(): void {
        this.handlers.forEach((handlers, id) => {
            this.off(id);
        });
    }

    /**
     * Setup button navigation events
     */
    public setupButtonNavigation(
        buttonsCustomizationMap: ButtonsCustomizationMap,
        leftValueMap: LeftValueMap,
        totalButtons: number
    ): void {
        const responsive = DeviceConfig.getResponsiveConfig();
        const linksQueueService = this.dataManager.getLinksQueueService();

        this.on('buttonNavigation', '.u-btn', responsive.eventType, (event) => {
            const target = event.currentTarget as HTMLElement;
            const numberStr = $(target).attr('number');
            const number = numberStr ? parseInt(numberStr) : 0;
            const zhiboIframe = document.getElementById(AppConfig.CONTAINER_IDS.ZHIBO_IFRAME) as HTMLIFrameElement | null;

            $(".App").css("min-height", "100vh");

            this.handleButtonClick(number, buttonsCustomizationMap, zhiboIframe, linksQueueService);
            this.updateButtonSelection(target, leftValueMap, number);
        });
    }

    /**
     * Handle button click logic
     */
    private handleButtonClick(
        number: number,
        buttonsCustomizationMap: ButtonsCustomizationMap,
        zhiboIframe: HTMLIFrameElement | null,
        linksQueueService: any
    ): void {
        // Hide all containers first
        this.hideAllContainers();

        switch (number) {
            case 0: // Forum
                this.showForumView(zhiboIframe);
                break;
            case 1: // Zhibo
                this.showZhiboView(zhiboIframe, linksQueueService);
                break;
            case 2: // Youxi
                this.showYouxiView(zhiboIframe);
                break;
            case 3: // Shangcheng
                this.showShangchengView(zhiboIframe);
                break;
            default: // Custom buttons
                this.showCustomButtonView(number, buttonsCustomizationMap);
                break;
        }
    }

    /**
     * Hide all containers
     */
    private hideAllContainers(): void {
        $(`.${AppConfig.CSS_CLASSES.SWIPER_TAG_CONTAINER}`).css("display", "none");
        $(`.${AppConfig.CSS_CLASSES.ZHIBO_CONTAINER}`).css("display", "none");
        $(`.${AppConfig.CSS_CLASSES.YOUXI_CONTAINER}`).css("display", "none");
        $(`.${AppConfig.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER}`).css("display", "none");
        $(`.${AppConfig.CSS_CLASSES.SHANGCHENG_CONTAINER}`).css("display", "none");
    }

    /**
     * Show forum view
     */
    private showForumView(zhiboIframe: HTMLIFrameElement | null): void {
        $(`.${AppConfig.CSS_CLASSES.SWIPER_TAG_CONTAINER}`).css("display", "");
        $(".App").css("min-height", "50vh");
        if (zhiboIframe) {
            zhiboIframe.src = "";
        }
    }

    /**
     * Show zhibo view
     */
    private showZhiboView(zhiboIframe: HTMLIFrameElement | null, linksQueueService: any): void {
        $(`.${AppConfig.CSS_CLASSES.ZHIBO_CONTAINER}`).css("display", "inline-block");

        const appNavHeight = $("#app-navigation").outerHeight() || 0;
        const selectTitleHeight = $(".selectTitleContainer").outerHeight() || 0;
        const linksQueueHeight = $("#linksQueuePrev").outerHeight() || 0;
        const iframeHeight = window.innerHeight - appNavHeight - selectTitleHeight - linksQueueHeight;

        if (zhiboIframe) {
            $("#zhiboIframe").css("height", iframeHeight + "px");
            const currentItem = linksQueueService.getCurrentItem();
            if (currentItem) {
                const linksQueueURL = currentItem.attribute("links");
                if (zhiboIframe.src !== linksQueueURL) {
                    zhiboIframe.src = linksQueueURL;
                }
            }
        }
    }

    /**
     * Show youxi view
     */
    private showYouxiView(zhiboIframe: HTMLIFrameElement | null): void {
        $(`.${AppConfig.CSS_CLASSES.YOUXI_CONTAINER}`).css("display", "flex");
        if (zhiboIframe) {
            zhiboIframe.src = "";
        }
    }

    /**
     * Show shangcheng view
     */
    private showShangchengView(zhiboIframe: HTMLIFrameElement | null): void {
        $(`.${AppConfig.CSS_CLASSES.SHANGCHENG_CONTAINER}`).css("display", "flex");
        if (zhiboIframe) {
            zhiboIframe.src = "";
        }
    }

    /**
     * Show custom button view
     */
    private showCustomButtonView(number: number, buttonsCustomizationMap: ButtonsCustomizationMap): void {
        const customButtonData = buttonsCustomizationMap[number];

        if (customButtonData) {
            const appNavHeight = $("#app-navigation").outerHeight() || 0;
            const selectTitleHeight = $(".selectTitleContainer").outerHeight() || 0;
            const linksQueueHeight = $("#linksQueuePrev").outerHeight() || 0;
            let iframeHeight = window.innerHeight - appNavHeight - selectTitleHeight - linksQueueHeight;
            let paddingBottom = 0;
            let scrolling: "yes" | "no" = "yes";
            let containerHeight: string | number = $(".swiperTagContainer").css("height") || '0px';

            // Custom height configurations for specific buttons
            if (number == 5) {
                iframeHeight = 550;
                containerHeight = 550;
            } else if (number == 6) {
                iframeHeight = containerHeight = 380;
                scrolling = "no";
            } else if (number == 7 || number == 8) {
                paddingBottom = 20;
            }

            $(`.${AppConfig.CSS_CLASSES.BUTTON_CUSTOMIZATION_CONTAINER}`).css({
                "padding-bottom": paddingBottom + "px",
                "height": containerHeight + "px",
                "display": "inline-block"
            });

            $(`#${AppConfig.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME}`).css({
                "padding-bottom": paddingBottom + "px",
                "height": iframeHeight + "px"
            }).attr("scrolling", scrolling);

            const customButtonIframe = document.getElementById(AppConfig.CONTAINER_IDS.CUSTOM_BUTTON_IFRAME) as HTMLIFrameElement | null;
            if (customButtonIframe) {
                customButtonIframe.src = customButtonData.url;
            }
        }
    }

    /**
     * Update button selection visual feedback
     */
    private updateButtonSelection(target: HTMLElement, leftValueMap: LeftValueMap, number: number): void {
        const selectedButtonWidth = $(target).outerWidth();
        const buttonSelectedBackground = $(`#${AppConfig.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND}`);

        if (buttonSelectedBackground.length && selectedButtonWidth) {
            buttonSelectedBackground.width(selectedButtonWidth);
        }

        if (leftValueMap[number] !== undefined) {
            buttonSelectedBackground.css("left", leftValueMap[number]);
        }
    }

    /**
     * Setup zhibo navigation events
     */
    public setupZhiboNavigation(): void {
        const responsive = DeviceConfig.getResponsiveConfig();
        const linksQueueService = this.dataManager.getLinksQueueService();
        const zhiboIframe = document.getElementById(AppConfig.CONTAINER_IDS.ZHIBO_IFRAME) as HTMLIFrameElement | null;

        // Refresh button
        this.on('zhiboRefresh', `#${AppConfig.CONTAINER_IDS.LINKS_QUEUE_REFRESH}`, responsive.eventType, () => {
            if (zhiboIframe) {
                zhiboIframe.src = "";
                setTimeout(() => {
                    const currentItem = linksQueueService.getCurrentItem();
                    if (currentItem && zhiboIframe) {
                        zhiboIframe.src = currentItem.attribute("links");
                    }
                }, AppConfig.ZHIBO_REFRESH_DELAY);
            }
        });

        // Previous button
        this.on('zhiboPrev', `#${AppConfig.CONTAINER_IDS.LINKS_QUEUE_PREV}`, responsive.eventType, () => {
            linksQueueService.decrementPointer();
            const currentItem = linksQueueService.getCurrentItem();
            
            if (currentItem && zhiboIframe) {
                zhiboIframe.src = currentItem.attribute("links");
            }

            this.updateZhiboButtonStates(linksQueueService);
        });

        // Next button
        this.on('zhiboNext', `#${AppConfig.CONTAINER_IDS.LINKS_QUEUE_NEXT}`, responsive.eventType, () => {
            linksQueueService.incrementPointer();
            const currentItem = linksQueueService.getCurrentItem();
            
            if (currentItem && zhiboIframe) {
                zhiboIframe.src = currentItem.attribute("links");
            }

            this.updateZhiboButtonStates(linksQueueService);
        });
    }

    /**
     * Update zhibo button states based on navigation
     */
    private updateZhiboButtonStates(linksQueueService: any): void {
        $("#nextZhiBoButton").css("color", linksQueueService.hasNext() ? "" : "#666");
        $("#prevZhiBoButton").css("color", linksQueueService.hasPrevious() ? "" : "#666");
    }

    /**
     * Setup mobile-specific UI adjustments
     */
    public setupMobileAdjustments(): void {
        const responsive = DeviceConfig.getResponsiveConfig();
        
        if (responsive.isMobile) {
            $(".item-newDiscussion").find("span.Button-label").html("<div class='buttonRegister'>登录</div>");
            $(".item-newDiscussion").find("span.Button-label").css({
                "display": "block",
                "font-size": "14px",
                "word-spacing": "-1px"
            });
        }

        $(".item-newDiscussion").find("i").css("display", "none");
        $(".item-nav").remove();
        $(".TagTiles").css("display", "none");
    }

    /**
     * Setup leaderboard positioning
     */
    public setupLeaderboardPosition(): void {
        $(".item-MoneyLeaderboard").addClass("App-primaryControl");
        $(".item-forum-checkin").parent().append($(".item-MoneyLeaderboard"));
        $(".item-MoneyLeaderboard").css("right", "75px");
    }
}

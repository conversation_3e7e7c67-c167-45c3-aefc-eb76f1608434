/**
 * Module validation tests for the refactored codebase
 * These tests validate that all modules can be imported and instantiated correctly
 */

// Import all modules to test their structure
import { AppController } from '../controllers/app-controller';
import { DataManager, TronscanService, ButtonsCustomizationService, LinksQueueService } from '../services/data-service';
import { EventManager } from '../services/event-manager';
import { SwiperManager, SwiperConfigFactory, SwiperUtils } from '../services/swiper-manager';
import { 
    ComponentFactory, 
    AdSwiperComponent, 
    TagSwiperComponent, 
    TronscanSwiperComponent,
    ButtonNavigationComponent,
    ContainerComponents 
} from '../components';
import { AppConfig, DeviceConfig, SettingsConfig } from '../config/app-config';
import { AdminSettingsConfig } from '../admin/config/settings-config';

/**
 * Test suite for module validation
 */
class ModuleValidationTest {
    private testResults: { [key: string]: boolean } = {};

    /**
     * Run all validation tests
     */
    public runAllTests(): void {
        console.log('Starting module validation tests...');

        this.testSingletonPatterns();
        this.testConfigurationModules();
        this.testServiceModules();
        this.testComponentModules();
        this.testSwiperModules();
        this.testTypeDefinitions();
        this.testAdminConfiguration();

        this.printResults();
    }

    /**
     * Test singleton patterns
     */
    private testSingletonPatterns(): void {
        try {
            const appController1 = AppController.getInstance();
            const appController2 = AppController.getInstance();
            this.testResults['Singleton AppController'] = appController1 === appController2;

            const dataManager1 = DataManager.getInstance();
            const dataManager2 = DataManager.getInstance();
            this.testResults['Singleton DataManager'] = dataManager1 === dataManager2;

            const eventManager1 = EventManager.getInstance();
            const eventManager2 = EventManager.getInstance();
            this.testResults['Singleton EventManager'] = eventManager1 === eventManager2;
        } catch (error) {
            console.error('Error testing singleton patterns:', error);
            this.testResults['Singleton Patterns'] = false;
        }
    }

    /**
     * Test configuration modules
     */
    private testConfigurationModules(): void {
        try {
            // Test AppConfig constants
            this.testResults['AppConfig Constants'] = 
                typeof AppConfig.CHECK_TIME === 'number' &&
                typeof AppConfig.DEFAULT_TRANSITION_TIME === 'number' &&
                typeof AppConfig.SELECTORS === 'object' &&
                typeof AppConfig.CONTAINER_IDS === 'object' &&
                typeof AppConfig.CSS_CLASSES === 'object';

            // Test DeviceConfig
            this.testResults['DeviceConfig Methods'] = 
                typeof DeviceConfig.isMobile === 'function' &&
                typeof DeviceConfig.getResponsiveConfig === 'function';

            // Test SettingsConfig
            this.testResults['SettingsConfig Methods'] = 
                typeof SettingsConfig.getTransitionTime === 'function' &&
                typeof SettingsConfig.getImageSrc === 'function' &&
                typeof SettingsConfig.getImageLink === 'function' &&
                typeof SettingsConfig.getImageLinkPairs === 'function';
        } catch (error) {
            console.error('Error testing configuration modules:', error);
            this.testResults['Configuration Modules'] = false;
        }
    }

    /**
     * Test service modules
     */
    private testServiceModules(): void {
        try {
            const dataManager = DataManager.getInstance();
            
            // Test service getters
            this.testResults['DataManager Services'] = 
                dataManager.getTronscanService() instanceof TronscanService &&
                dataManager.getButtonsCustomizationService() instanceof ButtonsCustomizationService &&
                dataManager.getLinksQueueService() instanceof LinksQueueService;

            // Test service methods
            const tronscanService = dataManager.getTronscanService();
            this.testResults['Service Methods'] = 
                typeof tronscanService.load === 'function' &&
                typeof tronscanService.isLoading === 'function' &&
                typeof tronscanService.getData === 'function' &&
                typeof tronscanService.parseResults === 'function';

            // Test LinksQueueService specific methods
            const linksQueueService = dataManager.getLinksQueueService();
            this.testResults['LinksQueueService Methods'] = 
                typeof linksQueueService.getPointer === 'function' &&
                typeof linksQueueService.setPointer === 'function' &&
                typeof linksQueueService.incrementPointer === 'function' &&
                typeof linksQueueService.decrementPointer === 'function' &&
                typeof linksQueueService.getCurrentItem === 'function' &&
                typeof linksQueueService.hasNext === 'function' &&
                typeof linksQueueService.hasPrevious === 'function';
        } catch (error) {
            console.error('Error testing service modules:', error);
            this.testResults['Service Modules'] = false;
        }
    }

    /**
     * Test component modules
     */
    private testComponentModules(): void {
        try {
            // Test ComponentFactory static methods
            this.testResults['ComponentFactory Methods'] = 
                typeof ComponentFactory.createContainer === 'function' &&
                typeof ComponentFactory.createSwiperContainer === 'function' &&
                typeof ComponentFactory.createSwiperWrapper === 'function' &&
                typeof ComponentFactory.createSwiperSlide === 'function' &&
                typeof ComponentFactory.createSwiperNavigation === 'function' &&
                typeof ComponentFactory.createButton === 'function' &&
                typeof ComponentFactory.createIframe === 'function';

            // Test component classes
            this.testResults['Component Classes'] = 
                typeof AdSwiperComponent.create === 'function' &&
                typeof TagSwiperComponent.create === 'function' &&
                typeof TagSwiperComponent.addTextContent === 'function' &&
                typeof TronscanSwiperComponent.create === 'function' &&
                typeof ButtonNavigationComponent.create === 'function' &&
                typeof ContainerComponents.createZhiboContainer === 'function' &&
                typeof ContainerComponents.createYouXiContainer === 'function' &&
                typeof ContainerComponents.createShangChengContainer === 'function' &&
                typeof ContainerComponents.createButtonCustomizationContainer === 'function' &&
                typeof ContainerComponents.createHeaderIcon === 'function';
        } catch (error) {
            console.error('Error testing component modules:', error);
            this.testResults['Component Modules'] = false;
        }
    }

    /**
     * Test swiper modules
     */
    private testSwiperModules(): void {
        try {
            // Test SwiperConfigFactory
            this.testResults['SwiperConfigFactory Methods'] = 
                typeof SwiperConfigFactory.createTagSwiperConfig === 'function' &&
                typeof SwiperConfigFactory.createTronscanSwiperConfig === 'function' &&
                typeof SwiperConfigFactory.createAdSwiperConfig === 'function';

            // Test SwiperManager static methods
            this.testResults['SwiperManager Methods'] = 
                typeof SwiperManager.create === 'function' &&
                typeof SwiperManager.getInstance === 'function' &&
                typeof SwiperManager.destroy === 'function' &&
                typeof SwiperManager.destroyAll === 'function' &&
                typeof SwiperManager.createTagSwiper === 'function' &&
                typeof SwiperManager.createTronscanSwiper === 'function' &&
                typeof SwiperManager.createAdSwiper === 'function' &&
                typeof SwiperManager.updateSwiper === 'function' &&
                typeof SwiperManager.slideTo === 'function' &&
                typeof SwiperManager.startAutoplay === 'function' &&
                typeof SwiperManager.stopAutoplay === 'function' &&
                typeof SwiperManager.getAllInstances === 'function' &&
                typeof SwiperManager.hasInstance === 'function';

            // Test SwiperUtils
            this.testResults['SwiperUtils Methods'] = 
                typeof SwiperUtils.waitForElement === 'function' &&
                typeof SwiperUtils.createWithRetry === 'function' &&
                typeof SwiperUtils.safeDestroy === 'function';
        } catch (error) {
            console.error('Error testing swiper modules:', error);
            this.testResults['Swiper Modules'] = false;
        }
    }

    /**
     * Test type definitions
     */
    private testTypeDefinitions(): void {
        try {
            // Test that types can be imported without errors
            // This is mainly a compilation test
            this.testResults['Type Definitions'] = true;
        } catch (error) {
            console.error('Error testing type definitions:', error);
            this.testResults['Type Definitions'] = false;
        }
    }

    /**
     * Test admin configuration
     */
    private testAdminConfiguration(): void {
        try {
            // Test AdminSettingsConfig
            this.testResults['AdminSettingsConfig Methods'] = 
                typeof AdminSettingsConfig.getAllSettings === 'function' &&
                typeof AdminSettingsConfig.getSettingsByCategory === 'function' &&
                typeof AdminSettingsConfig.getLinkImagePairCount === 'function';

            // Test that settings can be generated
            const allSettings = AdminSettingsConfig.getAllSettings();
            const basicSettings = AdminSettingsConfig.getSettingsByCategory('basic');
            const linkSettings = AdminSettingsConfig.getSettingsByCategory('links');
            
            this.testResults['AdminSettingsConfig Data'] = 
                Array.isArray(allSettings) &&
                Array.isArray(basicSettings) &&
                Array.isArray(linkSettings) &&
                allSettings.length > 0 &&
                basicSettings.length > 0 &&
                linkSettings.length > 0;
        } catch (error) {
            console.error('Error testing admin configuration:', error);
            this.testResults['Admin Configuration'] = false;
        }
    }

    /**
     * Print test results
     */
    private printResults(): void {
        console.log('\n=== Module Validation Test Results ===');
        
        let passedTests = 0;
        let totalTests = 0;
        
        for (const [testName, result] of Object.entries(this.testResults)) {
            totalTests++;
            if (result) {
                passedTests++;
                console.log(`✅ ${testName}: PASSED`);
            } else {
                console.log(`❌ ${testName}: FAILED`);
            }
        }
        
        console.log(`\n📊 Summary: ${passedTests}/${totalTests} tests passed`);
        
        if (passedTests === totalTests) {
            console.log('🎉 All module validation tests passed!');
        } else {
            console.log('⚠️  Some tests failed. Please check the module structure.');
        }
    }
}

// Export for potential use in other test files
export { ModuleValidationTest };

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined') {
    // Browser environment - run tests when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        const validator = new ModuleValidationTest();
        validator.runAllTests();
    });
} else {
    // Node environment - run tests immediately
    const validator = new ModuleValidationTest();
    validator.runAllTests();
}

(()=>{var t={n:e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},d:(e,n)=>{for(var i in n)t.o(n,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:n[i]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};(()=>{"use strict";const e=flarum.core.compat["admin/app"];var n=t.n(e),i=function(){function t(){}return t.generateLinkImagePairs=function(t,e){for(var n=[],i=t;i<=e;i++)n.push({index:i,linkKey:"Link"+i,imageKey:"Image"+i});return n},t.createSetting=function(t,e,i){return{setting:this.EXTENSION_PREFIX+"."+t,type:e,label:n().translator.trans(this.TRANSLATION_PREFIX+"."+i)}},t.getBasicSettings=function(){return[this.createSetting("TransitionTime","number","TransitionTime")]},t.getLinkImageSettings=function(){var t=this,e=[];return this.generateLinkImagePairs(1,30).forEach(function(n){e.push(t.createSetting(n.linkKey,"URL",n.linkKey)),e.push(t.createSetting(n.imageKey,"URL",n.imageKey))}),e},t.getAllSettings=function(){return[].concat(this.getBasicSettings(),this.getLinkImageSettings())},t.getSettingsByCategory=function(t){switch(t){case"basic":return this.getBasicSettings();case"links":return this.getLinkImageSettings();default:return[]}},t.getLinkImagePairCount=function(){return 30},t}();i.EXTENSION_PREFIX="wusong8899-client1-header-adv",i.TRANSLATION_PREFIX="wusong8899-client1.admin",n().initializers.add("wusong8899/client1-header-adv",function(){var t=n().extensionData.for("wusong8899-client1-header-adv");i.getAllSettings().forEach(function(e){t.registerSetting(e)})})})(),module.exports={}})();
//# sourceMappingURL=admin.js.map
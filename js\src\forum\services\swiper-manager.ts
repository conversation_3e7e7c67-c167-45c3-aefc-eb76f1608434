import Swiper from 'swiper';
import { EffectCoverflow, Navigation, Pagination, Autoplay } from 'swiper/modules';
import { SwiperConfig } from '../types';
import { AppConfig, DeviceConfig, SettingsConfig } from '../config/app-config';

/**
 * Swiper configuration factory
 */
export class SwiperConfigFactory {
    /**
     * Create configuration for tag swiper
     */
    public static createTagSwiperConfig(): SwiperConfig {
        const responsive = DeviceConfig.getResponsiveConfig();
        
        return {
            loop: true,
            spaceBetween: responsive.spaceBetween,
            slidesPerView: responsive.slidesPerView,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            modules: [Autoplay]
        };
    }

    /**
     * Create configuration for Tronscan swiper
     */
    public static createTronscanSwiperConfig(): SwiperConfig {
        const responsive = DeviceConfig.getResponsiveConfig();
        
        return {
            loop: true,
            spaceBetween: responsive.tronscanSpaceBetween,
            slidesPerView: responsive.tronscanSlidesPerView,
            modules: []
        };
    }

    /**
     * Create configuration for advertisement swiper
     */
    public static createAdSwiperConfig(): SwiperConfig {
        const transitionTime = SettingsConfig.getTransitionTime();
        
        return {
            autoplay: {
                delay: transitionTime,
                disableOnInteraction: false,
            },
            loop: true,
            spaceBetween: 30,
            effect: "coverflow",
            centeredSlides: true,
            slidesPerView: 2,
            coverflowEffect: {
                rotate: 0,
                depth: 100,
                modifier: 1,
                slideShadows: true,
                stretch: 0
            },
            pagination: {
                el: '.swiper-pagination',
                type: 'bullets',
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            modules: [EffectCoverflow, Navigation, Pagination, Autoplay]
        };
    }
}

/**
 * Swiper instance manager
 */
export class SwiperManager {
    private static instances: Map<string, Swiper> = new Map();

    /**
     * Create and register a new Swiper instance
     */
    public static create(selector: string, config: SwiperConfig, id?: string): Swiper {
        const swiper = new Swiper(selector, config);
        const instanceId = id || selector;
        
        this.instances.set(instanceId, swiper);
        return swiper;
    }

    /**
     * Get a Swiper instance by ID
     */
    public static getInstance(id: string): Swiper | undefined {
        return this.instances.get(id);
    }

    /**
     * Destroy a Swiper instance
     */
    public static destroy(id: string): void {
        const swiper = this.instances.get(id);
        if (swiper) {
            swiper.destroy();
            this.instances.delete(id);
        }
    }

    /**
     * Destroy all Swiper instances
     */
    public static destroyAll(): void {
        this.instances.forEach((swiper, id) => {
            swiper.destroy();
        });
        this.instances.clear();
    }

    /**
     * Create tag swiper
     */
    public static createTagSwiper(): Swiper {
        const config = SwiperConfigFactory.createTagSwiperConfig();
        return this.create('.tagSwiper', config, 'tagSwiper');
    }

    /**
     * Create Tronscan swiper
     */
    public static createTronscanSwiper(): Swiper {
        const config = SwiperConfigFactory.createTronscanSwiperConfig();
        return this.create('.tronscanSwiper', config, 'tronscanSwiper');
    }

    /**
     * Create advertisement swiper
     */
    public static createAdSwiper(): Swiper {
        const config = SwiperConfigFactory.createAdSwiperConfig();
        return this.create('.adSwiper', config, 'adSwiper');
    }

    /**
     * Update swiper when data changes
     */
    public static updateSwiper(id: string): void {
        const swiper = this.getInstance(id);
        if (swiper) {
            swiper.update();
        }
    }

    /**
     * Slide to specific index
     */
    public static slideTo(id: string, index: number, speed?: number): void {
        const swiper = this.getInstance(id);
        if (swiper) {
            swiper.slideTo(index, speed);
        }
    }

    /**
     * Start autoplay
     */
    public static startAutoplay(id: string): void {
        const swiper = this.getInstance(id);
        if (swiper && swiper.autoplay) {
            swiper.autoplay.start();
        }
    }

    /**
     * Stop autoplay
     */
    public static stopAutoplay(id: string): void {
        const swiper = this.getInstance(id);
        if (swiper && swiper.autoplay) {
            swiper.autoplay.stop();
        }
    }

    /**
     * Get all active swiper instances
     */
    public static getAllInstances(): Map<string, Swiper> {
        return new Map(this.instances);
    }

    /**
     * Check if a swiper instance exists
     */
    public static hasInstance(id: string): boolean {
        return this.instances.has(id);
    }
}

/**
 * Swiper utilities for common operations
 */
export class SwiperUtils {
    /**
     * Wait for DOM element to be available
     */
    public static waitForElement(selector: string, timeout: number = 5000): Promise<Element> {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }

    /**
     * Create swiper with retry mechanism
     */
    public static async createWithRetry(
        selector: string, 
        config: SwiperConfig, 
        maxRetries: number = 3,
        retryDelay: number = 100
    ): Promise<Swiper> {
        for (let i = 0; i < maxRetries; i++) {
            try {
                await this.waitForElement(selector, 1000);
                return SwiperManager.create(selector, config);
            } catch (error) {
                if (i === maxRetries - 1) {
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
        throw new Error(`Failed to create swiper after ${maxRetries} retries`);
    }

    /**
     * Safely destroy swiper
     */
    public static safeDestroy(id: string): void {
        try {
            SwiperManager.destroy(id);
        } catch (error) {
            console.warn(`Error destroying swiper ${id}:`, error);
        }
    }
}

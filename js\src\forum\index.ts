import app from 'flarum/forum/app';
import { AppController } from './controllers/app-controller';

/**
 * Main entry point for the forum extension
 */
app.initializers.add('wusong8899-client1-header-adv', async () => {
    try {
        const appController = AppController.getInstance();
        await appController.initialize();

        console.log('Client1 Header Adv extension initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Client1 Header Adv extension:', error);
    }
});





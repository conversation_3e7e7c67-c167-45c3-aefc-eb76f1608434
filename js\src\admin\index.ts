import app from 'flarum/admin/app';
import { AdminSettingsConfig } from './config/settings-config';

app.initializers.add('wusong8899/client1-header-adv', () => {
    const extensionData = app.extensionData.for('wusong8899-client1-header-adv');

    // Register all settings from configuration
    AdminSettingsConfig.getAllSettings().forEach(setting => {
        extensionData.registerSetting(setting);
    });
});


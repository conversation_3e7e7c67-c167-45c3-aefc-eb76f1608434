import { extend } from 'flarum/common/extend';
import app from 'flarum/forum/app';
import HeaderPrimary from 'flarum/forum/components/HeaderPrimary';
import { Vnode } from 'mithril';

import { DataManager } from '../services/data-service';
import { EventManager } from '../services/event-manager';
import { SwiperManager } from '../services/swiper-manager';
import { 
    AdSwiperComponent, 
    TagSwiperComponent, 
    TronscanSwiperComponent,
    ButtonNavigationComponent,
    ContainerComponents
} from '../components';
import { AppConfig, DeviceConfig, SettingsConfig } from '../config/app-config';
import { ButtonsCustomizationMap, LeftValueMap, AppState, Initializable } from '../types';

/**
 * Main application controller that orchestrates all modules
 */
export class AppController implements Initializable {
    private static instance: AppController;
    
    private dataManager: DataManager;
    private eventManager: EventManager;
    private appState: AppState;
    private isInitialized: boolean = false;

    private constructor() {
        this.dataManager = DataManager.getInstance();
        this.eventManager = EventManager.getInstance();
        this.appState = {
            currentView: 'forum',
            selectedButton: 0,
            isMobile: DeviceConfig.isMobile(),
            isInitialized: false
        };
    }

    public static getInstance(): AppController {
        if (!AppController.instance) {
            AppController.instance = new AppController();
        }
        return AppController.instance;
    }

    /**
     * Initialize the application
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        try {
            this.setupHeaderExtension();
            this.isInitialized = true;
            this.appState.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize AppController:', error);
            throw error;
        }
    }

    public isInitialized(): boolean {
        return this.isInitialized;
    }

    /**
     * Setup the header extension
     */
    private setupHeaderExtension(): void {
        extend(HeaderPrimary.prototype, 'view', (vnode: Vnode<any, HeaderPrimary>) => {
            const routeName = app.current.get('routeName');

            if (routeName === "tags") {
                this.attachAdvertiseHeader(vnode);
            }
        });
    }

    /**
     * Attach the advertise header functionality
     */
    private attachAdvertiseHeader(vdom: Vnode<any>): void {
        this.eventManager.setupMobileAdjustments();

        const task = setInterval(() => {
            if (vdom.dom) {
                clearInterval(task);
                this.initializeMainComponents(vdom);
            }
        }, AppConfig.CHECK_TIME);
    }

    /**
     * Initialize main components
     */
    private async initializeMainComponents(vdom: Vnode<any>): Promise<void> {
        try {
            // Check if already initialized
            if (document.getElementById(AppConfig.CONTAINER_IDS.SWIPER_AD_CONTAINER)) {
                return;
            }

            // Create and attach advertisement swiper
            const adSwiper = AdSwiperComponent.create();
            $(AppConfig.SELECTORS.CONTENT_CONTAINER).prepend(adSwiper);
            SwiperManager.createAdSwiper();

            // Load all data
            await this.dataManager.loadAllData();

            // Wait for data to be loaded
            this.waitForDataAndInitialize();

        } catch (error) {
            console.error('Error initializing main components:', error);
        }
    }

    /**
     * Wait for data to load and initialize components
     */
    private waitForDataAndInitialize(): void {
        const checkDataTask = setInterval(() => {
            if (this.dataManager.isAllDataLoaded()) {
                clearInterval(checkDataTask);
                this.initializeDataDependentComponents();
            }
        }, AppConfig.DATA_CHECK_INTERVAL);
    }

    /**
     * Initialize components that depend on loaded data
     */
    private initializeDataDependentComponents(): void {
        try {
            if (document.getElementById(AppConfig.CONTAINER_IDS.SWIPER_TAG_CONTAINER)) {
                return;
            }

            this.createCategoryLayout();
            this.createContainers();
            this.createButtonNavigation();
            this.setupEventHandlers();
            this.setupMiscellaneousFeatures();

        } catch (error) {
            console.error('Error initializing data-dependent components:', error);
        }
    }

    /**
     * Create category layout with tag swiper
     */
    private createCategoryLayout(): void {
        const tagSwiper = TagSwiperComponent.create();
        $(AppConfig.SELECTORS.TAGS_PAGE_CONTENT).prepend(tagSwiper);
        
        TagSwiperComponent.addTextContent(tagSwiper);
        SwiperManager.createTagSwiper();

        // Add Tronscan component
        this.addTronscanComponent(tagSwiper);

        // Remove original tag tiles and apply mobile styles
        $(AppConfig.SELECTORS.TAG_TILES).remove();
        this.applyMobileStyles();
    }

    /**
     * Add Tronscan component to the layout
     */
    private addTronscanComponent(parentContainer: HTMLDivElement): void {
        const tronscanData = this.dataManager.getTronscanService().getData();
        if (tronscanData && tronscanData.length > 0) {
            const tronscanSwiper = TronscanSwiperComponent.create(tronscanData);
            $(parentContainer).append(tronscanSwiper);
            SwiperManager.createTronscanSwiper();
        }
    }

    /**
     * Create all container components
     */
    private createContainers(): void {
        const tagsPageContent = $(AppConfig.SELECTORS.TAGS_PAGE_CONTENT);
        
        // Create containers
        const zhiboContainer = ContainerComponents.createZhiboContainer();
        const youxiContainer = ContainerComponents.createYouXiContainer();
        const buttonCustomizationContainer = ContainerComponents.createButtonCustomizationContainer();
        const shangchengContainer = ContainerComponents.createShangChengContainer();

        // Append containers
        tagsPageContent.prepend(zhiboContainer);
        tagsPageContent.prepend(youxiContainer);
        tagsPageContent.prepend(buttonCustomizationContainer);
        tagsPageContent.prepend(shangchengContainer);
    }

    /**
     * Create button navigation
     */
    private createButtonNavigation(): void {
        const buttonsCustomizationData = this.dataManager.getButtonsCustomizationService().getData() || [];
        const buttonNavigation = ButtonNavigationComponent.create(buttonsCustomizationData);
        
        $(AppConfig.SELECTORS.TAGS_PAGE_CONTENT).prepend(buttonNavigation);
    }

    /**
     * Setup event handlers
     */
    private setupEventHandlers(): void {
        const buttonsCustomizationData = this.dataManager.getButtonsCustomizationService().getData() || [];
        const { buttonsCustomizationMap, leftValueMap, totalButtons } = this.calculateButtonMappings(buttonsCustomizationData);

        this.eventManager.setupButtonNavigation(buttonsCustomizationMap, leftValueMap, totalButtons);
        this.eventManager.setupZhiboNavigation();
    }

    /**
     * Calculate button mappings for navigation
     */
    private calculateButtonMappings(buttonsCustomizationData: any[]) {
        const responsive = DeviceConfig.getResponsiveConfig();
        const buttonsCustomizationMap: ButtonsCustomizationMap = {};
        const leftValueMap: LeftValueMap = {};
        let totalButtons = AppConfig.DEFAULT_BUTTON_COUNT;

        // Map custom buttons
        buttonsCustomizationData.forEach(data => {
            totalButtons++;
            buttonsCustomizationMap[totalButtons] = { url: data.url() };
        });

        // Calculate left positions for button selection indicator
        let leftValuePrev = 0;
        leftValueMap[0] = 0;

        for (let i = 0; i <= totalButtons; i++) {
            const buttonElement = $(`#client1HeaderButton${i}`);
            const leftValue = buttonElement.outerWidth();

            if (i === 1 || i === 2) {
                continue;
            }

            if (i === 0 && leftValue) {
                const buttonSelectedBackground = $(`#${AppConfig.CONTAINER_IDS.BUTTON_SELECTED_BACKGROUND}`);
                buttonSelectedBackground.width(leftValue);
            }

            if (leftValue) {
                leftValueMap[i + 1] = leftValue + leftValuePrev - responsive.leftModifier;
                leftValuePrev += leftValue;
            }
        }

        return { buttonsCustomizationMap, leftValueMap, totalButtons };
    }

    /**
     * Setup miscellaneous features
     */
    private setupMiscellaneousFeatures(): void {
        this.eventManager.setupLeaderboardPosition();

        if (!app.session.user) {
            this.addHeaderIcon();
        }
    }

    /**
     * Add header icon
     */
    private addHeaderIcon(): void {
        const headerIcon = ContainerComponents.createHeaderIcon();
        $(AppConfig.SELECTORS.APP_BACK_CONTROL).prepend(headerIcon);
    }

    /**
     * Apply mobile-specific styles
     */
    private applyMobileStyles(): void {
        if (this.appState.isMobile) {
            $(AppConfig.SELECTORS.APP).css("overflow-x", "hidden");
            $(AppConfig.SELECTORS.APP_CONTENT).css({
                "min-height": "auto",
                "background": ""
            });
        }
    }

    /**
     * Get current application state
     */
    public getAppState(): AppState {
        return { ...this.appState };
    }

    /**
     * Update application state
     */
    public updateAppState(updates: Partial<AppState>): void {
        this.appState = { ...this.appState, ...updates };
    }

    /**
     * Cleanup resources
     */
    public dispose(): void {
        this.eventManager.offAll();
        SwiperManager.destroyAll();
        this.isInitialized = false;
        this.appState.isInitialized = false;
    }

    /**
     * Restart the application
     */
    public async restart(): Promise<void> {
        this.dispose();
        await this.initialize();
    }
}

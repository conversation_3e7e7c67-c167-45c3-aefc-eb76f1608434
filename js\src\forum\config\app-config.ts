import app from 'flarum/forum/app';

/**
 * Application configuration constants
 */
export class AppConfig {
    // Timing constants
    public static readonly CHECK_TIME = 10;
    public static readonly DEFAULT_TRANSITION_TIME = 5000;
    public static readonly DATA_CHECK_INTERVAL = 100;
    public static readonly ZHIBO_REFRESH_DELAY = 100;

    // UI constants
    public static readonly MOBILE_SPACE_BETWEEN = 90;
    public static readonly DESKTOP_SPACE_BETWEEN = 10;
    public static readonly MOBILE_SLIDES_PER_VIEW = 2;
    public static readonly DESKTOP_SLIDES_PER_VIEW = 7;
    public static readonly TRONSCAN_MOBILE_SLIDES = 4;
    public static readonly TRONSCAN_DESKTOP_SLIDES = 7;
    public static readonly TRONSCAN_MOBILE_SPACE = 80;

    // Extension settings keys
    public static readonly EXTENSION_PREFIX = 'wusong8899-client1-header-adv';
    public static readonly TRANSLATION_PREFIX = 'wusong8899-client1';

    // Button configuration
    public static readonly DEFAULT_BUTTON_COUNT = 3;
    public static readonly MAX_LINK_IMAGE_PAIRS = 30;

    // CSS selectors
    public static readonly SELECTORS = {
        APP: '#app',
        APP_NAVIGATION: '#app-navigation',
        APP_CONTENT: '.App-content',
        CONTENT_CONTAINER: '#content .container',
        TAGS_PAGE_CONTENT: '.TagsPage-content',
        TAG_TILES: '.TagTiles',
        TAG_TILE: '.TagTile',
        ITEM_NEW_DISCUSSION: '.item-newDiscussion',
        ITEM_NAV: '.item-nav',
        ITEM_MONEY_LEADERBOARD: '.item-MoneyLeaderboard',
        ITEM_FORUM_CHECKIN: '.item-forum-checkin',
        APP_BACK_CONTROL: '.App-backControl'
    } as const;

    // Container IDs
    public static readonly CONTAINER_IDS = {
        SWIPER_TAG_CONTAINER: 'swiperTagContainer',
        SWIPER_AD_CONTAINER: 'swiperAdContainer',
        TRONSCAN_TEXT_CONTAINER: 'TronscanTextContainer',
        SELECT_TITLE_CONTAINER: 'selectTitleContainer',
        HEADER_ICON: 'wusong8899Client1HeaderIcon',
        ZHIBO_IFRAME: 'zhiboIframe',
        CUSTOM_BUTTON_IFRAME: 'customButtonIframe',
        BUTTON_SELECTED_BACKGROUND: 'buttonSelectedBackground',
        LINKS_QUEUE_REFRESH: 'linksQueueRefresh',
        LINKS_QUEUE_PREV: 'linksQueuePrev',
        LINKS_QUEUE_NEXT: 'linksQueueNext'
    } as const;

    // CSS classes
    public static readonly CSS_CLASSES = {
        SWIPER_TAG_CONTAINER: 'swiperTagContainer',
        SWIPER_AD_CONTAINER: 'swiperAdContainer',
        TAG_SWIPER: 'swiper tagSwiper',
        AD_SWIPER: 'swiper adSwiper',
        TRONSCAN_SWIPER: 'swiper tronscanSwiper',
        ZHIBO_CONTAINER: 'zhiboContainer',
        YOUXI_CONTAINER: 'youxiContainer',
        SHANGCHENG_CONTAINER: 'shangchengContainer',
        BUTTON_CUSTOMIZATION_CONTAINER: 'buttonCustomizationContainer',
        SELECT_TITLE_CONTAINER: 'selectTitleContainer',
        U_BTN: 'u-btn',
        SWIPER_SLIDE_TAG: 'swiper-slide swiper-slide-tag',
        SWIPER_SLIDE_TAG_INNER: 'swiper-slide-tag-inner',
        SWIPER_SLIDE_TAG_INNER_MOBILE: 'swiper-slide-tag-inner-mobile'
    } as const;
}

/**
 * Settings configuration manager
 */
export class SettingsConfig {
    /**
     * Get transition time from forum settings
     */
    public static getTransitionTime(): number {
        const transitionTime = app.forum.attribute('Client1HeaderAdvTransitionTime');
        return transitionTime ? Number(transitionTime) : AppConfig.DEFAULT_TRANSITION_TIME;
    }

    /**
     * Get image source for a specific index
     */
    public static getImageSrc(index: number): string | null {
        return app.forum.attribute(`Client1HeaderAdvImage${index}`) || null;
    }

    /**
     * Get link for a specific index
     */
    public static getImageLink(index: number): string | null {
        return app.forum.attribute(`Client1HeaderAdvLink${index}`) || null;
    }

    /**
     * Get all configured image/link pairs
     */
    public static getImageLinkPairs(): Array<{ src: string; link: string; index: number }> {
        const pairs: Array<{ src: string; link: string; index: number }> = [];
        
        for (let i = 1; i <= AppConfig.MAX_LINK_IMAGE_PAIRS; i++) {
            const src = this.getImageSrc(i);
            const link = this.getImageLink(i);
            
            if (src) {
                pairs.push({
                    src,
                    link: link || '#',
                    index: i
                });
            }
        }
        
        return pairs;
    }
}

/**
 * Device detection and responsive configuration
 */
export class DeviceConfig {
    private static _isMobile: boolean | null = null;

    /**
     * Check if the current device is mobile
     */
    public static isMobile(): boolean {
        if (this._isMobile === null) {
            this._isMobile = this.mobileCheck();
        }
        return this._isMobile;
    }

    /**
     * Mobile device detection
     */
    private static mobileCheck(): boolean {
        let check = false;
        const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
        
        if (userAgent) {
            const mobileRegex = /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;
            const mobileRegex2 = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;
            
            check = mobileRegex.test(userAgent) || mobileRegex2.test(userAgent.substr(0, 4));
        }
        
        return check;
    }

    /**
     * Get responsive configuration based on device type
     */
    public static getResponsiveConfig() {
        const isMobile = this.isMobile();
        
        return {
            isMobile,
            spaceBetween: isMobile ? AppConfig.MOBILE_SPACE_BETWEEN : AppConfig.DESKTOP_SPACE_BETWEEN,
            slidesPerView: isMobile ? AppConfig.MOBILE_SLIDES_PER_VIEW : AppConfig.DESKTOP_SLIDES_PER_VIEW,
            tronscanSlidesPerView: isMobile ? AppConfig.TRONSCAN_MOBILE_SLIDES : AppConfig.TRONSCAN_DESKTOP_SLIDES,
            tronscanSpaceBetween: isMobile ? AppConfig.TRONSCAN_MOBILE_SPACE : AppConfig.DESKTOP_SPACE_BETWEEN,
            eventType: isMobile ? 'touchend' : 'click',
            leftModifier: isMobile ? 3 : 0
        };
    }
}
